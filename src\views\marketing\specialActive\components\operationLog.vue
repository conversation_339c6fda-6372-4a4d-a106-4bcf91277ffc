<template>
  <el-dialog
    :visible.sync="visible"
    title="操作日志"
    width="70%"
  >
  <xyy-panel :titleShow="false">
    <div class="table-box">
        <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
        :show-overflow="false" :key="tableKey" empty-text="暂未查询到匹配的记录">
            <template>
                <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                    :title="item.title" :min-width="item.width" :fixed="item.fixed">
                    <template v-slot:default="slotProps">

                        <div v-if="item.field === 'operation1'">
                            <span class="clickWriting" style="margin-right: 10px;">修改</span>
                            <span class="clickWriting" style="margin-right: 10px;" @click="viewHandler(slotProps.row)">查看</span>
                              <span class="clickWriting" style="margin-right: 10px;" @click="operationLogHandler(slotProps.row)">活动变更日志</span>
                        </div>
                        <div v-else-if="item.field === 'content'">
                          <div v-for="(item,index) in slotProps.row.changeInfo" :key="index" style="text-align: left;">{{ item }}</div>
                        </div>
                        <div v-else>
                            {{ slotProps.row[item.field] }}
                        </div>
                    </template>
                </vxe-table-column>
            </template>
        </vxe-table>
    </div>
  </xyy-panel>
  </el-dialog>

</template>
<script>
import { getSpecialLog } from "@/api/marketing/specialActive";
import { pageConfig }  from "@/utils";
import { operationLogColumns } from "../config";
export default {
    name: 'OperationLog',

    props: {
    },
    data() {
        return {
          visible:false,
          tableKey:0,
          tableData:[],
          tablePage: pageConfig(),
          tableColumns:operationLogColumns(),
          loading:false,
          row:{}
        }
    },
    methods: {
      open(row) {
        this.row = row
        this.visible = true
        this.getList()
      },
      async getList() {
        this.loading = true
        try{
          const res = await getSpecialLog({promotionId:this.row.promotionId})
          if(res.code === 0){
            this.tableData = res.result
          }else{
            this.$message.error(res.msg)

          }

        } catch(e) {
          console.log(e)
        } finally {
          this.loading = false
          this.tableKey ++
        }
        this.$nextTick(() => {
          this.$refs.xTable.recalculate(true)
        })
      },
      close() {
        this.tableData = []
        this.visible = false
      },
       // 分页器改变处理
       handlePageChange({currentPage, pageSize}) {
          this.tablePage.pageNum = currentPage;
          this.tablePage.pageSize = pageSize;

        },
    }
}
</script>

<style scoped lang="scss">
::v-deep .vxe-table--body-wrapper{
    height:500px !important;
}
</style>
