<template>
  <xyy-dialog
    class="abow_dialog"
    ref="districtDialog"
    :title="title"
    :width="dialogWidth"
    @on-close="handleDialogClose"
  >
    <xyy-panel :titleShow="false">
      <el-form
        ref="ruleForm"
        label-width="100px"
        :model="ruleForm"
        :rules="rules"
        class="demo-ruleForm"
      >
        <el-form-item label="商圈名称:" prop="busAreaName">
          <el-input
            maxlength="20"
            style="width: 300px; margin-left: 10px"
            v-model="ruleForm.busAreaName"
            placeholder="选择商品售卖范围，不超过20个汉字"
          ></el-input>
        </el-form-item>
        <el-form-item label="商圈描述:" prop="busAreaDesc">
          <el-input
            maxlength="100"
            style="width: 300px; margin-left: 10px"
            v-model="ruleForm.busAreaDesc"
            placeholder="不超过100个汉字"
          ></el-input>
        </el-form-item>
        <el-form-item label="商圈类型:" prop="region">
          <el-select
            style="width: 300px; margin-left: 10px"
            @change="typeChange"
            v-model="ruleForm.busType"
          >
            <el-option
              v-for="item in bussinessType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-tooltip class="item" effect="dark" placement="top">
            <template #content>
              <span>
                勾选不同商圈类型，勾选区域会发生变化，
                <br />其中药品对应商品一级分类为中西成药、养
                <br />身中药、注射用药、配方饮片，其他均为非 <br />药。请根据实际应用进行选择。
              </span>
            </template>
            <p class="span-tip">?</p>
          </el-tooltip>
        </el-form-item>
        <div class="check-container">
          <xyy-city-tree
            show-checkbox
            v-model="selectArray"
            :data="all"
            accordion
            node-key="areaCode"
            :default-expanded-keys="defaultExpandedKeys"
            :props="treeProps"
            @check="cityCheckChange"
          ></xyy-city-tree>
        </div>
        <el-row :span="6" :offset="18">
          <div style="float: right; margin-top: 10px">
            <el-button size="small" @click="cancelBtn">取 消</el-button>
            <el-button type="primary" size="small" v-if="title === '新增业务弹窗'" @click="sureAddBtn">确 定</el-button>
            <el-button type="primary" size="small" v-else @click="sureEditBtn">确 定</el-button>
          </div>
        </el-row>
      </el-form>
    </xyy-panel>
  </xyy-dialog>
</template>

<script>
import XyyCityTree from '@/components/xyy/xyy-city-tree/index'
import {add,edit,querySaleableArea}   from '@/api/product/businessManagement'
const DEFAULT_ALL_CODE = 0
export default {
  name: 'newBussiessDistrict',
  components: { XyyCityTree },
  props: {
    dialogVisible: Boolean,
    row: Object
  },
  mounted() {
    this.$nextTick(() => {
      this.visible = true
    })
  },
  data() {
    return {
      selectArray: [],
      selectCodes: [],
      defaultExpandedKeys: [DEFAULT_ALL_CODE],
      treeProps: {
        label: 'areaName',
        children: 'children',
        isLeaf: (data, node) => {
          return node.level === 4
        }
      },
      checkAreas: [], // 选中区域
      all: [],
      show: false,
      isLoading: false,
      dialogWidth: '80%',
      formData: {
        ERPCode: '',
        productName: ''
      },
      title: '',
      ruleForm: {
        busAreaName: '', // 商圈名称
        busType: '', // 商圈类型
        busAreaDesc: '' // 商圈描述
      },
      // 商圈类型下拉框
      bussinessType: [
        {
          value: 3,
          label: '药品和非药'
        },
        {
          value: 1,
          label: '药品'
        },
        {
          value: 2,
          label: '非药'
        }
      ],
      rules: {
        busAreaName: [
          { required: true, message: '请输入商圈名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度限制为1至20个汉字', trigger: 'blur' }
        ]
      },
      busAreaName: '', // 记录初始化商品名称的值
      dialogVisibl: false,
      tableData: [], // 弹窗表格
      rowIndex: '',
      orgId: '', // 编辑行id
      tablePage: {
        // 分页
        pageNum: 1,
        pageSize: 100,
        total: 0
      }
    }
  },
  methods: {
    handleDialogClose() {
      this.busAreaName = '' // 初始化商品名称的值
      this.ruleForm.busAreaName = ''
      this.ruleForm.busType = ''
      this.ruleForm.busAreaDesc = '';
      this.selectArray = [];
      this.checkAreas = [];
      // DEFAULT_ALL_CODE = 0;
      this.$emit('onDialogChange')
    },
    cityCheckChange(node) {
      this.checkAreas = node
    },
    open() {
      this.ruleForm.busType = 3
      this.$nextTick(()=>{
        if (this.row) {
          this.title = '编辑业务弹窗'
          this.busAreaName = this.row.busAreaName // 初始化商品名称的值
          this.ruleForm.busAreaName = this.row.busAreaName
          this.ruleForm.busType = this.row.busType ? this.row.busType : 3
          this.ruleForm.busAreaDesc = this.row.busAreaDesc
          if (this.row.areas && this.row.areas.length > 0) {
            this.selectArray = this.row.areas.map((item) => item.areaCode)
            this.checkAreas = this.row.areas
          }
        } else {
          this.title = '新增业务弹窗'
        }
        this.searchList()
        this.$refs.districtDialog.open()
      })
    },
    // 新增确定
    sureAddBtn() {
      // 判断商圈名称是否重复
      this.duplicatName()
    },

    // 校验商圈名称是否重复
    duplicatName() {
      if (this.ruleForm.busAreaName === '') {
        this.$message.warning('请填写商圈名称')
        return
      }
      if (this.checkAreas.length === 0) {
        this.$message.warning('请选择区域')
        return
      }

      if (new RegExp(/^[0-9]+$/g).test(this.ruleForm.busAreaName)) {
        this.$message.warning('商圈名称不能全为数字')
        return
      }
      if (new RegExp(/^[a-zA-Z]+$/g).test(this.ruleForm.busAreaName)) {
        this.$message.warning('商圈名称不能全为字母')
        return
      }
      const tempRegex = new RegExp(/^.*[\W_].*$/g)
      const tempStr = this.ruleForm.busAreaName.replace(/[\u4e00-\u9fa5]/g, '')
      if (tempRegex.test(tempStr)) {
        this.$message.warning('商圈名称不能包含特殊字符')
        return
      }
      const parms = {
        busAreaName: this.ruleForm.busAreaName
      }
      if (this.title === '新增业务弹窗') {
        this.addSave()
      } else if (this.title === '编辑业务弹窗') {
        this.editSave()
      }
      // 判断编辑的时候商圈名称是否更改
      // if (this.ruleForm.busAreaName === this.busAreaName) {
      //   if (this.title === '新增业务弹窗') {
      //     this.addSave()
      //   } else if (this.title === '编辑业务弹窗') {
      //     this.editSave()
      //   }
      // } else {
      //   if (this.title === '新增业务弹窗') {
      //     this.addSave()
      //   } else if (this.title === '编辑业务弹窗') {
      //     this.editSave()
      //   }
      //   // getCheckBusAreaName(parms).then((res) => {
      //   //   const { code, data } = res
      //   //   if (code === 0) {
      //   //     if (data === true) {
      //   //       this.$message.error('商圈名称重复')
      //   //     } else if (data === false) {
      //   //       if (this.title === '新增业务弹窗') {
      //   //         this.addSave()
      //   //       } else if (this.title === '编辑业务弹窗') {
      //   //         this.editSave()
      //   //       }
      //   //     }
      //   //   }
      //   // })
      // }
    },
    // 新增保存
    async addSave() {
      this.checkAreas.forEach((item, index) => {
        if (item.areaName === '全选') {
          this.checkAreas.splice(index, 1)
        }
      })
      const params = {
        areas: this.checkAreas
      }
      Object.assign(params, this.ruleForm)
      try{
        const res = await add(params);
        if (res.code === 0) {
          if (res.result === true) {
            this.$message.success('提交成功')
            this.$refs.districtDialog.close();
          } else {
            this.$message.error('提交失败')
          }
        }else{
          this.$message.error(res.msg)
        }
      }catch(error){

      }
    },
    // 编辑
    sureEditBtn() {
      this.duplicatName()
    },

    // 编辑保存
    async editSave() {
      this.checkAreas.forEach((item, index) => {
        if (item.areaName === '全选') {
          this.checkAreas.splice(index, 1)
        }
      })
      const parmas = {
        busId: this.row.id,
        areas: this.checkAreas
      }
      Object.assign(parmas, this.ruleForm);
      try{
        const res = await edit(parmas);
        if (res.code === 0) {
          if (res.result === true) {
            this.$message.success('提交成功')
            this.$refs.districtDialog.close();
          } else {
            this.$message.error('提交失败')
          }
        }else{
          this.$message.error(res.msg)
        }
      }catch(error){

      }
    },
    // 取消
    cancelBtn() {
      this.$refs.districtDialog.close();
    },
    // 类型切换
    typeChange() {
      this.checkAreas = []
      this.selectArray = []
      this.searchList()
    },
    // 查询
    async searchList() {
      let parmas = {
        busType: this.ruleForm.busType
      }
      if (this.row) {
        this.orgId = this.row.id
        parmas = {
          busType: this.ruleForm.busType,
          orgId: this.orgId
        }
      } else if (this.title === '新增业务弹窗') {
        parmas = {
          busType: this.ruleForm.busType
        }
      }
      try{
        const res = await querySaleableArea();
        if (res.code === 0) {
          if (res.result.length === 0) {
            this.all = []
            this.$message.warning('企业当前药品可售区域为空，请检查企业信息')
          } else {
            this.all = []
            const selectAll = {
              areaName: '全选',
              areaCode: DEFAULT_ALL_CODE,
              children: res.result
            }
            this.all.push(selectAll)
          }
        }
      }catch{
        this.$message.warning('查询失败')
      }
    }
  }
}
</script>

<style style="scss" scoped>
.span-tip {
  display: inline-block;
  width: 20px;
  height: 20px;
  font-size: 14px;
  border: 1px solid #4183d5;
  color: #4183d5;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  margin-left: 10px;
}

.el-form-item__label {
  margin-left: 0px;
}
::v-deep.el-dialog__header {
  background-color: #f8f8ff;
}
/* .tooltip-class.el-tooltip__popper.is-light {
  background: #ffffff;
  border: 1px solid #fff;
} */
.xyyCityTree {
  max-height: 300px;
  overflow: auto;
}
.check-container {
  max-height: 200px;
  min-height: 200px;
  overflow-y: auto;
  border: 1px solid #ebebeb;
  margin-left: 0px;
  margin-top: 10px;
  padding: 10px;
}
</style>
