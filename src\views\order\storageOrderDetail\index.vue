<template>
<div class="app-container">
  <xyy-panel title="查询条件">
     <!-- 按钮组 start-->
     <btn-group slot="tools" :btn-list="btnListTop"/>
     <el-form ref="formData" :model="formData" label-width="120px" class="clearfix">
      <el-row :gutter="10">
        <el-col :lg="6" :md="6">
          <el-form-item label="供货单号" prop="supplyCode">
            <el-input v-model.trim="formData.supplyCode" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="入库单号" prop="storageCode">
            <el-input v-model.trim="formData.storageCode" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12">
          <el-form-item label="订单下单时间" prop="SubmissionTime">
            <el-date-picker
              v-model="formData.SubmissionTime"
              value-format="yyyy-MM-dd"
              type="daterange"
              :picker-options="pickerOptions"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :lg="6" :md="6">
            <el-form-item label="商品信息" prop="productInfo">
              <el-input v-model.trim="formData.productInfo" placeholder="商品名称/本公司商品编码/商品编码/产品编码"></el-input>
            </el-form-item>
          </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="批号" prop="batchCode">
            <el-input v-model.trim="formData.batchCode" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
     </el-form>
  </xyy-panel>
  <xyy-panel :titleShow='false' :bottom="10">
    <div style="display: flex;justify-content: space-between;align-items: center;">
       <el-tabs v-model="activeName">
         <el-tab-pane name="planModel">
           <div slot="label">
             <span>供货入库单明细</span>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- 按钮组 start-->
      <btn-group slot="tools" :btn-list="btnListTabale" style="float:right"/>
    </div>
    <div :key="tabKey" class="table-box">
        <vxe-table ref="xTable"
          :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize,}"
          :loading="loading"
          height="auto"
          :key="tableKey"
          :row-style="rowStyle"
          :data="tableData"
          empty-text="暂未查询到匹配的记录"
          >
          <vxe-table-column type="seq" title="序号" width="80" />
          <!-- <vxe-table-column type="checkbox" width="60"></vxe-table-column> -->
          <template>
            <vxe-table-column v-for="item in tableColumn" :key="item.field" :field="item.field" :title="item.title" :width="item.width" :fixed="item.fixed">
              <template v-slot:default="slotProps">
                <div>
                  {{ slotProps.row[item.field] }}
                </div>
              </template>
            </vxe-table-column>
          </template>
        </vxe-table>
      </div>
      <div class="pager" >
        <vxe-pager
        border
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :page-sizes="tablePage.pageSizes"
        :total="tablePage.total"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total',
        ]"
        @page-change="handlePageChange"
      />
      </div>
  </xyy-panel>
</div>
</template>
<script>
import { exportFile } from '@/api/system/exportCenter'
import utils from "@/utils";
import { orderDetailCol } from "./config";
import{getStoreOrderList} from "@/api/order/storeOrderDetail"
import XEUtils from 'xe-utils'
import { pageConfig } from '@/utils';
const end = new Date();
const start = XEUtils.getWhatDay(end, -30); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd'); // 将结束日期格式化为字符串
export default {
  name: "OrderStorageDetail",
  data() {
    return {
      activeName: 'planModel',
      btnListTop: [
       {
         label: "查询",
          type: "primary",
          clickEvent: this.searchList,
          code: "",
          plain: 'false',
          permission:'storageOrderDetail:list:query'
        },
        {
            label: "重置",
            clickEvent: this.reset,
            code: "",
            plain: 'false',
            permission:'storageOrderDetail:list:reset'
        },
       ],
       btnListTabale: [
       {
         label: "导出",
          clickEvent: this.exprotHandler,
          code: "",
          plain: 'false',
          permission:'storageOrderDetail:list:export'
        }
       ],
       formData:{
        SubmissionTime: [defaultBeginTime, defaultEndTime], // 提交时间
        productInfo:"", //商品信息
        supplyCode:'', //供货单号
        batchCode:"", //批号
        storageCode:'', //入库单号

       },
      tabKey: 0,
      loading:false,
      tablePage: pageConfig(),
      tableKey:0,
      tableData:[],
      tableColumn: orderDetailCol(),
       // 时间限制
       pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
         }
      },
      queryFlag: 0, // 路由跳转查询标识
    }
  },
  mounted() {
    // alert(11)
    this.routeQuery()
  },

  activated() {
    // alert(22)
    this.$nextTick(()=>{
      this.routeQuery()
        this.tabKey++
    })
  },
  methods: {
    routeQuery(){
      if(this.$route.query.storageCode){
          this.$set(this.formData,'storageCode',this.$route.query.storageCode)
          this.$set(this.formData, 'SubmissionTime', null)
          this.$set(this.formData, 'productInfo', '')
          this.$set(this.formData, 'supplyCode', '')
          this.$set(this.formData, 'batchCode', '')
          const newQuery = { ...this.$route.query };
          delete newQuery.storageCode;
          this.$router.replace({ query: newQuery });
      }
      this.searchList();
    },
    // 动态设置行高
    rowStyle({ row, rowIndex }) {
      return {  }; // 统一设置行高为 60px
    },
     // 分页器改变处理
     handlePageChange({currentPage, pageSize}){
      this.tablePage.pageNum = currentPage;
      this.tablePage.pageSize = pageSize;
      this.searchList(true);
    },
    // 导出
    exprotHandler() {

      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }
      const formInfo = {
        productInfo: this.formData.productInfo,
        supplyCode: this.formData.supplyCode,
        batchCode: this.formData.batchCode,
        storageCode: this.formData.storageCode,
        startTime: this.formData.SubmissionTime ? `${this.formData.SubmissionTime[0]} 00:00:00` : '',
        endTime: this.formData.SubmissionTime ? `${this.formData.SubmissionTime[1]} 23:59:59` : '',
        pageNum: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
      }
      const params = {
        taskBean:'SUPPLY_STORAGE_PRODUCT_REMOTE_LISTSUPPLYORDER',
        colNameDesc: this.tableColumn.filter(item => item.field != 'operation1').map(item => item.title).join(','),
        colName: this.tableColumn.filter(item => item.field != 'operation1').map(item => item.field).join(','),
        moduleName: 'SUPPLY_ORDER',
        menuDesc: '供货入库单明细',
        exportParams: JSON.stringify(formInfo)
      }
      this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
    },
    // 查询
    searchList(flag) {
      this.loading = true;
      if(flag !== true){
        this.tablePage.pageNum = 1;
      }
      const params = {
        pageNum: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
        productInfo: this.formData.productInfo,
        supplyCode: this.formData.supplyCode,
        batchCode: this.formData.batchCode,
        storageCode: this.formData.storageCode,
        startTime: this.formData.SubmissionTime ? this.formData.SubmissionTime[0] + ' 00:00:00' : '',
        endTime: this.formData.SubmissionTime ? this.formData.SubmissionTime[1] + ' 23:59:59' : '',
      }
      getStoreOrderList(params).then(res=>{
        if(res.code === 0){
          this.tableKey++
          this.tableData = res.result.list;
          this.tablePage.total = res.result.total;
        } else {
          this.$message.error(res.msg);
        }
      }).finally(()=>{
        this.loading = false;
      })

    },
    //重置
    reset() {
      this.tablePage.pageSize= 20
      this.$refs.formData.resetFields();
      this.tablePage.pageNum = 1;
      this.tablePage.pageSize = 20;
      this.searchList();

    },

  }
}
</script>
<style scoped lang="scss">
</style>
