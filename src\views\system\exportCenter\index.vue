<template>
    <div class="app-container">
        <xyy-panel title="刷新条件">
            <btn-group slot="tools" :btn-list="btnList" />
            <!-- 查询条件 start -->
            <el-form ref="formData" :model="formData" label-width="90px" class="clearfix">
                <el-row>
                    <el-col :lg="8" :md="8">
                        <el-form-item label="模块名称">
                            <el-select v-model="formData.moduleName" placeholder="请选择" @change="changeMenu" clearable>
                                <el-option value="" label="全部"></el-option>
                                <el-option v-for="item in formData.moduleAndTypeList" :key="item.value"
                                    :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="8" :md="8">
                        <el-form-item label="菜单名称">
                            <el-select v-model="formData.menuDesc" placeholder="请选择" @focus="changeMenuDesc" clearable>
                                <el-option v-for="item in formData.menuDescList" :key="item" :label="item"
                                    :value="item" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :lg="8" :md="8">
                        <el-form-item label="开始日期">
                            <el-date-picker v-model="formData.gteCreateTimeStr" type="datetime" placeholder="选择日期时间"
                                default-time="00:00:00">
                                >
                            </el-date-picker>
                        </el-form-item>
                    </el-col> -->
                    <el-col :lg="8" :md="8">
                        <el-form-item label="下载状态">
                            <el-select v-model="formData.taskStatus" placeholder="请选择" clearable>
                                <el-option value="" label="全部"></el-option>
                                <el-option v-for="item in formData.taskStatusList" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- <el-row>
                </el-row> -->
            </el-form>
            <!-- 查询条件 -->
        </xyy-panel>
        <xyy-panel title="下载中心列表">
            <div class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    border="full">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template v-for="item in tableColumns">
                        <vxe-table-column v-if="item.visible" :key="item.field" :field="item.field" :title="item.title"
                            :min-width="item.width">
                            <template #default="{ row }">
                                <div v-if="item.field === 'operate'">
                                    <!-- <el-button type="text" :disabled="row.taskStatusStr != '执行成功'"
                                    @click="downloadFile(row)">
                                    下载
                                </el-button> -->
                                    <div v-if="row.taskStatusStr === '执行成功'" @click="downloadFile(row)"
                                        class="clickWriting">
                                        下载
                                    </div>
                                    <div v-else style="color: gray;">下载</div>
                                </div>
                                <span class="detailLink" v-else-if="item.field === 'menuDesc'">
                                    <span v-if="row.taskStatusStr === '执行成功'" @click="downloadFile(row)"
                                        class="clickWriting">{{ row.menuDesc
                                        }}</span>
                                    <span style="color: gray;" v-else>{{ row.menuDesc }}</span>
                                </span>
                                <span v-else>{{ row[item.field] }}</span>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>

            <!-- <div class="pager">
                <pagination :current-page.sync="tablePage.pageNum" :page-size.sync="tablePage.pageSize"
                    :total="tablePage.total" @current-change="handlePageChange"></pagination>
            </div> -->
        </xyy-panel>
    </div>
</template>

<script>
import { getExportList, getModuleEnum, downloadFile } from '@/api/system/exportCenter'
import XEUtils from "xe-utils";
const end = new Date();
const start = XEUtils.getWhatDay(end, -6); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, "yyyy-MM-dd"); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, "yyyy-MM-dd"); // 将结束日期格式化为字符串
import { columns } from "./config";
import utils from "@/utils";
export default {
    name: "exportCenter",
    dicts: ['module_type', 'menu_desc', 'task_module_type'],
    data() {
        return {
            remote: { labelWidth: "80px" },
            show: false,
            loading: false,
            tableData: [],
            btnList: [
                {
                    label: "刷新",
                    type: "primary",
                    // shortkey: 'F4',
                    icon: "refresh-icon",
                    clickEvent: this.getList,
                    permission: "system:export:refresh",
                },
            ],
            tablePage: {
                pageNum: 1,
                pageSize: 20,
                total: 0,
            },
            tableColumns: columns(),
            formData: {
                // SubmissionTime: [defaultBeginTime, defaultEndTime], // 提交时间
                // gteCreateTimeStr: `${defaultEndTime}T00:00:00`,
                taskStatus: "",
                taskStatusList: [
                    {
                        label: "已提交",
                        value: "1",
                    },
                    {
                        label: "执行中",
                        value: "2",
                    },
                    {
                        label: "执行成功",
                        value: "3",
                    },
                    {
                        label: "执行失败",
                        value: "4",
                    },
                ],
                moduleName: "",
                moduleAndTypeList: [],
                menuDesc: "",
                menuDescList: [],
            },
            dataInfo: [],
            ModuleEnum: {},
            // 时间限制
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
        };
    },
    activted() {
        this.$nextTick(() => {
            utils.pageActivated()
        })
        this.getList();
    },
    mounted() {
        this.$nextTick(() => {
            utils.pageActivated()
        })
        this.apiGetModuleEnum()
        this.getList();
    },
    methods: {
        apiGetModuleEnum() {
            getModuleEnum().then(res => {
                const transEnum = this.dict.type.task_module_type
                const { code, msg, result } = res
                if (code === 0) {
                    this.ModuleEnum = result
                    let data = [];
                    Object.keys(result).forEach(item => {
                        if (transEnum.find(item1 => item1.value === item)) {
                            data.push({
                                label: transEnum.find(item1 => item1.value === item).label,
                                value: item
                            })
                        } else {
                            data.push({
                                label: item,
                                value: item
                            })
                        }
                    })
                    this.formData.moduleAndTypeList = data
                }
            })
        },
        downloadFile(row) {
            // downFileByForm('/api/export/download', 'get', { taskId: row.taskId })
            const params = { taskId: row.taskId }
            downloadFile(params).then(res => {
                // 确定文件类型，这里假设是 Excel 文件
                const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                const blob = new Blob([res], { type: fileType });
                // 创建一个 URL 对象
                const url = window.URL.createObjectURL(blob);
                // 创建一个 <a> 标签
                const link = document.createElement('a');
                link.href = url;
                if (row.menuDesc === '账单') {
                    link.download = `${row.executeTimeStr}_${row.menuDesc}.xlsx`
                } else {
                    link.download = `${row.menuDesc}.xlsx`; // 设置下载的文件名，假设是 .xlsx 文件
                }
                document.body.appendChild(link);
                link.click();
                // 释放 URL 对象
                window.URL.revokeObjectURL(url);
                document.body.removeChild(link);
            })
        },
        async getList() {
            this.loading = true;
            // var dateString = this.formData.gteCreateTimeStr;
            // var date = new Date(dateString);

            // var formattedDate =
            //     date.getFullYear() +
            //     "" +
            //     ("0" + (date.getMonth() + 1)).slice(-2) +
            //     "" +
            //     ("0" + date.getDate()).slice(-2) +
            //     " " +
            //     ("0" + date.getHours()).slice(-2) +
            //     ":" +
            //     ("0" + date.getMinutes()).slice(-2) +
            //     ":" +
            //     ("0" + date.getSeconds()).slice(-2);
            // console.log(this.formData.gteCreateTimeStr);
            // console.log(formattedDate);
            // formattedDate = this.formatDate(formattedDate);
            const params = {
                // pageNo: this.tablePage.pageNum,
                // pageSize: this.tablePage.pageSize,
                moduleName: this.formData.moduleName,
                taskStatus: this.formData.taskStatus,
                // gteCreateTimeStr: formattedDate,
                menuDesc: this.formData.menuDesc,
            };
            const res = await getExportList(params);
            console.log(res);
            if (res.code === 0) {
                res.result.list.forEach(item => {
                    item.createTimeStr = XEUtils.toDateString(item.createTime, "yyyy-MM-dd HH:mm:ss");
                    item.executeTimeStr = item.executeTime ? XEUtils.toDateString(item.executeTime, "yyyy-MM-dd HH:mm:ss") : "";
                })
                this.tableData = res.result.list || [];
                this.tablePage.total = res.result.total;
                this.tablePage.pageNum = res.result.pageNum;
                this.tablePage.pageSize = res.result.pageSize;
            } else {
                this.$message.error(res.msg);
            }
            this.loading = false;
        },
        handlePageChange({ pageSize, currentPage }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList();
        },
        formatDate(dateStr) {
            // 使用字符串的slice和substring方法来分割和重组字符串
            const year = dateStr.substring(0, 4);
            const month = dateStr.substring(4, 6);
            const day = dateStr.substring(6, 8);
            const time = dateStr.substring(8); // 这部分已经是HH:mm:ss格式，所以直接保留

            // 使用模板字符串或加号(+)来拼接新的日期时间字符串
            // 并在月和日之间插入短横线(-)
            return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}${time}`;
        },
        async getTaskModuleAndType() {
            // const res = await getTaskModuleAndType();
            if (res.code === 0) {
                this.dataInfo = res.result;
                res.result.forEach((item) => {
                    this.formData.moduleAndTypeList.push({
                        desc: item.desc,
                        code: item.code,
                    });
                });
            } else {
                this.$message.error(res.msg);
            }
        },
        changeMenu(val) {
            // console.log(val);
            if (!val) {
                this.formData.menuDescList = [];
                this.formData.menuDesc = "";
                return;
            }
            this.formData.menuDescList = this.ModuleEnum[val]
        },
        changeMenuDesc(val) {
            if (this.formData.moduleAndType === "") {
                this.$message.warning("请先选择模块名称!");
                return;
            }
        },
    },
};
</script>

<style>
.detailLink {
    color: #337ab7;
    cursor: pointer;
}

.detailLink:hover {
    color: #163652;
}
</style>