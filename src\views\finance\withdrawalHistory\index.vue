<template>
    <div class="app-container">
        <xyy-panel :titleShow="false">
            <div slot="tools">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; ">
                    <div>
                        <div id="total">
                            <div id="total_content">
                                <div id="total_taxt">账户余额
                                    <el-tooltip content="结算单金额减已提现金额的剩余账号余额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${balanceAccount}` }}</div>
                            </div>
                            <div id="total_content">
                                <div id="total_taxt">发票待核销总金额
                                    <el-tooltip content="发票状态为已录入下的待核销发票金额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${verificationAmount}` }}</div>
                            </div>
                            <div id="total_content">
                                <div id="total_taxt">可提现金额
                                    <el-tooltip content="在账户余额和发票待核销总金额之间取最小金额为可提现金额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${WithdrawableAmount}` }}</div>
                            </div>
                            <div id="total_content">
                                <div id="total_taxt">本月提现</div>
                                <div id="total_aomunt">{{ `￥${monthPayouts}` }}</div>
                            </div>
                            <div id="total_content">
                                <div id="total_taxt">已提现金额</div>
                                <div id="total_aomunt">{{ `￥${withdrawnAmount}` }}</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <el-button type="primary" v-hasPermi="['finance:withdrawalHistory:requestWithdrawal']"
                            @click="requestWithdrawal">申请提现</el-button>
                    </div>
                </div>
            </div>
        </xyy-panel>
        <xyy-panel title="查询条件">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListForm" />
            <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="12" :md="12">
                        <el-form-item label="申请日期">
                            <el-date-picker v-model="formData.applicationDate" value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                :picker-options="pickerOptions" :default-time="['00:00:00', '23:59:59']" />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="12" :md="12">
                        <el-form-item label="打款日期">
                            <el-date-picker v-model="formData.payoutDate" value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                :picker-options="pickerOptions" :default-time="['00:00:00', '23:59:59']" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="提现单号">
                            <el-input v-model="formData.withdrawNo" placeholder="请输入" />
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :lg="6" :md="6">
                        <el-form-item label="提现单类型">
                            <el-select v-model="formData.withdrawType" clearable>
                                <el-option v-for="item in withdrawalTypeOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                    <el-col :lg="6" :md="6">
                        <el-form-item label="打款状态">
                            <el-select v-model="formData.payStatus" clearable>
                                <el-option v-for="item in withdrawalStatusOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="审核状态">
                            <el-select v-model="formData.approvalStatus" clearable>
                                <el-option v-for="item in auditStatusOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel title="提现记录">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTable" />
            <div class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }" :key="tableKey"
                    empty-text="暂无提现记录数据">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'operation'">
                                    <span @click="detailhandler(slotProps.row)" class="clickWriting">查看明细</span>
                                    <span v-if="slotProps.row.payStatus == 3" @click="downloadReceipt(slotProps.row)"
                                        style="margin-left: 12px;" class="clickWriting">提现回执单</span>
                                </div>
                                <div v-else-if="item.field == 'payStatusStr'">
                                    <el-tag type="warning" v-if="slotProps.row.payStatus == 0"
                                        style="font-size: 14px;">{{
                                            slotProps.row.payStatusStr }}</el-tag>
                                    <el-tag v-else-if="slotProps.row.payStatus == 1" style="font-size: 14px;">{{
                                        slotProps.row.payStatusStr }}</el-tag>
                                    <el-tag type="info" v-else-if="slotProps.row.payStatus == 2"
                                        style="font-size: 14px;">{{
                                            slotProps.row.payStatusStr }}</el-tag>
                                    <el-tag type="success" v-else-if="slotProps.row.payStatus == 3"
                                        style="font-size: 14px;">{{
                                            slotProps.row.payStatusStr }}</el-tag>
                                    <el-tag v-else-if="slotProps.row.payStatus == 4" style="font-size: 14px;">{{
                                        slotProps.row.payStatusStr }}</el-tag>
                                </div>
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
    </div>
</template>

<script>
import { getWithdrawalHistoryList, getWithdrawalHistorySummary } from "@/api/finance/withdrawalHistory";
import { pageConfig } from "@/utils";
import { tableColumns } from './config'
import XEUtils from 'xe-utils'
import { exportFile } from '@/api/system/exportCenter';
import { downloadInvoiceFile } from '@/api/finance/InvoiceLedger'
const end = new Date();
const start = XEUtils.getWhatDay(end, -30); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd 00:00:00'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd 23:59:59'); // 将结束日期格式化为字符串
export default {
    name: 'WithdrawalHistory',
    data() {
        return {
            btnListForm: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: this.searchList,
                    plain: 'false',
                    permission: "finance:withdrawalHistory:query"
                },
                {
                    label: "重置",
                    clickEvent: this.resetForm,
                    plain: 'false',
                    permission: 'finance:withdrawalHistory:reset'
                },
            ],
            btnListTable: [
                {
                    label: "导出提现记录",
                    clickEvent: this.exportTable,
                    permission: "finance:withdrawalHistory:export"
                },
                // {
                //     label: "导出提现明细",
                //     clickEvent: this.searchList,
                //     permission: "finance:withdrawalHistory:exportDetail"
                // },
            ],
            withdrawnAmount: 0, //已提现金额
            monthPayouts: 0,    //本月提现
            balanceAccount: 0,  //账户余额
            verificationAmount: 0,  //发票待核销总金额
            WithdrawableAmount: 0,  //可提现金额
            formData: {
                applicationDate: [defaultBeginTime, defaultEndTime],
                payoutDate: [],
                withdrawNo: '',
                withdrawType: 0,
                payStatus: '',
                approvalStatus:''
            },
            withdrawalTypeOptions: [
                { value: '', label: '全部' },
                { value: 0, label: '提现单' },
                { value: 1, label: '自动核销单' },
            ],
            withdrawalStatusOptions: [
                { value: '', label: '全部' },
                { value: 0, label: '待打款' },
                { value: 1, label: '打款中' },
                { value: 2, label: '打款失败' },
                { value: 3, label: '打款成功' },
                // { value: 4, label: '自动核销' },
            ],
            auditStatusOptions: [
                { value: '', label: '全部' },
                { value: 1, label: '待审核' },
                { value: 2, label: '审核通过' },
                { value: 3, label: '审核驳回' },
            ],
            loading: false,
            tableData: [],
            tableKey: 0,
            tableColumns: tableColumns(),
            tablePage: pageConfig(),
            // 时间限制
            pickerOptions: {
                // disabledDate 用于禁用超过明天零点的日期时间
                disabledDate(time) {
                    // 计算明天的起始时间（第二天 00:00:00）
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(0, 0, 0, 0);
                    // 如果选择的时间大于等于明天 00:00:00，则禁用
                    return time.getTime() >= tomorrow.getTime();
                }
            },
        }
    },
    mounted() {
    },
    activated() {
        this.searchList()
    },
    methods: {
        // 申请提现
        requestWithdrawal() {
            if(Number(this.WithdrawableAmount) <= 0){
                this.$message.warning('暂无可提现金额')
                return
            }
            this.$router.push({
                path: '/finance/requestWithdrawal'
            })
        },
        // 详情
        detailhandler(row) {
            if (row.withdrawType != 1) {
                this.$router.push({
                    path: '/finance/withdrawalDetails',
                    query: { withdrawNo: row.withdrawNo }
                })
            } else {
                this.$router.push({
                    path: '/finance/autoWriteOffDetail',
                    query: { withdrawNo: row.withdrawNo }
                })
            }
        },
        //查询提现汇总
        apiGetWithdrawalHistorySummary() {
            getWithdrawalHistorySummary().then(res => {
                if (res.code == 200) {
                    this.withdrawnAmount = res.data.totalWithdrawalAmount || 0
                    this.monthPayouts = res.data.monthWithdrawalAmount || 0
                    this.balanceAccount = res.data.platformBalance || 0
                    this.verificationAmount = res.data.totalNoMeltAmount || 0
                    this.WithdrawableAmount = res.data.canWithdrawAmount || 0
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 导出提现记录
        exportTable() {
            if (this.tableData.length == 0) {
                this.$message.warning('暂无数据可导出')
                return
            }
            const colName = this.tableColumns.filter(item => item.field != 'operation').map(item => item.field).join(',');
            const colNameDesc = this.tableColumns.filter(item => item.field != 'operation').map(item => item.title).join(',');
            const formInfo = {
                ...this.formData,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                applyDateStart: this.formData.applicationDate ? this.formData.applicationDate[0] + ' 00:00:00' : null,
                applyDateEnd: this.formData.applicationDate ? this.formData.applicationDate[1] + ' 23:59:59' : null,
                payTimeStart: this.formData.payoutDate ? (this.formData.payoutDate[0]? this.formData.payoutDate[0] + ' 00:00:00' : null) : null,
                payTimeEnd: this.formData.payoutDate ? (this.formData.payoutDate[1] ? this.formData.payoutDate[1] + ' 23:59:59' : null ): null,
            }
            delete formInfo.applicationDate
            delete formInfo.payoutDate
            const params = {
                taskBean: 'FinanceWithdrawRemote_pageQueryWithDraw',
                colName,
                colNameDesc,
                menuDesc: '提现记录',
                moduleName: 'FINANCE',
                exportParams: JSON.stringify(formInfo)
            }
            this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        },
        // 下载提现回执单
        downloadReceipt(row) {
            if (!row.receiptUrl) {
                this.$message.warning('暂无银行回执单，请稍后再试下')
                return
            }
            downloadInvoiceFile(row.receiptUrl).then(res => {
                let blob = new Blob()
                if (row.receiptUrl.includes('pdf')) {
                    blob = new Blob([res], { type: 'application/pdf' })
                } else {
                    blob = new Blob([res], { type: 'image/jpeg' })
                }
                const link = document.createElement('a')
                link.href = window.URL.createObjectURL(blob)
                link.download = `${row.payTimeStr}_提现回执单.${row.receiptUrl.includes('pdf') ? 'pdf' : row.receiptUrl.includes('png') ? 'png' : 'jpg'}`
                document.body.appendChild(link)
                link.click()
                window.URL.revokeObjectURL(link.href)
                document.body.removeChild(link)
            })
        },
        // 获取提现记录列表
        getList() {
            this.loading = true
            const params = {
                ...this.formData,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                applyDateStart: this.formData.applicationDate ? this.formData.applicationDate[0] : null,
                applyDateEnd: this.formData.applicationDate ? this.formData.applicationDate[1] : null,
                payTimeStart: this.formData.payoutDate ? (this.formData.payoutDate[0]? this.formData.payoutDate[0] + ' 00:00:00' : null) : null,
                payTimeEnd: this.formData.payoutDate ? (this.formData.payoutDate[1] ? this.formData.payoutDate[1] + ' 23:59:59' : null ): null,
            }
            delete params.applicationDate
            delete params.payoutDate
            getWithdrawalHistoryList(params).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.tableData = data.list
                    this.tablePage.pageNum = data.pageNum
                    this.tablePage.total = data.total
                    this.loading = false
                    this.tableKey = this.tableKey + 1
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            })
            this.apiGetWithdrawalHistorySummary()
        },
        // 查询
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
        // 分页器改变处理
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList();
        },
        //重置
        resetForm() {
            this.tablePage.pageSize = 20
            this.formData = {
                applicationDate: [defaultBeginTime, defaultEndTime],
                payoutDate: [],
                withdrawNo: '',
                withdrawType: 0,
                payStatus: '',
                approvalStatus:''

            }
            this.searchList()
        },
    }
}
</script>

<style lang="scss" scoped>
#total {
    display: flex;
    flex-direction: row;
    justify-content: start;

    #total_content {
        align-items: center;
        margin: 0 23px 0 23px;
        display: flex;
        flex-direction: column;

        #total_aomunt {
            font-size: 23px;
            font-weight: bolder;
        }
    }
}
</style>