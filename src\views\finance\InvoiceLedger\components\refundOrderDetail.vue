<template>
    <div class="app-container">
        <xyy-panel :titleShow="false">
            <div slot="tools">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div class="info-box">
                        <div class="info-box-content">
                            <div class="info-box-text">
                                退补价单号： {{ formData.erpRefundPriceCode }}
                            </div>
                            <div class="info-box-text">
                                日期： {{ formData.refundAdjustmentDate }}
                            </div>
                        </div>
                        <div class="info-box-content">
                            <div class="info-box-text">
                                商业返利金额： ￥{{ formData.commercialRebate }}
                            </div>
                            <div class="info-box-text">
                                供货价差金额： ￥{{ formData.supplyPriceDifference }}
                            </div>
                            <div class="info-box-text">
                                退补价金额： ￥{{ formData.refundAdjustmentAmount }}
                            </div>
                        </div>
                    </div>
                    <div>
                        <el-button v-hasPermi="['finance:RefundOrderDetail:back']" @click="backToPage">返回</el-button>
                    </div>
                </div>
            </div>
        </xyy-panel>
        <xyy-panel :titleShow="false">
            <div class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }" :key="tableKey"
                    empty-text="暂无退补价单明细数据">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :width="item.width" :fixed="item.fixed">
                            <template v-slot="{ row }">
                                <div v-if="item.field === 'taxRate'">
                                    {{ row[item.field] }}%
                                </div>
                                <div v-else>
                                    {{ row[item.field] }}
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
    </div>
</template>

<script>
import { getInvoicesDetailList, getInvoicesList } from '@/api/finance/InvoiceLedger'
import { refundDetailColumns } from '../config'
import { pageConfig } from '@/utils';
import XEUtils from 'xe-utils'
export default {
    data() {
        return {
            formData: { // 表单数据
                refundAdjustmentCode: '',  // 退补价单号
                refundAdjustmentDate: '', // 日期
                refundAdjustmentAmount: '',   // 退补价金额
                commercialRebate: '',   // 商业返利金额
                supplyPriceDifference: '',    // 供货价差金额
            },
            loading: false, // 表格加载
            tableData: [],  // 表格数据
            tableKey: 0,    // 表格刷新
            tableColumns: refundDetailColumns(),  // 表格列
            refundAdjustmentCode: this.$route.query.refundAdjustmentCode,  // 退补价单号
            tablePage: pageConfig()
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.getInfo()
            this.searchList()
        })
    },
    methods: {
        // 获取退补价单明细
        getList() {
            this.loading = true
            const params = {
                refundAdjustmentCode: this.refundAdjustmentCode,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize
            }
            getInvoicesList(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.tableData = result.list
                    this.tablePage.total = result.total
                    this.loading = false
                    this.tableKey = this.tableKey + 1
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            })
        },
        // 分页
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage
            this.tablePage.pageSize = pageSize
            this.getList()
        },
        // 获取退补价单明细
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
        // 获取退补价单详情
        getInfo() {
            const params = {
                refundAdjustmentCode: this.refundAdjustmentCode
            }
            getInvoicesDetailList(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.formData = result
                    this.formData.refundAdjustmentDate = XEUtils.toDateString(result.refundAdjustmentDate, 'yyyy-MM-dd')
                } else {
                    this.$message.error(msg)
                }
            })
        },
        // 返回
        backToPage() {
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
        },
    },
}
</script>

<style lang="scss" scoped>
.info-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;

    .info-box-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .info-box-text {
            margin: 12px 23px 12px 23px;
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
    }
}
</style>