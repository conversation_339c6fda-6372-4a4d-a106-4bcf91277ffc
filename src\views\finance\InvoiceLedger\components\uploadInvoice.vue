<template>
    <div>
        <xyy-dialog key="dialog1" :footerShow="true" ref="uploadInvoice" title="上传发票" width="30%">
            <xyy-panel :titleShow="false">
                <el-upload action="#" ref="excludeImport" :http-request="uploadFile" :file-list="fileFormData"
                    :before-remove="removeImportData" :show-file-list="true" :limit="2"
                    :before-upload="beforeImportData" :auto-upload="false" :on-change="handleChange"
                    accept=".pdf,.png,.jpg">
                    <el-button type="primary" size="small" style="margin-right: 10px">
                        发票上传
                    </el-button>
                    <el-button v-if="uploaded" size="small" @click.stop="viewUploaded">查看已上传</el-button>
                </el-upload>
                <div>
                    <p>格式:PDF，PNG，JPG 10MB大小</p>
                </div>
            </xyy-panel>
            <span slot="footer" class="dialog-footer">
                <el-button @click="close">取 消</el-button>
                <el-button type="primary" @click="submitUpload" :loading="submitLoading">确 定</el-button>
            </span>
        </xyy-dialog>
        <view-invocie ref="viewInvoice"></view-invocie>
    </div>
</template>

<script>
import { uploadInvoice } from '@/api/finance/InvoiceLedger'
import viewInvocie from './viewInvocie.vue'
export default {
    name: 'uploadInvoice',
    components: {
        viewInvocie,
    },
    data() {
        return {
            fileFormData: [],
            uploaded: false,
            rowIndex: '',
            row: {},
            submitLoading: false,
        }
    },
    methods: {
        // 上传发票
        submitUpload() {
            this.$refs.excludeImport.submit()
        },
        // 文件上传
        handleChange(file, fileList) {
            if (fileList.length > 1) {
                fileList.shift()
            }
            this.fileFormData = fileList; // 更新为数组
            this.uploaded = fileList.length > 0;
        },
        //批量导入
        uploadFile({ file }) {
            if (file) {
                this.submitLoading = true
                const formData = new FormData()
                formData.append('file', file)
                uploadInvoice(formData).then(res => {
                    if (res.code === 200) {
                        this.$message.success('上传成功')
                        this.submitLoading = false
                        this.$refs.uploadInvoice.close()
                        this.$emit('imgUrlCallback', res.url, this.rowIndex)
                    } else {
                        this.$message.error(res.message)
                        this.submitLoading = false
                    }
                }).catch(err => {
                    this.$message.error('上传失败')
                    this.submitLoading = false
                })
            } else {
                this.$message.warning('请选择上传文件!')
            }
        },
        //清空上传列表
        removeImportData() {
            return this.$confirm('确定要删除该发票文件吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$refs.excludeImport.clearFiles()
                this.fileFormData = []
                this.uploaded = false
            }).catch(() => {
                // 继续抛出错误或者返回一个 rejected Promise
                return Promise.reject('用户取消了删除操作')
            })
        },

        beforeImportData(uploadInfo) {
            const isLt10M = uploadInfo.size / 1024 / 1024 < 10
            if (!isLt10M) {
                this.$message.warning('文件过大，最大支持10M!')
                return false
            }
            return true
        },
        viewUploaded() {
            if (this.fileFormData.length > 0) {
                if (this.fileFormData[0].url) {
                    const fileUrl = this.fileFormData[0].url
                    if (fileUrl.includes('pdf')) {
                        window.open(fileUrl);
                    } else {
                        this.$refs.viewInvoice.open(fileUrl, 'image')
                    }
                } else {
                    const fileUrl = URL.createObjectURL(this.fileFormData[0].raw);
                    if (this.fileFormData[0].raw.type === 'application/pdf') {
                        window.open(fileUrl); // 打开 PDF 文件
                    } else {
                        this.$refs.viewInvoice.open(fileUrl, 'image')
                    }
                }
            } else {
                this.$message.error('没有已上传的文件');
            }
        },
        handleBatchAudit() {
            if (!this.fileFormData.file) {
                this.$message.warning('请选择要上传的文件')
                return
            }
        },
        // 关闭上传发票弹窗
        close() {
            this.$refs.uploadInvoice.close()
        },
        // 打开上传发票弹窗
        open(row, rowIndex) {
            this.$refs.uploadInvoice.open(row, rowIndex)
            this.$nextTick(() => {
                this.$refs.excludeImport.clearFiles()
                this.fileFormData = []
                this.uploaded = false
                this.rowIndex = rowIndex
                this.row = row
                if (row.imageUrl) {
                    this.fileFormData = [{ name: '已上传发票', url: row.imageUrl }]
                    this.uploaded = true
                }
            })
        }
    },
}
</script>

<style></style>