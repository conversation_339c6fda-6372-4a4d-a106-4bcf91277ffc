<template>
    <div class="app-container product-sales-record-page">
        <xyy-panel title="查询条件">
            <btn-group slot="tools" :btn-list="btnListTop" />
            <el-form ref="formData" :model="formData" label-width="120px">
                <el-row :gutter="20">
                    <el-col :lg="12" :md="12">
                        <el-form-item label="订单创建时间">
                            <el-date-picker v-model="formData.orderCreateTime" value-format="yyyy-MM-dd HH:mm:ss"
                                :default-time="['00:00:00', '23:59:59']" type="datetimerange" placeholder="选择日期范围"
                                :clearable="false" :picker-options="datePickerOptions"
                                popper-class="hide-clear-button"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="商品信息">
                            <el-input v-model="formData.productName" placeholder="请输入内容" disabled>
                                <template slot="append">
                                    <el-button class="el-icon-delete" @click="empty"></el-button>
                                    <el-button class="el-icon-search" @click="openSearch"></el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="发货仓">
                            <el-select v-model="formData.orgCodes" placeholder="请选择" multiple clearable>
                                <el-option v-for="item in deliveryWarehouseList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="订单编号">
                            <el-input v-model="formData.orderCode" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="出库单号">
                            <el-input v-model="formData.outOrderCode" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel :titleShow="false">
            <div slot="tools" style="position: relative">
                <div>
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane name="saleOrder">
                            <div slot="label">
                                <span>销售订单记录</span>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane name="saleReturnOrder">
                            <div slot="label">
                                <span>销退订单记录</span>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
            <div :key="tabKey" class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" :seq-config="{
                        startIndex: (tablePage.pageNum - 1) * tablePage.pageSize,
                    }" :show-overflow="false">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed" :show-overflow="false">
                            <template #header>
                                <div v-if="item.hit">
                                    <el-tooltip placement="top">
                                        <template slot="content">
                                            <div v-for="(it, index) in item.hit" :key="index">
                                                <div>{{ it }}</div>
                                            </div>
                                        </template>
                                        <div>
                                            <span>{{ item.title }}</span>
                                            <span>
                                                <svg-icon icon-class="prompt-icon" />
                                            </span>
                                        </div>
                                    </el-tooltip>
                                </div>
                                <div v-else>
                                    {{ item.title }}
                                </div>
                            </template>
                            <template v-slot:default="slotProps">
                                <div v-if="item.field === 'sku'" style="text-align: left;">
                                    <div>商品编码: {{ slotProps.row.csuid }}</div>
                                    <div>erp商品编码: {{ slotProps.row.erpProductCode }}</div>
                                </div>
                                <div v-else-if="item.field === 'productName'" style="text-align: left;">
                                    <div>商品名称: {{ slotProps.row.productName }}</div>
                                    <div>规格: {{ slotProps.row.spec }}</div>
                                    <div>生产厂家: {{ slotProps.row.manufacturer }}</div>
                                </div>
                                <div v-else-if="item.field === 'batchCode'">
                                    <div v-for="(it, index) in slotProps.row.batchCodeList" :key="index">
                                        {{ it }}
                                    </div>
                                </div>
                                <div v-else-if="item.field === 'expiryTime'">
                                    <div v-for="(it, index) in slotProps.row.expiryTimeList" :key="index">
                                        {{ it }}
                                    </div>
                                </div>
                                <div v-else-if="item.field === 'businessOutCode'">
                                    <div v-for="(it, index) in slotProps.row.businessOutCodeList" :key="index">
                                        {{ it }}
                                    </div>
                                </div>
                                <div v-else-if="item.field === 'supplyCode'">
                                    <div v-for="(it, index) in slotProps.row.supplyCodeList" :key="index">
                                        {{ it }}
                                    </div>
                                </div>
                                <div v-else-if="item.field === 'supplyAmount'">
                                    <div v-for="(it, index) in slotProps.row.supplyAmountList" :key="index">
                                        {{ it }}
                                    </div>
                                </div>
                                <div v-else>
                                    {{ slotProps.row[item.field] }}
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
        <product-search ref="productSearch" @echoData="echoData" />
    </div>
</template>

<script>
import productSearch from "./components/productSearch.vue";
import {
    productSalesRecord,
    productSalesReturnRecord,
} from "@/api/product/productSalesRecord";
import { SaleOrdertableColumns, saleReturnColumns } from "./config";
import XEUtils from "xe-utils";
import { pageConfig } from "@/utils";
import { exportFile } from '@/api/system/exportCenter';
const end = new Date();
const start = XEUtils.getWhatDay(end, -6); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, "yyyy-MM-dd 00:00:00"); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, "yyyy-MM-dd 23:59:59"); // 将结束日期格式化为字符串
export default {
    name: "ProductSalesRecord",
    dicts: ['org_code'],
    components: {
        productSearch
    },
    data() {
        return {
            // 日期选择器配置，限制最多选择3个月（90天）
            datePickerOptions: {
                onPick: ({ maxDate, minDate }) => {
                    // 只保存日期部分，忽略时分秒
                    const chosenDate = new Date(minDate);
                    chosenDate.setHours(0, 0, 0, 0); // 设置为当天的00:00:00
                    this.chosenDay = chosenDate.getTime();
                    if (maxDate) {
                        this.chosenDay = '';
                    }
                },
                disabledDate: (time) => {
                    if (this.chosenDay) {
                        // 将当前判断的时间也设置为当天的00:00:00，只比较日期部分
                        const currentDate = new Date(time);
                        currentDate.setHours(0, 0, 0, 0);
                        const currentTime = currentDate.getTime();

                        // 计算90天的毫秒数（基于自然日）
                        const ninetyDays = 90 * 24 * 3600 * 1000;
                        const minTime = this.chosenDay - ninetyDays;
                        const maxTime = this.chosenDay + ninetyDays;

                        return currentTime < minTime || currentTime > maxTime;
                    }
                    return false;
                }
            },
            chosenDay: '', // 用于存储用户选择的第一个日期
            btnListTop: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: () => this.searchHandler(1),
                    code: "",
                    plain: "false",
                    permission: "product:ProductSalesRecord:search",
                },
                {
                    label: "重置",
                    clickEvent: this.refresh,
                    code: "",
                    plain: "false",
                    permission: "product:ProductSalesRecord:refresh",
                },
                {
                    label: "导出",
                    clickEvent: this.exportTable,
                    code: "",
                    plain: "false",
                    permission: "product:ProductSalesRecord:export",
                },
            ],
            formData: {
                orderCreateTime: [defaultBeginTime, defaultEndTime],
                // settleTime: [defaultBeginTime, defaultEndTime],
                csuid: "",
                orgCodes: [],
                orderCode: "",
                outOrderCode: "",
                productName: "",
                productCode: "",
            },
            // deliveryWarehouseList: [],
            activeName: "saleOrder",
            tabKey: 0,
            tableKey: 0,
            loading: false,
            tableData: [],
            tableColumns: SaleOrdertableColumns(),
            tablePage: {
                pageSize: 20,
                pageNum: 1,
                total: 0,
                pageSizes: [10, 20, 50],
            },
        };
    },
    computed: {
        deliveryWarehouseList() {
            return this.$store.getters.warehouses.map(item => {
                return { label: item.dictLabel, value: item.dictValue, id: item.id }
            })
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.searchHandler();
        })
    },
    activated() {
        this.$nextTick(() => {
            this.searchHandler();
        })
    },
    methods: {
        empty() {
            this.formData.productName = ""
            this.formData.csuid = ""
            this.formData.productCode = ""
        },
        echoData(data) {
            this.formData.productName = data.productName
            this.formData.csuid = data.csuid
            this.formData.productCode = data.productCode
        },
        openSearch() {
            this.$refs.productSearch.open()
        },
        getList() {
            this.loading = true
            const params = {
                ... this.formData,
                createTimeStart: this.formData.orderCreateTime ? this.formData.orderCreateTime[0] : "",
                createTimeEnd: this.formData.orderCreateTime ? this.formData.orderCreateTime[1] : "",
                pageSize: this.tablePage.pageSize,
                pageNum: this.tablePage.pageNum,
            }
            delete params.orderCreateTime
            delete params.productName
            if (this.activeName == 'saleOrder') {
                params.orderStatusList = [10, 7, 3, 2, 1, 91] // EC订单状态，临时硬编码，后续优化成查询条件
                delete params.productCode
                productSalesRecord(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0) {
                        result?.list?.forEach(item => {
                            item.settleDateStr = XEUtils.toDateString(item.settleDate, "yyyy-MM-dd")
                            item.createTimeStr = XEUtils.toDateString(item.createTime, "yyyy-MM-dd HH:mm:ss")
                            item.outTimeStr = XEUtils.toDateString(item.outTime, "yyyy-MM-dd HH:mm:ss")
                            item.expiryTimeList = item.expiryTime?.split(',')?.map(item => XEUtils.toDateString(item, "yyyy-MM-dd"))
                            item.batchCodeList = item.batchCode?.split(',')
                            item.businessOutCodeList = item.businessOutCode?.split(',')
                            item.supplyCodeList = item.supplyCode?.split(',')
                            item.supplyAmountList = item.supplyAmount?.split(',')
                        })
                        this.tableData = result.list;
                        this.tablePage.total = result.total;
                    } else {
                        this.$message.error(msg)
                    }
                }).finally(() => {
                    this.$nextTick(() => {
                        this.loading = false
                        this.tableKey++
                    })
                })
            } else {
                delete params.csuid
                productSalesReturnRecord(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0) {
                        result?.list?.forEach(item => {
                            item.settleDateStr = XEUtils.toDateString(item.settleDate, "yyyy-MM-dd")
                            item.createTimeStr = XEUtils.toDateString(item.createTime, "yyyy-MM-dd HH:mm:ss")
                            item.expiryTimeList = item.expiryTime?.split(',')?.map(item => XEUtils.toDateString(item, "yyyy-MM-dd"))
                            item.batchCodeList = item.batchCode?.split(',')
                            item.businessOutCodeList = item.businessOutCode?.split(',')
                            item.supplyCodeList = item.supplyCode?.split(',')
                            item.supplyAmountList = item.supplyAmount?.split(',')
                        })
                        this.tableData = result.list;
                        this.tablePage.total = result.total;
                    } else {
                        this.$message.error(msg)
                    }
                }).finally(() => {
                    this.$nextTick(() => {
                        this.loading = false
                        this.tableKey++
                    })
                })
            }
        },
        exportTable() {
            if (this.tableData.length == 0) {
                this.$message.warning('暂无数据可导出')
                return
            }
            const formInfo = {
                ... this.formData,
                createTimeStart: this.formData.orderCreateTime ? this.formData.orderCreateTime[0] : "",
                createTimeEnd: this.formData.orderCreateTime ? this.formData.orderCreateTime[1] : "",
                pageSize: this.tablePage.pageSize,
                pageNum: this.tablePage.pageNum,
            }
            delete formInfo.orderCreateTime
            delete formInfo.productName
            let colName = ''
            let colNameDesc = ''
            let taskBean = ''
            let menuDesc = ''
            if (this.activeName == 'saleOrder') {
                formInfo.orderStatusList = [10, 7, 3, 2, 1, 91] // EC订单状态，临时硬编码，后续优化成查询条件
                delete formInfo.productCode
                colName = 'settleDate,settleNo,shippingWarehouse,customerName,customerCode,customerTypeName,orderNo,orderStatusStr,createTime,outTime,outOrderCode,businessOutCode,csuid,erpProductCode,productName,spec,manufacturer,approvalNumber,batchCode,expiryTime,supplyCode,supplyAmount,salesAmount,productTaxPrice,taxAmount,rebateMoney,settleMoney'
                colNameDesc = '结算时间,结算单号,发货仓,客户名称,客户ID,客户类型,订单编号,订单状态,订单创建时间,出库时间,出库单号,erp出库单号,商品编码,erp商品编码,商品名称,规格,生产厂家,批准文号,批号,有效期至,供货单号,供货金额（元）,销售数量,销售单价（含税）,销售金额（含税）,商业返利,结算金额'
                taskBean = 'SalesOrderRemote.salesList'
                menuDesc = '销售订单记录'
            } else {
                delete formInfo.csuid
                colName = 'settleDate,settleNo,shippingWarehouse,customerName,customerCode,orderNo,refundRequestStorageCode,createTime,csuid,erpProductCode,productName,spec,manufacturer,approvalNumber,batchCode,expiryTime,salesAmount,productTaxPrice,taxAmount,rebateMoney,settleMoney'
                colNameDesc = '结算时间,结算单号,发货仓,客户名称,客户ID,订单编号,退款单号,订单创建时间,商品编码,erp商品编码,商品名称,规格,生产厂家,批准文号,批号,有效期至,销售退回数量,销售单价（含税）,销售金额（含税）,商业返利,结算金额'
                taskBean = 'SalesOrderRemote.refundList'
                menuDesc = '销退订单记录'
            }
            const params = {
                taskBean,
                colName,
                colNameDesc,
                menuDesc,
                moduleName: 'ORDER',
                exportParams: JSON.stringify(formInfo)
            }
            this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        },
        refresh() {
            this.formData = {
                orderCreateTime: [defaultBeginTime, defaultEndTime],
                // settleTime: [defaultBeginTime, defaultEndTime],
                csuid: "",
                orgCodes: [],
                orderCode: "",
                outOrderCode: "",
                productName: '',
            };
            this.searchHandler();
        },
        searchHandler() {
            this.tablePage.pageNum = 1;
            this.getList();
        },
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList();
        },
        handleClick() {
            if (this.activeName == "saleOrder") {
                this.tableColumns = SaleOrdertableColumns();
                this.searchHandler();
            } else {
                this.tableColumns = saleReturnColumns();
                this.searchHandler();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
// 组件内部的样式
.product-sales-record-page {
    position: relative; // 确保组件有定位上下文
}
</style>

<style lang="scss">
/*
 * 组件级别样式：只针对商品销售记录页面的日期选择器
 * 通过组件特定的类名和popper-class组合来限制作用范围
 */
.product-sales-record-page~.hide-clear-button .el-picker-panel__footer .el-button:first-child,
.hide-clear-button[data-popper-reference-hidden] .el-picker-panel__footer .el-button:first-child,
.hide-clear-button .el-picker-panel__footer .el-button:first-child {
    display: none !important;
}

/* 确保确定按钮居中显示 - 仅限当前组件 */
// .product-sales-record-page ~ .hide-clear-button .el-picker-panel__footer,
// .hide-clear-button[data-popper-reference-hidden] .el-picker-panel__footer,
// .hide-clear-button .el-picker-panel__footer {
//     text-align: center;
// }

/* 针对ElementUI不同版本的兼容性处理 - 仅限当前组件 */
.product-sales-record-page~.hide-clear-button .el-date-range-picker__time-header .el-button:first-child,
.product-sales-record-page~.hide-clear-button .el-picker-panel__footer .el-button[class*="clear"],
.hide-clear-button[data-popper-reference-hidden] .el-date-range-picker__time-header .el-button:first-child,
.hide-clear-button[data-popper-reference-hidden] .el-picker-panel__footer .el-button[class*="clear"],
.hide-clear-button .el-date-range-picker__time-header .el-button:first-child,
.hide-clear-button .el-picker-panel__footer .el-button[class*="clear"] {
    display: none !important;
}

/*
 * 更安全的方案：使用属性选择器和更高的特异性
 * 确保只有当前页面的日期选择器受影响
 */
body:has(.product-sales-record-page) .hide-clear-button .el-picker-panel__footer .el-button:first-child {
    display: none !important;
}

// body:has(.product-sales-record-page) .hide-clear-button .el-picker-panel__footer {
//     text-align: center;
// } 

body:has(.product-sales-record-page) .hide-clear-button .el-date-range-picker__time-header .el-button:first-child,
body:has(.product-sales-record-page) .hide-clear-button .el-picker-panel__footer .el-button[class*="clear"] {
    display: none !important;
}
</style>