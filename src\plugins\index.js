import tab from './tab'
import auth from './auth'
import cache from './cache'
import modal from './modal'
import download from './download'
import xeUtils from 'xe-utils'
import xyy from '@/components/xyy'
import VXETable from 'vxe-table'
import 'vxe-table/lib/index.css'
VXETable.setup({
  size: 'small',
  zIndex: 100, // 全局 zIndex 起始值，如果项目的的 z-index 样式值过大时就需要跟随设置更大，避免被遮挡
  version: 0, // 版本号，对于某些带数据缓存的功能有用到，上升版本号可以用于重置数据
  table: {
    stripe: true,
    resizable: true,
    align: 'center',
    showOverflow: 'title',
    border: true,
  },
  pager: {
    pageSize: 100,
    pagerCount: 7,
    pageSizes: [100, 200, 500, 1000],
    layouts: ['PrevJump', 'PrevPage', 'Jump', 'PageCount', 'NextPage', 'NextJump', 'Sizes', 'Total']
  }
})

export default {
  install(Vue) {
    // 页签操作
    Vue.prototype.$tab = tab
    // 认证对象
    Vue.prototype.$auth = auth
    // 缓存对象
    Vue.prototype.$cache = cache
    // 模态框对象
    Vue.prototype.$modal = modal
    // 下载文件
    Vue.prototype.$download = download
    Vue.prototype.$xeUtils = xeUtils
    Vue.use(xyy)
    Vue.use(VXETable)
  }
}
