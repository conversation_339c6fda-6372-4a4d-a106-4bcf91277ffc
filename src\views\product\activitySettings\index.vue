<template>
    <div class="app-container">
        <div class="header">
            <div class="header-content">
                <div class="header-text">1. 拼团活动自动创建：开启期间，每新创建一个普通商品，将自动创建一个拼团活动；若对应普通商品创建了特价活动，则根据特价价格生成拼团活动；</div>
                <div class="header-text">2. 拼团信息自动同步：开启期间，系统自动创建的拼团活动将与普通商品信息保持同步；</div>
                <div class="header-text">3. 自动生成/同步的价格：以普通商品 * 价格系数得出拼团商品的价格（取两位小数）</div>
            </div>
        </div>
        <div class="form">
            <div class="form-content">
                <div class="form-content-item">
                    <span class="form-content-item-text">
                        自动上架钱多多拼团
                    </span>
                    <span class="form-content-item-input">
                        <el-radio-group v-model="formData.autoPintuan" @input="handleAutoGroupChange">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </span>
                </div>
                <div class="form-content-item">
                    <span class="form-content-item-text">
                        最低订单价
                    </span>
                    <span class="form-content-item-input">
                        <el-input v-model="formData.minAmount" placeholder="请输入最低订单价" disabled>
                            <template slot="prepend">¥</template>
                        </el-input>
                    </span>
                </div>
                <div class="form-content-item">
                    <div class="form-content-item-text">
                        <span style="color: red;">*</span>
                        价格系数
                    </div>
                    <span class="form-content-item-input-container">
                        <div class="form-content-item-info">
                            • 拼团销售价=一口价商品销售价*系数(四舍五入)；若存在进行中或未上线的特价活动，则拼团销售价=特价价格
                        </div>
                        <div class="form-content-item-info">
                            • 拼团成团价（客户可见价格）=拼团销售价*（1+加点系数*拼团活动打折系数）
                        </div>
                        <div class="form-content-item-input">
                            <span class="form-content-item-text">默认系数</span>
                            <span>
                                <el-input v-model="formData.pintuanRate" placeholder="请输入价格系数" :disabled="!isEdit"
                                    @input="handlePintuanRateInput"></el-input>
                            </span>
                            <span class="clickWriting" @click="editPriceCoefficient">修 改</span>
                        </div>
                        <div v-if="isEdit" style="margin-top: 23px;">
                            <el-button @click="cancelHandle" style="margin-right: 23px;"
                                v-hasPermi="['product:activitySettings:cancel']">取 消</el-button>
                            <el-button type="primary" @click="confirmHandle"
                                v-hasPermi="['product:activitySettings:confirm']" :loading="activitySettingLoading">提
                                交</el-button>
                        </div>
                    </span>
                </div>
            </div>
        </div>
        <turn-off-auto-report ref="turnOffAutoReport" @closeAndShelve="closeAndShelve" />
    </div>
</template>

<script>
import { getPintuanRate, openAutoCreate, closeAutoCreate, closeAndDelete, setPintuanRate, checkAccess } from '@/api/product/addReduction';
import turnOffAutoReport from './components/turnOffAutoReport.vue';
export default {
    name: "ActivitySettings",
    components: {
        turnOffAutoReport,
    },
    data() {
        return {
            formData: {
                autoPintuan: 0,   // 是否自动上架拼团
                minAmount: 0,   // 最低订单价
                pintuanRate: 0,    // 价格系数
            },
            isEdit: false,  // 是否编辑状态
            activitySettingLoading: false,  // 活动设置loading
        };
    },
    mounted() {
        this.fetchPintuanRate();
    },
    destroyed() {
        localStorage.removeItem('originalPriceCoefficient')
    },
    methods: {
        // 轮询接口
        async pollApi() {
            return new Promise((resolve, reject) => {
                // const loading = this.$loading({
                //     lock: true,
                //     text: '正在提交数据……',
                //     spinner: 'el-icon-loading',
                //     background: 'rgba(0, 0, 0, 0.7)',
                // });

                const checkStatus = async () => {
                    try {
                        const res = await checkAccess();
                        if (res.code === 0) {
                            if (res.result == 1) {
                                this.$message.success('修改成功');
                                // loading.close();
                                resolve(1);
                            } else if (res.result == 2) {
                                this.$message.error('修改失败，请稍后重试');
                                // loading.close();
                                resolve(2);
                            } else {
                                // 如果 result 不是 1 或 2，继续轮询
                                setTimeout(checkStatus, 1000);
                            }
                        }
                    } catch (error) {
                        // loading.close();
                        reject(error);
                    }
                };
                checkStatus();
            });
        },
        //提交
        async confirmHandle() {
            const params = {
                pintuanRate: this.formData.pintuanRate
            }
            try {
                const loading = this.$loading({
                    lock: true,
                    text: '正在提交数据……',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                });
                this.activitySettingLoading = true;
                const res = await setPintuanRate(params);
                if (res.code === 0) {
                    let status = await this.pollApi();
                    if (status == 1) {
                        this.fetchPintuanRate();
                        this.activitySettingLoading = false;
                        this.isEdit = false;
                        loading.close();
                    }
                    if (status == 2) {
                        this.activitySettingLoading = false;
                        loading.close();
                    }
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg,
                    });
                    this.activitySettingLoading = false;
                    loading.close();
                }
            } catch (error) {
                console.log(error);
                this.activitySettingLoading = false;
                loading.close();
            }
        },
        handlePintuanRateInput(value) {
            // 移除所有无效字符：仅允许数字和小数点
            let newValue = value.replace(/[^0-9.]/g, '');

            /* 
                修改正则表达式规则：
                匹配以下情况：
                1. 数字 0 或以 0 开头的小数：例如 "0"、"0."、"0.1"、"0.12"
                2. 数字 1：必须后跟 0 或不带小数部分，如 "1"、"1."、"1.0"、"1.00"
            */
            const pintuanRateRegex = /^(?:0(?:\.\d{0,2})?|1(?:\.0{0,2})?)$/;

            // 如果不匹配，即输入值有格式问题或超过 1，则进行修正
            if (!pintuanRateRegex.test(newValue)) {
                let num = parseFloat(newValue);
                if (isNaN(num)) {
                    num = 0;
                }
                // 如果数值大于 1，则直接限制为 1
                if (num > 1) {
                    newValue = '';
                } else {
                    // 如果在 0 ~ 1 之间，但格式不对，则固定小数位数后再转换为字符串（并去除多余的0）
                    newValue = parseFloat(num.toFixed(2)).toString();
                }
            }

            // 更新数据绑定
            this.$set(this.formData, 'pintuanRate', newValue);
        },

        // 关闭自动上架拼团且删除拼团品
        async fetchCloseAndDelete() {
            try {
                const loading = this.$loading({
                    lock: true,
                    text: '正在提交数据……',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                });
                const res = await closeAndDelete({})
                if (res.code === 0) {
                    let status = await this.pollApi()
                    if (status == 2) {
                        this.formData.autoPintuan = 1
                        loading.close();
                    } else if (status == 1) {
                        this.formData.autoPintuan = 0
                        loading.close();
                    }
                } else {
                    this.$message.error(res.msg);
                    this.formData.autoPintuan = 1
                    loading.close();
                }
            } catch (error) {
                console.log(error);
                this.formData.autoPintuan = 1
                loading.close();
            }
        },
        //仅关闭自动上架拼团
        async fetchCloseAutoCreate() {
            try {
                const loading = this.$loading({
                    lock: true,
                    text: '正在提交数据……',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                });
                const res = await closeAutoCreate({})
                if (res.code === 0) {
                    let status = await this.pollApi()
                    if (status == 2) {
                        this.formData.autoPintuan = 1
                        loading.close();
                    } else if (status == 1) {
                        this.formData.autoPintuan = 0
                        loading.close();
                    }
                } else {
                    this.$message.error(res.msg);
                    this.formData.autoPintuan = 1
                    loading.close();
                }
            } catch (error) {
                console.log(error);
                this.formData.autoPintuan = 1
                loading.close();
            }
        },
        // 开启自动上架拼团
        async fetchOpenAutoCreate() {
            try {
                const loading = this.$loading({
                    lock: true,
                    text: '正在提交数据……',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                });
                const res = await openAutoCreate({})
                if (res.code === 0) {
                    let status = await this.pollApi()
                    if (status == 2) {
                        this.formData.autoPintuan = 0
                        loading.close();
                    } else if (status == 1) {
                        this.formData.autoPintuan = 1
                        loading.close();
                    }
                } else {
                    this.$message.error(res.msg);
                    this.formData.autoPintuan = 0
                    loading.close();
                }
            } catch (error) {
                console.log(error);
                this.formData.autoPintuan = 0
                loading.close();
            }
        },
        // 获取拼团活动信息
        async fetchPintuanRate() {
            const loading = this.$loading({
                lock: true,
                text: '加载中……',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
            });
            try {
                const res = await getPintuanRate();
                if (res.code === 0) {
                    this.formData = res.result;
                    localStorage.setItem('originalPriceCoefficient', this.formData.pintuanRate)
                    loading.close();
                } else {
                    this.$message.error(res.msg);
                    loading.close();
                    this.formData.autoPintuan = 1
                }
            } catch (error) {
                console.log(error);
                loading.close();
                this.formData.autoPintuan = 1
            }
        },
        // 关闭自动上架拼团
        closeAndShelve(flag) {
            if (flag == 0) {
                this.fetchCloseAutoCreate({});
            } else {
                this.fetchCloseAndDelete({});
            }
        },
        // 自动上架钱多多拼团
        handleAutoGroupChange(value) {
            this.formData.autoPintuan = value == 1 ? 0 : 1;
            if (value == 0) {
                this.$refs.turnOffAutoReport.open();
            } else {
                this.fetchOpenAutoCreate();
            }
        },
        // 启用编辑
        editPriceCoefficient() {
            this.isEdit = true;
        },
        // 取消编辑
        cancelHandle() {
            this.isEdit = false;
            this.$set(this.formData, 'pintuanRate', localStorage.getItem('originalPriceCoefficient'))
        },
    }
}
</script>

<style lang="scss" scoped>
.header {
    margin: 23px, 0;
    background-color: rgb(254, 255, 202);

    .header-content {
        .header-text {
            margin-left: 23px;
            margin-bottom: 12px;
            color: red;
        }
    }
}

.form {
    margin-top: 46px;

    .form-content {
        position: relative;
        margin-left: 23px;

        .form-content-item {
            position: relative;
            margin-bottom: 23px;
            display: flex;
            align-items: center;

            .form-content-item-text {
                width: 200px;
                font-size: 16px;
                font-weight: 500;
                color: #333;
                align-self: flex-start;  // 添加此行，文本对齐顶部
            }

            .form-content-item-input {
                width: 300px;
                font-size: 16px;
                font-weight: 500;
                color: #333;
            }


            .form-content-item-input-container {

                .form-content-item-info {
                    margin-top: 12px;
                    font-size: 16px;
                    color: red;
                }

                .form-content-item-input {
                    margin-top: 12px;
                    display: flex;
                    align-items: center;

                    .form-content-item-text {
                        margin-right: 12px;
                        font-size: 16px;
                        font-weight: 500;
                        color: #333;
                    }

                    .clickWriting {
                        margin-left: 12px;
                        width: 100px;
                    }
                }
            }
        }
    }
}

::v-deep .el-radio__input.is-checked .el-radio__inner {
    background-color: #39B950;
    border-color: #39B950;
}

::v-deep .el-radio__input.is-checked+.el-radio__label {
    color: #39B950;
}
</style>