export function SaleOrdertableColumns() {
  return [
    { field: "settleDateStr", title: "结算时间", width: 180 },
    { field: "settleNo", title: "结算单号", width: 180 },
    { field: "shippingWarehouse", title: "发货仓", width: 180 },
    { field: "customerName", title: "客户名称", width: 230 },
    // { field: "customerAddress", title: "客户地址", width: 230 },
    { field: "customerCode", title: "客户ID", width: 180 },
    { field: "customerTypeName", title: "客户类型", width: 180 },
    { field: "orderNo", title: "订单编号", width: 230 },
    // { field: "refundNo", title: "退款单号", width: 180 },
    {
      field: "orderStatusStr",
      title: "订单状态",
      width: 180,
      hit: [
        "订单下单后需后台审核确认，状态为订单审核中",
        "客户下单未支付货款，状态为未支付",
        "订单拣货/分拣环节（未出库），状态为出库中",
        "订单商品完成打包/物流配送中/客户已签收，状态为已完成、配送中",
        "订单全部发起退款并退款完成，状态为已退款，客户可对支付后（出库中和已出库）订单发起全部退款，能否产生结算请以最终结算单为准",
      ],
    },
    { field: "createTimeStr", title: "订单创建时间", width: 180 },
    { field: "outTimeStr", title: "出库时间", width: 180 },
    { field: "outOrderCode", title: "出库单号", width: 180 },
    { field: "businessOutCode", title: "erp出库单号", width: 180 },
    { field: "sku", title: "商品编码/erp商品编码", width: 230 },
    { field: "productName", title: "商品名称/规格/生产厂家", width: 230 },
    { field: "approvalNumber", title: "批准文号", width: 180 },
    { field: "batchCode", title: "批号", width: 180 },
    { field: "expiryTime", title: "有效期至", width: 180 },
    { field: "supplyCode", title: "供货单号", width: 180 },
    { field: "supplyAmount", title: "供货金额（元）", width: 180 },
    { field: "salesAmount", title: "销售数量", width: 180 },
    { field: "productTaxPrice", title: "销售单价（含税）", width: 180 },
    { field: "taxAmount", title: "销售金额（含税）", width: 180 },
    { field: "rebateMoney", title: "商业返利", width: 180 },
    { field: "settleMoney", title: "结算金额", width: 180 },
  ];
}

export function saleReturnColumns() {
  return [
    { field: "settleDateStr", title: "结算时间", width: 180 },
    { field: "settleNo", title: "结算单号", width: 180 },
    { field: "shippingWarehouse", title: "发货仓", width: 180 },
    { field: "customerName", title: "客户名称", width: 230 },
    // { field: "customerAddress", title: "客户地址", width: 180 },
    { field: "customerCode", title: "客户ID", width: 180 },
    // { field: "customerType", title: "客户类型", width: 180 },
    { field: "orderNo", title: "订单编号", width: 230 },
    { field: "refundRequestStorageCode", title: "退款单号", width: 230 },
    // {
    //   field: "orderStatus",
    //   title: "订单状态",
    //   width: 180,
    //   hit: [
    //     "商品出库后，状态为“已出库”；",
    //     "商品未出库，状态为“未出库”；",
    //     "订单完成收款后，状态为“已完成”；",
    //     "客户退款后，订单状态为“已退款”",
    //   ],
    // },
    { field: "createTimeStr", title: "订单创建时间", width: 180 },
    // { field: "shipTime", title: "出库时间", width: 180 },
    // { field: "shipNo", title: "出库单号", width: 180 },
    // { field: "erpShipNo", title: "erp出库单号", width: 180 },
    { field: "sku", title: "商品编码/erp商品编码", width: 200 },
    { field: "productName", title: "商品名称/规格/生产厂家", width: 200 },
    { field: "approvalNumber", title: "批准文号", width: 180 },
    { field: "batchCode", title: "批号", width: 180 },
    { field: "expiryTime", title: "有效期至", width: 180 },
    // { field: "supplyNo", title: "供货单号", width: 180 },
    // { field: "supplyAmount", title: "供货金额（元）", width: 180 },
    { field: "salesAmount", title: "销售退回数量", width: 180 },
    { field: "productTaxPrice", title: "销售单价（含税）", width: 180 },
    { field: "taxAmount", title: "销售金额（含税）", width: 180 },
    { field: "rebateMoney", title: "商业返利", width: 180 },
    { field: "settleMoney", title: "结算金额", width: 180 },
  ];
}

export function productDataColumns() {
  return [
    { field: "orgName", title: "下单仓", width: 120 },
    { field: "erpProductCode", title: "本公司商品编码", width: 120 },
    { field: "csuid", title: "商品编码", width: 120 },
    { field: "productCode", title: "产品编码", width: 120 },
    { field: "productName", title: "商品名称", width: 120 },
    { field: "spuCategoryName", title: "商品类别", width: 120 },
    { field: "spec", title: "规格", width: 120 },
    { field: "productUnit", title: "单位", width: 120 },
    { field: "manufacturer", title: "生产厂家", width: 120 },
    { field: "code", title: "69码", width: 120 },
  ];
}
