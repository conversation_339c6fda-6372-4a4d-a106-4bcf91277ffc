import request from '@/utils/request'

//获取状态
export function getRefundStatus() {
  return request({
    url: '/order/refundOrder/getRefundStatus',
    method: 'post',
  })
}

//退货单列表查询
export function getRefundOrderList(data) {
  return request({
    url: '/order/refundOrder/queryList',
    method: 'post',
    data
  })
}


// 获取供货仓列表
export function getCooperationOrg() {
  return request({
    url: '/order/base/getCooperationOrg',
    method: 'post',
  })
}

// 获取供应商信息
export function getBusiness() {
  return request({
    url: '/order/base/getBusiness',
    method: 'post',
  })
}

// 获取退货单号
export function getRefundCode() {
  return request({
    url: '/order/refundOrder/getRefundCode',
    method: 'post',
  })
}

// 获取退货单配送方式
export function getDeliveryType() {
  return request({
    url: '/order/refundOrder/getRefundDeliveryMethod',
    method: 'post',
  })
}
// 退货单获取商品列表
export function getRefundProductList(data) {
  return request({
    url: '/order/refundOrder/getRefundProductList',
    method: 'post',
    data
  })
}

//新建退货单
export function saveRefundOrder(data) {
  return request({
    url: '/order/refundOrder/saveRefundOrder',
    method: 'post',
    data
  })
}

//退货单详情
export function viewRefundOrder(data) {
  return request({
    url: '/order/refundOrder/viewRefundOrder',
    method: 'post',
    data
  })
}
