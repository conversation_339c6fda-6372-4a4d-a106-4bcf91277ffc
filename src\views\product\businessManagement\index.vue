<template>
<div class="app-container">
    <xyy-panel title="查询条件">
        <!-- 按钮组 start-->
      <btn-group slot="tools" :btn-list="btnListTop"/>
      <el-form ref="ruleForm" :model="ruleForm" label-width="120px" class="clearfix">
        <el-row :gutter="20">
            <el-col :lg="6" :md="6">
                <el-form-item label="业务商圈">
                    <el-input v-model="ruleForm.busId" placeholder="请输入业务商圈" >
                    </el-input>
                </el-form-item >
            </el-col>
            <el-col :lg="6" :md="6">
                <el-form-item label="业务商圈名称">
                    <el-input v-model="ruleForm.busAreaName" placeholder="请输入业务商圈名称" >
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :lg="6" :md="6">
                <el-form-item prop="businessSource" label="商圈来源">
                    <el-select v-model="ruleForm.busSource" size="small" placeholder="全部">
                        <el-option
                        v-for="item in businessSource"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :lg="6" :md="6">
                <el-form-item prop="branchCode" label="商圈类型">
                    <el-select v-model="ruleForm.busType" size="small" placeholder="全部">
                        <el-option
                        v-for="item in businessType"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :lg="6" :md="6">
                <el-form-item prop="templateType" label="状态">
                    <el-select v-model="ruleForm.busStatus" size="small" placeholder="全部">
                        <el-option
                        v-for="item in state"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
      </el-form>
    </xyy-panel>
    <xyy-panel :titleShow='false'>
        <!-- 按钮组 -->
        <btn-group slot="tools" :btn-list="btnListTabale" style="float:right"/>
        <div :key="tabKey" class="table-box">
            <vxe-table ref="xTable" 
                :loading="loading" 
                highlight-current-row 
                height="auto" 
                :data="tableData"
                :key="tableKey"
                @checkbox-all="selectAllEvent"
                @checkbox-change="selectChangeEvent"
                :row-style="rowStyle">
                <vxe-table-column type="checkbox"  width="80" />
                <template v-for="item in tableColumns">
                    <vxe-table-column :key="item.field" :field="item.field" :title="item.title" :min-width="item.width" :fixed="item.fixed">
                    <template v-slot:default="slotProps">
                        <div v-if="item.field === 'test'">
                            
                        </div>
                        <div v-else-if="item.field==='operation'" >
                            <div>
                                <div style="display:flex;flex-direction: row;flex-wrap: wrap; ">
                                    <div  class="clickWriting" @click="viewProduct(slotProps.row)">
                                        查看商品
                                    </div>
                                    <div  class="clickWriting" @click="viewBusiness(slotProps.row)">
                                        查看业务商圈
                                    </div>
                                    <div
                                        type="text"
                                        size="small"
                                        class="clickWriting"
                                        @click="viewchangeRecordsVis(slotProps.row)"
                                    >
                                        查看变更记录
                                    </div>
                                    <div v-if="slotProps.row.busSource === 2"  type="text" size="small" class="clickWriting" @click="getCircleDetail(slotProps.row)"
                                    >
                                    编辑
                                    </div>
                                    <div v-if="slotProps.row.busStatus === 0 && slotProps.row.busSource === 2" class="clickWriting" type="text" size="small" @click="enableClick(slotProps.row)"
                                    >启用
                                    </div>
                                    <div v-if="slotProps.row.busStatus === 1 && slotProps.row.busSource === 2" class="clickWriting" type="text" size="small" @click="disableClick(slotProps.row)"
                                    >
                                    禁用
                                    </div>
                                    <div v-if="slotProps.row.busSource === 2" class="clickWriting" type="text" size="small"  @click="batchDelete('2',slotProps.row)"
                                    >
                                    删除
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            {{ slotProps.row[item.field] }}
                        </div>
                    </template>
                    </vxe-table-column>
                </template>
            </vxe-table>
        </div>
        <div class="pager">
            <vxe-pager
                border
                :current-page="tablePage.pageNum"
                :page-size="tablePage.pageSize"
                :total="tablePage.total"
                :page-sizes="tablePage.pageSizes"
                :layouts="[
                'PrevPage',
                'JumpNumber',
                'NextPage',
                'FullJump',
                'Sizes',
                'Total',
                ]"
                @page-change="handlePageChange"
            />
        </div>
    </xyy-panel>
    <!-- 查看商品 -->
    <view-product  :row="viewDialogRow" ref="viewDialog"></view-product>
    <!-- 查看变更记录 -->
    <viewChangeRecords
      ref="changeRecordsVis"
      :row="viewDialogRow"
    />
    <new-business-district
      :row="selectRow"
      ref="districtDialog"
      @onDialogChange="onDialogChange"
    />
    <!-- 查看业务商圈 -->
    <business-circle-detail-dialog
      :row="selectViewRow"
      ref="viewBusinessDialog"
    />
</div>
</template>
<script>
import { pageConfig } from '@/utils';
import {queryList,getBusArea,updateStatus,deleteBusArea,busBindProducts}   from '@/api/product/businessManagement'
import utils from "@/utils";
// 查看商品
import viewProduct from './components/viewProduct';
// 查看变更记录
import viewChangeRecords from './components/viewChangeRecords';
// 新增业务商圈
import newBusinessDistrict from './components/newBusinessDistrict';
// 查看业务商圈
import BusinessCircleDetailDialog from './components/businessCircleDetailDialog.vue';
import { planColumns ,detailData} from "./config";
export default{
    name: "BusinessManagement",
    components: {
        viewProduct,
        viewChangeRecords,
        newBusinessDistrict,
        BusinessCircleDetailDialog,
    },
    data(){
        return{
            btnListTop: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: this.search,
                    code: "",
                    plain: 'false',
                    permission:'product:businessManagement:search'
                },
                {
                    label: "重置",
                    clickEvent: this.resetForm,
                    code: "",
                    plain: 'false',
                    permission:'product:businessManagement:search'
                },
            ],
            btnListTabale:[
            {
                    label: "新建业务商圈",
                    type: "primary",
                    clickEvent: this.createCircle,
                    code: "",
                    icon: 'add-icon',
                    permission:'product:businessManagement:add'
                },
                {
                    label: "批量删除",
                    plain: 'false',
                    clickEvent: () => this.batchDelete('1'),
                    code: "",
                    icon: 'delete-icon',
                    permission:'product:businessManagement:delete'
                }
            ],
            ruleForm:{
                busId: '', // 业务商圈id
                busAreaName: '', // 业务商圈名称
                busSource: '', // 商圈来源
                busType: '', // 商圈类型
                busStatus: '', // 状态
            },
            // 商圈来源下拉框
            businessSource: [
                {
                value: '',
                label: '全部',
                },
                {
                value: '1',
                label: '系统默认',
                },
                {
                value: '2',
                label: '手工创建',
                },
                {
                value: '3',
                label: '活动创建',
                },
            ],
            // 商圈类型下拉框
            businessType: [
                {
                value: '',
                label: '全部',
                },
                {
                value: '1',
                label: '药品',
                },
                {
                value: '2',
                label: '非药',
                },
                {
                value: '3',
                label: '药品和非药',
                },
            ],
            // 状态下拉框
            state: [
                {
                value: '',
                label: '全部',
                },
                {
                value: '1',
                label: '启用',
                },
                {
                value: '0',
                label: '禁用',
                },
            ],
            tableKey:0,
            tableColumns: planColumns(),
            tableData:[
            ],
            loading:false,
            tablePage: pageConfig(),
            statistics:1,
            tabKey:0,
            viewDialogRow: undefined,
            selectRow: undefined,
            selectViewRow: undefined,
        }   
    },
    mounted(){
        this.$nextTick(()=>{
            this.tabKey++;
            this.search();
        })
    },
    methods:{
        // 重置条件
        resetForm() {
            this.ruleForm.busAreaName = '';
            this.ruleForm.busSource = '';
            this.ruleForm.busStatus = '';
            this.ruleForm.busType = '';
            this.ruleForm.busId = '';
            this.tablePage.pageNum = 1;
            this.tablePage.pageSize = 20;
            this.search();
        },
        // 启用变禁用
        async disableClick(row) {
            // 点击禁用校验是否已绑定商品，若绑定商品弹窗提示  查询商圈绑定商品数量
            // “该业务商圈已绑定商品，若禁用则已绑定该商圈的**个商品会默认企业经营区域，确定是否禁用”，
            // 点击确定则禁用成功，取消则关闭弹窗；
            // 未绑定商品则弹窗提示“是否禁用该业务商圈”，确定则禁用，取消则关闭弹窗
            try{
                let params = {
                    busId:row.id,
                }
                const res = await busBindProducts(params);
                if (res.code === 0) {
                    let param ={
                        busId:row.id,
                        status:0
                    }
                    if(res.result === 0){
                        this.$confirm('确定禁用该业务商圈吗？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消'
                        }).then(() => {
                            updateStatus(param).then((resp)=>{
                                if (resp.code == 0) {
                                    this.$message({
                                        message: '禁用成功',
                                        type: 'success'
                                    });
                                    this.search()
                                } else {
                                    this.$message.error(resp.msg)
                                    this.search()
                                }
                            })
                        }).catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消禁用'
                            })
                        })
                    }else{
                        const h = this.$createElement
                        this.$confirm('提示', {
                            title: '提示',
                            message: h('div', [
                            h(
                                'p',
                                { style: 'font-size:16px;font-weight:bold' },
                                '该业务商圈已绑定商品，确定禁用吗？'
                            ),
                            h(
                                'p',
                                { style: 'margin-top: 10px;font-size:15px;' },
                                `若禁用则已绑定的${res.result}个商品会默认企业经营区域`
                            )
                            ]),
                            confirmButtonText: '确定',
                            cancelButtonText: '取消'
                        }).then(() => {
                            updateStatus(param).then((resp)=>{
                                if (resp.code == 0) {
                                    this.$message({
                                        message: '禁用成功',
                                        type: 'success'
                                    });
                                    this.search()
                                } else {
                                    this.$message.error(resp.msg)
                                    this.search()
                                }
                            })
                        }).catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消禁用'
                            })
                        })
                    }
                }
            }catch(err){
                console.log(err);
                this.search()
            }
        },
        // 禁用状态变启用
        async enableClick(row) {
            let param ={
                busId:row.id,
                status:1
            }
            try {
                const res = await updateStatus(param);
                if (res.code == 0) {
                    this.$message({
                        message: res.msg,
                        type: 'success'
                    });
                    this.search()
                } else {
                    this.$message.error(res.msg || '获取列表失败')
                }
            } catch (e) {
                console.log(e)
            }
        
        },
        // 查看业务商圈
        viewBusiness(row) {
            this.selectViewRow = row;
            this.$refs.viewBusinessDialog.open();
        },
        onDialogChange() {
            // 刷新页面
            this.search();
        },
        // 查询当前选中行详情
        getCircleDetail(row) {
            this.goodsQuantity(row, true);
        },
        async goodsQuantity(row, detailData1) {
            if (!detailData1) {
                return;
            }
            try{
                const res = await getBusArea({busId:row.id})
                if(res.code == 0){
                    this.selectRow = res.result
                    this.$refs.districtDialog.open()
                    // if (res.data === 0) {
                    //     } else {
                    //     // const h = this.$createElement
                    //     // this.$confirm('提示', {
                    //     //     title: '提示',
                    //     //     message: h('div', [
                    //     //     h(
                    //     //         'p',
                    //     //         { style: 'font-size:16px;font-weight:bold' },
                    //     //         '该业务商圈已绑定商品,确定继续编辑吗？'
                    //     //     ),
                    //     //     h(
                    //     //         'div',
                    //     //         { style: 'margin-top: 10px;font-size:15px;' },
                    //     //         `若继续编辑则已绑定该商圈的${res.data}个商品将使用编辑后的商圈`
                    //     //     )
                    //     //     ]),
                    //     //     confirmButtonText: '确定',
                    //     //     cancelButtonText: '取消'
                    //     // })
                    //     //     .then(() => {
                    //     //     this.selectRow = detailData
                    //     //     this.$refs.districtDialog.open()
                    //     //     })
                    //     //     .catch(() => {
                    //     //     this.$message({
                    //     //         type: 'info',
                    //     //         message: '已取消编辑'
                    //     //     })
                    //     //     })
                    // }
                }else{
                    this.$message.error(res.msg || '获取列表失败')
                }
            }catch(error){
                
            }
        },
        /**查看变更记录 */
        viewchangeRecordsVis(row){
            this.viewDialogRow = row;
            this.$refs.changeRecordsVis.open()
        },
        // 查看商品
        viewProduct(row) {
            this.viewDialogRow = row
            this.$refs.viewDialog.open()  //查看商品弹窗
        },
        // 动态设置行高
        rowStyle({ row, rowIndex }) {
            return { 
                height: '85px', // 行高根据内容自适应
            };
        },
        /**删除 */
        batchDelete(type, row) {
            let params = [];
            if (type === '2') {
                //单个删除
                if(row.busSource !== 2){
                    this.$message.warning('系统生成的商圈无法删除，请重新勾选');
                    return false;
                }else{
                    params.push(row.id)
                }
            } else {
                let checkList = this.$refs.xTable.getCheckboxRecords();  //获取选中 
                if (!checkList.length > 0) {
                    this.$message.warning('请勾选需要删除的商圈');
                    return false;
                }
                let flag = false;
                checkList.forEach(item => {
                    if (item.busSource !== 2) {
                        flag = true;
                    } else {
                        params.push(item.id);
                    }
                });
                if (flag) {
                    this.$message.warning('系统生成的商圈无法删除，请重新勾选');
                    return false;
                }
            }
            this.$confirm('确定删除此商圈吗？删除后此商圈内商品将使用系统默认药品/非药商圈。', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await deleteBusArea(params);
                console.log(res);
                if (res && res.code === 0) {
                    this.$message.success('成功删除商圈');
                    this.search()
                } else {
                    this.$message.error(res.msg || '删除失败');
                }
            });
        },
        /**新建业务商圈 */
        createCircle() {
            this.selectRow = undefined;
            this.$refs.districtDialog.open()
        },
        // 查询商圈
        search() {
            const params = {};
            this.loading = true;
            Object.assign(params, this.ruleForm, this.tablePage);
            queryList(params)
                .then((res) => {
                const { code, result } = res;
                if (code === 0) {
                    this.tableKey++;
                    this.loading = false;
                    this.tableData = result.list;
                    this.tablePage.total = result.total; // 总数据数量
                    this.tablePage.pageNum = result.pageNum;
                }
            }).catch((err) => {
                this.loading = false;
            });
        },
        // 分页器改变处理
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.search();
        },
        selectAllEvent(){

        },
        selectChangeEvent(){

        },
    }
}
</script>
<style scoped>
::v-deep .vxe-cell{
  max-height: 100px !important;
}
.clickWriting{
    color: #00B955;
    cursor: pointer;
    margin: 0 5px 0 0;
}
</style>