<template>
<div class="app-container">
  <xyy-panel title='查询条件'>
    <el-form ref="formData" :model="formData" label-width="120px">
      <el-row :gutter="20">
        <el-col :lg="6" :md="6">
          <el-form-item label="商品信息" prop="searchLikeCode">
            <el-input v-model.trim="formData.searchLikeCode" placeholder="请输入本公司商品编码/csuid/erp商品编码" />
          </el-form-item>
        </el-col>
        <!-- <el-col :lg="6" :md="6">
          <el-form-item label="商品编码" prop="csuid">
            <el-input v-model.trim="formData.csuid" placeholder="请输入" />
          </el-form-item>
        </el-col> -->
        <el-col :lg="6" :md="6">
          <el-form-item label="商品名称" prop="subProductName">
            <el-input v-model.trim="formData.subProductName" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="供货单号" prop="supplyCode">
            <el-input v-model.trim="formData.supplyCode" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="本公司出库单号" prop="businessOutCode">
            <el-input v-model.trim="formData.businessOutCode" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
      </el-row>
    </el-form>
    <btn-group slot="tools" :btn-list="btnListTop"/>
  </xyy-panel>
  <xyy-panel :titleShow='false'>
    <p style="font-weight: 400; font-size: 14px;">更新时间：{{ updateTime }}</p>
    <div :key="tabKey" class="table-box">
      <vxe-table ref="xTable"
        :key="tableKey"
        :loading="loading"
        height="auto"
        :row-style="rowStyle"
        :data="tableData"
        empty-text="暂未查询到匹配的记录"
        :show-overflow="false"
        >
        <!-- <vxe-table-column type="checkbox" width="60"></vxe-table-column> -->
        <template >
          <vxe-table-column v-for="item in tableColumn" :key="item.field" :field="item.field" :title="item.title" :min-width="item.width" :fixed="item.fixed">
            <template v-slot:default="slotProps">
              <!-- 商品名称/规格/生产厂家 -->
              <div v-if="item.field === 'nameAnd'">
                <div style="text-align: center;">
                  <div>{{slotProps.row.productName}}</div>
                  <div>{{slotProps.row.productSterilization}}</div>
                  <div>{{slotProps.row.manufacturerName}}</div>
                </div>
              </div>
              <div v-else-if="item.field === 'erpProductCode'">
                <div style="text-align: left;">
                  <div>本公司商品编码：{{slotProps.row.erpProductCode}}</div>
                  <div>csuid：{{slotProps.row.csuid}}</div>
                  <div>erp商品编码：{{slotProps.row.productCode}}</div>
                </div>
              </div>
             <!-- 批号/有效期至 -->
              <div v-else-if="item.field === 'batchNumAnd'">
                <div style="text-align: center;">
                  <div>{{slotProps.row.batchNum}}</div>
                  <div>{{slotProps.row.validateDate.split('T')[0]}}</div>
                </div>
              </div>
              <!-- 入库日期/在库天数 -->
              <div v-else-if="item.field === 'intoTimeAnd'">
                <div style="text-align: center;">
                  <div>{{slotProps.row.intoTime.split('T')[0]}}</div>
                  <div>{{slotProps.row.lengthOfLibrary}}天</div>
                </div>
              </div>

              <div v-else>
                {{ slotProps.row[item.field] }}
              </div>
            </template>
          </vxe-table-column>
        </template>
      </vxe-table>
    </div>
    <div class="pager" >
      <vxe-pager
        border
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :page-sizes="tablePage.pageSizes"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total',
        ]"
        @page-change="handlePageChange"
      />
      <!-- <vxe-pager
        border
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total',
        ]"
        @page-change="handlePageChange"
      /> -->
    </div>
  </xyy-panel>

</div>
</template>
<script>
import { pageConfig } from "@/utils";
import utils from "@/utils";
import{inventoryCol} from './config'
import { getStorageList } from '@/api/product/storage'
import { exportFile } from '@/api/system/exportCenter';

export default {
  name: "ProductInventory",
  data() {
    return{
      isShow:true,
      formData:{ //查询条件
        // erpProductCode: '',
        // csuid: '',
        subProductName: '',
        supplyCode: '',
        businessOutCode: '',
        searchLikeCode: ''
      },
      loading:false,
      tablePage: pageConfig(),
      updateTime:'',
      btnListTop: [
          {
              label: "查询",
              type: "primary",
              clickEvent: this.getList,
              code: "",
              permission:'inventory:list:query'
          },
          {
              label: "重置",
              clickEvent: this.refresh,
              code: "",
              plain: 'false',
              permission:'inventory:list:refresh'
          },
          {
            label: "导出",
              clickEvent: this.exportTable,
              code: "",
              plain: 'false',
              permission:'inventory:list:export'
          },
      ],
      tabKey:0,
      tableKey:0,
      tableColumn: inventoryCol(),
      tableData:[]
    }
  },
  mounted(){
    this.getList();
    this.$nextTick(()=>{
      //utils.pageActivated();
      this.tabKey++
      this.tableKey++
    })
  },
  methods: {
    // 导出
    exportTable(){
      if (this.tableData.length === 0) {
                this.$message.warning('暂无数据可导出')
                return
          }
      const colName  = 'erpProductCode,csuid,productCode,productName,productSterilization,manufacturerName,supplyCode,stockOrderNo,businessOutCode,batchNum,validateDate,intoPrice,surplusAmount,soldAmount,intoTime,lengthOfLibrary'
      const colNameDesc = '本公司商品编码,csuid,erp商品编码,商品名称,规格,生产厂家,供货单号,入库单号,本公司出库单号,批号,有效期至,供货价(元),在库库存,已售库存,入库日期,在库天数'
      const formInfo = {
        ...this.formData,
        pageNum: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
      }
      const params = {
                    taskBean: 'LogicStorageApi.queryLogicStoragePage',
                    colNameDesc: colNameDesc,
                    colName: colName,
                    moduleName: 'PRODUCT',
                    menuDesc: '库存管理',
                    exportParams: JSON.stringify(formInfo)
              }
      this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
    },
    // 动态设置行高
    rowStyle({ row, rowIndex }) {
      return {
        height:'80px !important'
      }; // 统一设置行高为 60px
    },
    // 分页器改变处理
    handlePageChange({currentPage, pageSize}){
      this.tablePage.pageNum = currentPage;
      this.tablePage.pageSize = pageSize;
      this.getList(true);

    },


    // /**单选 */
    // selectChangeEvent({ checked, records }){
    // },
    // /**全选 */
    // selectAllEvent({ checked, records }){
    //   console.log(111);
    // },
    // 获取当前时间
     getFormattedDate() {
      const now = new Date();

      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      this.updateTime =  `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 表格查询
    getList(flag) {
      if (flag !== true) {
        this.tablePage.pageNum = 1;
      }
      this.loading = true;
      this.getFormattedDate();
      const params = {
        pageNum: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
        ...this.formData
      }
      getStorageList(params).then(res => {
        if(res.code === 200){
          this.tableData = res.data.list;
          this.tablePage.total = res.data.total;
        } else {
          this.$message.error(res.msg);
        }
      }).finally(() => {
        this.loading = false;
        this.tableKey++
      });

      this.$nextTick(() => {
          this.$refs.xTable.recalculate(true)
        })
    },
    // 表格重置
    refresh() {
      this.tablePage.pageSize= 20
      this.$refs.formData.resetFields();
      this.getList();
    }
  }

  }
</script>
<style lang="scss" scoped>
// ::v-deep .vxe-body--column{
//   max-height: 100px !important;
// }
// ::v-deep .vxe-cell{
//   max-height: 100px !important;
// }
</style>
