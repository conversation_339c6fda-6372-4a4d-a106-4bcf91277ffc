<template>
    <div>
        <xyy-dialog title='发布入仓商品' ref="productListDialog" width='80%' @on-close="onClose">
          <xyy-panel :titleShow="false">
                <el-form ref="formData" :model="formModel" label-width="120px">
                    <el-row :gutter="20">
                        <el-col :lg="6" :md="6">
                            <el-form-item label="商品编码" >
                                <el-input v-model="formModel.productCode" placeholder="请输入商品编码" />
                            </el-form-item>
                        </el-col>
                        <el-col :lg="6" :md="6">
                            <el-form-item label="商品名称" >
                                <el-input v-model="formModel.productName" placeholder="请输入商品名称" />
                            </el-form-item>
                        </el-col>
                        <el-col :lg="6" :md="6">
                            <el-form-item label="条形码" >
                                <el-input v-model="formModel.smallPackageBarCode" placeholder="请输入条形码" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div style="text-align: right;margin-bottom: 10px;">
                    <el-button type="primary" @click="handleFormSubmit">查询</el-button>
                    <el-button @click="handleFormReset">重置</el-button> 
                </div>
          </xyy-panel>
          <xyy-panel :titleShow="false">
                <div  class="table-box">    
                    <vxe-table ref="xTable" 
                        :loading="loading" 
                        highlight-current-row 
                        height="400px" 
                        :data="tableConfig.data"
                        :key="tableKey"
                        @checkbox-all="selectAllEvent"
                        @checkbox-change="selectChangeEvent"
                        >
                        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
                        <template v-for="item in tableConfig.col">
                        <vxe-table-column :key="item.field" :field="item.field" :title="item.title" :width="item.width" :fixed="item.fixed">
                            <template #header>
                                <div v-if="item.hit">
                                    <el-tooltip placement="top">
                                    <template slot="content">
                                        <div v-for="(it,index) in item.hit" :key="index">
                                        <div>{{it}}</div>
                                        </div>
                                    </template>
                                    <div>
                                        <span>{{item.title}}</span>
                                        <span>
                                        <svg-icon  icon-class="prompt-icon"/>
                                        </span>
                                    </div>
                                    </el-tooltip>
                                </div>
                                <div v-else>
                                    {{item.title}}
                                </div>
                            </template>
                            <template v-slot:default="slotProps">
                            <!-- 创建时间 -->
                            <div v-if="item.field === 'applicationTime'">
                                {{formatDate(slotProps.row[item.field], 'yyyy-MM-dd')}}
                            </div>
                            <!--操作按钮-->
                            <div v-else-if="item.field === 'options'">
                                <div>
                                <a href="#" class="clickWriting" v-if="activeName == 'shelvedProducts'" @click="pendingChange(slotProps.row,1)">下架</a>
                                <a href="#" class="clickWriting" v-if="activeName == 'pendingShelving'" @click="shelvedChange(slotProps.row,1)">上架</a>
                                <a href="#" class="clickWriting" style="margin-left:10px;" @click="editbusinessManagement(slotProps.row)">控销商圈设置</a>
                                </div>
                            </div>
                            <div v-else>
                                {{ slotProps.row[item.field] }}
                            </div>
                            </template>
                        </vxe-table-column>
                        </template>
                    </vxe-table>
                </div>
                <div class="pager" >
                    <vxe-pager
                        border
                        :current-page="tablePage.pageNum"
                        :page-size="tablePage.pageSize"
                        :total="tablePage.total"
                        :page-sizes="tablePage.pageSizes"
                        :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                        ]"
                        @page-change="handlePageChange"
                    />
                </div>
          </xyy-panel>
              <span slot='footer' class='dialog-footer'>
                    <el-button size='small' @click='onClose'>取 消</el-button>
                    <el-button type='primary' size='small' @click='submit' :loading='submitLoading'>确 定</el-button>
                </span>
        </xyy-dialog>
    </div>
</template>

<script>
import { pageConfig } from '@/utils'
import { publishList,publish} from '@/api/product/addReduction'
import XEUtils, { isArguments } from 'xe-utils'
import { formatDate } from "@/utils/index.js"

export default {
  name: 'productListDialog',
  components: {
  },
  data() {
    return {
      formModel: {
        productCode: '',//原商品编码
        productName: '',//商品名称
        smallPackageBarCode: ''//条形码
      },
      loading: false,
      tableConfig: {
        data: [],
        col: [
          {
            title: '商品编码',
            field: 'productCode',
          },
          {
            title: '原商品编码',
            field: 'oldProductCode',
          },
          {
            title: '商品名称',
            field: 'productName'
          }, {
            title: '条形码',
            field: 'smallPackageBarCode'
          }, {
            title: '规格',
            field: 'specifications'
          }, {
            title: '创建时间',
            field: 'applicationTime',
          }
        ]
      },
      tablePage: pageConfig(),
      tableKey:0,
      submitLoading: false
    }
  },
  created() {
  },
  methods: {
    formatDate,
    // 分页器改变处理
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNum = currentPage;
      this.tablePage.pageSize = pageSize;
      this.queryList(this.tablePage);
    },
    /**单选 */
    selectChangeEvent({ checked, records }){
    },
    /**全选 */
    selectAllEvent({ checked, records }){
      console.log(111);
    },
    /**关闭弹窗 */
    onClose() {
        this.$refs.productListDialog.close()
        this.$emit('on-close')
    },
    /**打开弹窗 */
    open(){
        this.$refs.productListDialog.open();
        this.queryList()
    },
    handleFormSubmit() {
      this.queryList()
    },
    handleFormReset() {
      Object.keys(this.formModel).forEach(item=>{
        this.formModel[item] = ''
      })
      this.tablePage.pageSize = 100;
      this.tablePage.pageNum = 1;
      this.queryList()
    },
    async queryList(tablePage) {
      this.loading = true
      if (tablePage) {
        const { pageSize, pageNum } = tablePage
        this.tablePage.pageSize = pageSize
        this.tablePage.pageNum = pageNum
      }
      let params = { ...this.formModel }
      const { pageSize, pageNum } = this.tablePage
      params.pageNum = pageNum
      params.pageSize = pageSize
      console.log(params)
      try {
        const res = await publishList(params)
        if (res && res.code === 0) {
          this.tableConfig.data = res.result.list
          this.tablePage.total = res.result.total
        } else {
          this.$message.error(res.message || '获取列表失败')
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    async submit() {
        let checkList = this.$refs.xTable.getCheckboxRecords();  //获取选中 
        const ids = checkList.map(item => item.productCode)
        if (ids && ids.length > 0) {
            this.submitLoading = true
            try {
            const res = await publish({productCodes:ids})
            if (res && res.code === 0) {
                let success = 0
                let error = [];
                if(res.result && res.result.length > 0){
                    res.result.forEach(item=>{
                        if(item.success){
                            success++;
                        }else{
                            error.push(item);
                        }
                    })
                }
                const str = [`<div>${success || 0}个商品发布成功</div>`]
                if (error) {
                    str.push(`<div>${error.length || 0}个商品发布失败</div>`)
                    if(error.length > 0){
                        error.forEach(item=>{
                            str.push(`<div>
                            <span>商品编码：</span>
                            <span>${item.productCode}</span>
                            <span>失败原因：</span>
                            <span>${item.reason}</span>
                            </div>`)
                        })
                    }
                }
                this.$confirm(str.join(''), '发布商品提醒', {
                    distinguishCancelAndClose: true,
                    dangerouslyUseHTMLString: true,
                    confirmButtonText: '返回商品列表',
                    cancelButtonText: '继续发布',
                    customClass: 'el-message-box__wrapper_lwq',
                }).then(() => {
                    this.$emit('search')
                    this.onClose()
                }).catch(action => {
                    this.queryList({
                        pageNum:1,
                        pageSize:100,
                    })
                    console.log(action)
                })
            } else {
                this.$message.error(res.message || '发布失败')
            }
            } catch (e) {
            console.log(e)
            }
            this.submitLoading = false
        } else {
            this.$message.warning('请勾选数据！')
        }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-message-box__wrapper_lwq .el-message-box__content{
    max-height: 400px !important; /* 设置你想要的高度 */
    overflow-y: auto !important; /* 如果内容超出高度，可以滚动 */
}

::v-deep .vxe-table--body-wrapper {
  height: 230px !important;
}
</style>
