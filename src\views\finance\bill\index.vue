<template>
    <div class="app-container">
        <xyy-panel :titleShow="false">
            <div slot="tools">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; ">
                    <div>
                        <div id="total">
                            <div id="total_content">
                                <div id="total_taxt">账户余额
                                    <el-tooltip content="结算单金额减已提现金额的剩余账号余额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${balanceAccount}` }}</div>
                            </div>
                            <div id="total_content">
                                <div id="total_taxt">发票待核销总金额
                                    <el-tooltip content="发票状态为已录入下的待核销发票金额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${verificationAmount}` }}</div>
                            </div>
                            <div id="total_content">
                                <div id="total_taxt">可提现金额
                                    <el-tooltip content="在账户余额和发票待核销总金额之间取最小金额为可提现金额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${WithdrawableAmount}` }}</div>
                            </div>
                            <!-- <div id="total_content">
                        <div id="total_taxt">本月提现</div>
                        <div id="total_aomunt">{{ `￥${monthPayouts}` }}</div>
                    </div> -->
                            <!-- <div id="total_content">
                        <div id="total_taxt">已提现金额</div>
                        <div id="total_aomunt">{{ `￥${withdrawnAmount}` }}</div>
                    </div> -->
                        </div>
                    </div>
                    <div>
                        <el-button type="primary" v-hasPermi="['finance:bill:requestWithdrawal']"
                            @click="goRequestWithdrawal">申请提现</el-button>
                        <el-button v-hasPermi="['finance:bill:export']" @click="handleExport">导出</el-button>
                        <el-button v-hasPermi="['finance:bill:withdrawalRecord']" @click="goRecord">提现记录</el-button>
                    </div>
                </div>
            </div>
        </xyy-panel>
        <xyy-panel title="查询条件">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListForm" />
            <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="账单号">
                            <el-input v-model="formData.flowNo" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="12" :md="12">
                        <el-form-item label="时间">
                            <el-date-picker v-model="formData.flowTimes" type="datetimerange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
                                :picker-options="pickerOptions"
                                :default-time="['00:00:00', '23:59:59']"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="账单类型">
                            <el-select v-model="formData.flowType" placeholder="请选择" clearable>
                                <el-option v-for="item in billTypeList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel title="账单列表">
            <div style="margin: 12px;">
                有未录入的发票？点击<span class="clickWriting" @click="goInvoice">去开票</span>可进行开票
            </div>
            <div class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }" :key="tableKey"
                    empty-text="暂无账单数据">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'operation'">
                                    <span v-if="slotProps.row.sourceName !== '系统'" @click="detailhandler(slotProps.row)"
                                        class="clickWriting">详情</span>
                                </div>
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
    </div>
</template>

<script>
import { pageConfig } from "@/utils";
import { getBillList, getTotalSaleAmount } from '@/api/finance/bill'
import { tableColumns } from './config'
import XEUtils from 'xe-utils'
import { exportFile } from '@/api/system/exportCenter';
const end = new Date();
const start = XEUtils.getWhatDay(end, -30); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd 00:00:00'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd 23:59:59'); // 将结束日期格式化为字符串
export default {
    name: 'Bill',
    data() {
        return {
            btnListForm: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: this.searchList,
                    plain: 'false',
                    permission: "finance:bill:query"
                },
            ],
            formData: { // 查询条件
                flowNo: '',
                flowTimes: [defaultBeginTime, defaultEndTime],
                flowType: ''
            },
            billTypeList: [ // 账单类型
                {
                    label: '全部',
                    value: ''
                },
                {
                    label: '手工调账-提现',
                    value: 1
                },
                {
                    label: '手工调账-结算',
                    value: 4
                },
                {
                    label: '结算充值',
                    value: 2
                },
                {
                    label: '提现扣减',
                    value: 3
                },
            ],
            balanceAccount: 0,  //账户余额
            verificationAmount: 0,   //发票待核销总金额
            WithdrawableAmount: 0,   //可提现金额
            loading: false, // 表格加载
            tableData: [],  // 表格数据
            tableKey: 0,    // 表格刷新
            tableColumns: tableColumns(),
            tablePage: pageConfig(),    // 分页
            pickerOptions: {
                // disabledDate 用于禁用超过明天零点的日期时间
                disabledDate(time) {
                    // 计算明天的起始时间（第二天 00:00:00）
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(0, 0, 0, 0);
                    // 如果选择的时间大于等于明天 00:00:00，则禁用
                    return time.getTime() >= tomorrow.getTime();
                }
            },
        }
    },
    activated() {
        this.searchList()
    },
    mounted() {
    },
    methods: {
        // 导出
        handleExport() {
            if (this.tableData.length === 0) {
                this.$message.warning('暂无数据可导出')
                return
            }
            const formInfo = {
                ...this.formData,
                flowTimeStart: this.formData.flowTimes ? this.formData.flowTimes[0] : '',
                flowTimeEnd: this.formData.flowTimes ? this.formData.flowTimes[1] : '',
                page: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize
            }
            delete formInfo.flowTimes
            const colNameDesc = this.tableColumns.filter(item => item.field != 'operation').map(item => item.title).join(',');
            const colName = this.tableColumns.filter(item => item.field != 'operation').map(item => item.field).join(',');
            const params = {
                taskBean: 'FinanceMerchantFlow_query',
                moduleName: 'FINANCE',
                colNameDesc,
                colName,
                exportParams: JSON.stringify(formInfo),
                menuDesc: '账单'
            }
            this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        },
        // 获取表格数据
        getList() {
            this.loading = true
            const params = {
                ...this.formData,
                flowTimeStart: this.formData.flowTimes ? this.formData.flowTimes[0] : '',
                flowTimeEnd: this.formData.flowTimes ? this.formData.flowTimes[1] : '',
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize
            }
            delete params.flowTimes
            getBillList(params).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.tableData = data.list || []
                    this.tablePage.total = data.total
                    this.tableKey++
                    this.loading = false
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            })
            this.getAmount()
        },
        // 分页
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList();
        },
        // 提现汇总
        async getAmount() {
            try {
                const res = await getTotalSaleAmount();

                if (res.code == 200) {
                    this.balanceAccount = res.data.platformBalance || 0
                    this.verificationAmount = res.data.waitWriteOffAmount || 0
                    this.WithdrawableAmount = res.data.canWithdrawAmount || 0
                } else {
                    this.$message.error(res.msg)
                }
            } catch (err) {
                console.log(err)
            }

        },
        //查询
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
        //申请提现
        goRequestWithdrawal() {
            if (Number(this.WithdrawableAmount) <= 0) {
                this.$message.warning('暂无可提现金额')
                return
            }
            this.$router.push({
                path: '/finance/requestWithdrawal',
            })
        },
        // 去开票
        goInvoice() {
            this.$router.push({
                path: '/finance/InvoiceLedger',
            })
        },
        // 提现记录
        goRecord() {
            this.$router.push({
                path: '/finance/WithdrawalHistory',
            })
        },

        // 详情
        detailhandler(row) {
            if (row.sourceName === '结算单') {
                this.$router.push({
                    path: '/finance/InvoicesDetail',
                    query: { settleNo: row.sourceNo }
                })

            } else if (row.sourceName === '提现申请') {
                if (row.amount == 0) {
                    this.$router.push({
                        path: '/finance/autoWriteOffDetail',
                        query: { withdrawNo: row.sourceNo }
                    })
                } else {
                    this.$router.push({
                        path: '/finance/withdrawalDetails',
                        query: { withdrawNo: row.sourceNo }
                    })
                }
            }
        },
    }
}
</script>

<style lang="scss" scoped>
#total {
    display: flex;
    flex-direction: row;
    justify-content: start;

    #total_content {
        align-items: center;
        margin: 0 23px 0 23px;
        display: flex;
        flex-direction: column;

        #total_aomunt {
            font-size: 23px;
            font-weight: bolder;
        }
    }
}
</style>
