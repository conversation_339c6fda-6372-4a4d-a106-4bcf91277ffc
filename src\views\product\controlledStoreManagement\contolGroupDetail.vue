<template>
    <div class="app-container">
        <xyy-panel title='查看控销组'>
            <div slot="tools" style="position: relative;">
                <div>
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane name="controlStore">
                            <div slot="label">
                                <span>控销药店</span>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane name="controlProduct">
                            <div slot="label">
                                <span>控销商品</span>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <!-- 按钮组 start -->
                <btn-group slot="tools" :btn-list="btnListTop" style="position: absolute; right: 0; top: 0; " />
            </div>
            <btn-group slot="tools" :btn-list="btnListForm" />
            <div>
                <el-form ref="formData" :model="formData" label-width="120px">
                    <el-row :gutter="20" v-if="activeName === 'controlStore'">
                        <el-col :lg="6" :md="6">
                            <el-form-item label="药店ID">
                                <el-input v-model="formData.merchantId" @input="merchantIdInput" />
                            </el-form-item>
                        </el-col>
                        <el-col :lg="6" :md="6">
                            <el-form-item label="药店名称">
                                <el-input v-model="formData.merchantName" />
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :lg="6" :md="6">
                            <el-form-item label="手机号">
                                <el-input v-model="formData.merchantMobile" />
                            </el-form-item>
                        </el-col> -->
                    </el-row>
                    <el-row :gutter="20" v-else>
                        <el-col :lg="6" :md="6">
                            <el-form-item label="商品编码">
                                <el-input v-model="formData.skuId" @input="skuIdInput" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </xyy-panel>
        <xyy-panel :titleShow='false'>
            <div :key="tabKey" class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
    </div>
</template>

<script>
import { queryMerchant, queryProduct } from '@/api/product/controlledStoreManagement';
import { controlStoreCol, controlProductCol } from './config';
import { pageConfig } from '@/utils';

export default {
    name: 'ContolGroupDetail',
    data() {
        return {
            activeName: 'controlStore',
            btnListTop: [
                {
                    label: "返回",
                    type: 'primary',
                    clickEvent: this.backToPage,
                    code: "",
                    plain: 'false',
                    permission: 'product:ContolGroupDetail:backToPage'
                },
            ],
            btnListForm: [
                {
                    label: "查询",
                    type: 'primary',
                    clickEvent: this.searchList,
                    code: "",
                    plain: 'false',
                    permission: 'product:ContolGroupDetail:searchList'
                },
                {
                    label: "重置",
                    type: 'info',
                    clickEvent: this.resetForm,
                    code: "",
                    plain: 'false',
                    permission: 'product:ContolGroupDetail:resetForm'
                },
            ],
            formData: {
                merchantId: '', // 药店ID
                merchantName: '', // 药店名称
                // merchantMobile: '', // 手机号
                skuId: '' // 商品编码
            },
            tableData: [], // 表格数据
            loading: false, // 表格加载状态
            tabKey: 0, // 表格key
            tableKey: 0, // 表格key
            tablePage: pageConfig(), // 表格分页
            tableColumns: controlStoreCol(), // 表格列配置
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (this.$route.query.activeName) {
                this.activeName = this.$route.query.activeName
            }
            this.searchList()
        })
    },
    methods: {
        skuIdInput(value) {
            let newVal = value.trim();
            newVal = newVal.replace(/[^\d]/g, '');
            this.formData.skuId = newVal;
        },
        merchantIdInput(value) {
            let newVal = value.trim();
            newVal = newVal.replace(/[^\d]/g, '');
            this.formData.merchantId = newVal;
        },
        handleClick() {
            if (this.activeName == 'controlStore') {
                this.tableColumns = controlStoreCol()
                this.searchList()
            } else {
                this.tableColumns = controlProductCol()
                this.searchList()
            }
        },
        backToPage() {
            this.$router.go(-1)
            this.$store.state.tagsView.visitedViews =
                this.$store.state.tagsView.visitedViews.filter(
                    (item) => item.name !== "ControlGroupEdit"
                );
        },
        resetForm() {
            this.formData = {
                merchantId: '',
                merchantName: '',
                // merchantMobile: '',
                skuId: ''
            }
            this.searchList()
        },
        getList() {
            this.loading = true
            if (this.activeName === 'controlStore') {
                const params = {
                    merchantId: this.formData.merchantId,
                    merchantName: this.formData.merchantName,
                    // merchantMobile: this.formData.merchantMobile,
                    groupId: Number(this.$route.query.groupId),
                    pageNum: this.tablePage.pageNum,
                    pageSize: this.tablePage.pageSize,
                }
                queryMerchant(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0) {
                        this.tableData = result.list
                        this.tablePage.total = result.total
                    } else {
                        this.$message.error(msg)
                    }
                }).finally(() => {
                    this.tableKey++
                    this.loading = false
                })
            } else {
                const params = {
                    skuId: this.formData.skuId,
                    groupId: Number(this.$route.query.groupId),
                    pageNum: this.tablePage.pageNum,
                    pageSize: this.tablePage.pageSize,
                }
                queryProduct(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0) {
                        this.tableData = result.list
                        this.tablePage.total = result.total
                    } else {
                        this.$message.error(msg)
                    }
                }).finally(() => {
                    this.tableKey++
                    this.loading = false
                })
            }
        },
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage
            this.tablePage.pageSize = pageSize
            this.getList()
        },
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
    },
}
</script>

<style></style>