import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return request({
    url: '/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 获取当前可用机构仓
export function queryOrg() {
  return request({
    url: '/queryOrg',
    method: 'get',
    timeout: 20000
  })
}

// 切换机构仓
export function switchOrg(orgCode) {
  return request({
    url: '/switchOrg',
    method: 'post',
    params: { orgCode }, 
    timeout: 20000
  })
}
// 校验弱密码
export function isPassWeak(params) {
  return request({
    url: '/isPassWeak',
    method: 'post',
    params: params
  })
}
// 校验公告
export function noticeList(params) {
  return request({
    url: '/system/notice/list',
    method: 'get',
    params: params
  })
}