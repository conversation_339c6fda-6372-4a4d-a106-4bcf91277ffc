<template>
    <div class="app-container">
        <xyy-panel title="查询条件">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTop" />
            <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="单据编号">
                            <el-input v-model="formData.supplyCode" placeholder="供货计划单编号/本公司出库单号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="12" :md="12">
                        <el-form-item label="订单创建时间">
                            <el-date-picker v-model="formData.SubmissionTime" value-format="yyyy-MM-dd" type="daterange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                :picker-options="pickerOptions" />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="商品信息">
                            <el-input v-model="formData.productInfo"
                                placeholder="商品名称/本公司商品编码/ERP商品编码/csuid/标准库id"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="单据状态">
                            <el-select v-model="formData.supplyStatus" clearable>
                                <el-option v-for="item in supplyStatusOptions" :key="item.code" :label="item.name"
                                    :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="配送方式">
                            <el-select v-model="formData.deliveryMethod" clearable>
                                <el-option v-for="item in deliveryMethodOptions" :key="item.code" :label="item.name"
                                    :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel :titleShow='false' :bottom="10">
            <div slot="tools" style="position: relative;">
                <div>
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane name="planModel">
                            <div slot="label">
                                <span>供货计划单列表</span>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane name="productModel">
                            <div slot="label">
                                <span>供货单商品明细</span>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <!-- 按钮组 start -->
                <btn-group slot="tools" :btn-list="btnListTabale" style="position: absolute; right: 0; top: 0; " />
            </div>
            <div class="btn-status-wrap">
                <div :class="['btn-status', statistics == '1' ? 'status-activity' : '']" @click="statusHandler(1)">
                    全部（{{ totalNum }}）</div>
                <div :class="['btn-status btn-status-right', statistics == '2' ? 'status-activity' : '']"
                    @click="statusHandler(2)">待确认发货（{{ confirmNum }}）</div>
            </div>
            <div :key="tabKey" class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    @checkbox-all="selectAllEvent"
                    :key="tableKey"
                    :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }"
                    @checkbox-change="selectChangeEvent">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field === 'test31'">
                                    <!-- 在 test31 插槽中渲染自定义内容 -->
                                    <el-input v-model="slotProps.row[item.field]" class="custom-input" size="small">
                                        <svg-icon slot="suffix" icon-class="edit-table-icon"
                                            style="margin-top: 10px;"></svg-icon>
                                    </el-input>
                                </div>
                                <div v-else-if="item.field === 'businessOutCode' && activeName == 'planModel'">
                                    <el-input v-model="slotProps.row[item.field]" suffix-icon="el-icon-edit-outline"
                                        @change="orderCodeChange($event, slotProps.row)"></el-input>
                                </div>
                                <div v-else-if="item.field === 'operation1'">
                                    <span @click="detailhandler(slotProps.row)" class="clickWriting">详情</span>
                                    <span style="margin-left:10px;"
                                        v-if="slotProps.row.supplyStatus == 1 || slotProps.row.supplyStatus == 2"
                                        @click="cancelhandler(slotProps.row)" class="clickWriting">取消订单</span>
                                    <span style="margin-left:10px;" @click="confirmhandler(slotProps.row)"
                                        v-if="slotProps.row.supplyStatus == 1" class="clickWriting">确认发货</span>
                                </div>
                                <div v-else-if="item.field === 'operation2'">
                                    <span @click="detailhandler(slotProps.row)" class="clickWriting">详情</span>
                                </div>
                                <div v-else-if="item.field === 'taxRate'">
                                    {{slotProps.row[item.field]}}%
                                </div>
                                <div v-else>
                                    {{ slotProps.row[item.field] }}
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <!-- <pagination :current-page.sync="tablePage.pageNum" :page-size.sync="tablePage.pageSize"
                    :total="tablePage.total" @pagination="handlePageChange"></pagination> -->
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
        <instockStandardDialog ref="instockStanDardDialog"></instockStandardDialog>
    </div>
</template>
<script>
import instockStandardDialog from './components/instockStandardDialog.vue';
import { exportFile } from '@/api/system/exportCenter'
import {
    getSupplyGoodsList,
    getSupplyGoodsProductList,
    updateSupplyGoods,
    cancelSupplyGoods,
    getSupplyGoodsStatus,
    getSupplyGoodsDelivery,
    getSupplyOrderDeliveryCount,
    getSupplyOrderProductDeliveryCount
} from "@/api/order/supplyGoods";
import utils from "@/utils";
import { pageConfig }  from "@/utils";
import { planColumns, productColumns } from "./config";
import XEUtils from 'xe-utils'
const end = new Date();
const start = XEUtils.getWhatDay(end, -6); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd'); // 将结束日期格式化为字符串
export default {
    name: "OrderSupplier",
    components: {instockStandardDialog},
    data() {
        return {
            btnListTop: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: this.searchList,
                    plain: 'false',
                    icon: 'search-icon',
                    permission: "supplier:order:query"
                },
                {
                    label: "重置",
                    clickEvent: this.resetForm,
                    plain: 'false',
                    icon: 'refresh-icon',
                    permission: 'supplier:order:reset'
                },
            ],
            btnListTabale: [
                {
                    label: "新建计划单",
                    type: "primary",
                    clickEvent: this.addHandler,
                    // icon: 'add-icon',
                    permission: 'supplier:order:add'
                },
                {
                    label: "导出",
                    type: "warning",
                    clickEvent: this.exprotHandler,
                    // icon: 'add-icon',
                    permission: 'supplier:order:export'
                },
                {
                    label: "仓库验收入库标准",
                    type: "info",
                    clickEvent: this.instockStandard,
                    // icon: 'search-icon',
                    permission: 'supplier:order:create'
                },
            ],
            formData: {
                SubmissionTime: [defaultBeginTime, defaultEndTime], // 提交时间
                supplyCode: "",
                productInfo: "",
                supplyStatus: '',
                deliveryMethod: ''
            },
            deliveryMethodOptions: [],
            supplyStatusOptions: [],
            tableColumns: planColumns(),
            tableData: [],
            tableData1: [],
            tableData2: [],
            activeName: 'planModel',
            loading: false,
            tablePage: pageConfig(),
            statistics: 1,
            tabKey: 0,
            // 时间限制
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            totalNum: 0, // 统计数量
            confirmNum: 0, // 待确认数量
            tableKey:0,//两个表格切换刷新
        }
    },
    mounted() {
        this.$nextTick(() => {
            //utils.pageActivated()
            //this.tabKey++
            this.apiGetSupplyGoodsStatus()
            this.apiGetSupplyGoodsDelivery()
        })
        // this.tableData = this.tableData1
        // this.searchList()
    },
    activated() {
        this.searchList()
    },
    methods: {
        //获取待确认发货数量
        apiGetWaitPublishCount() {
            const params = {
                ...this.formData
            }
            params.startTime = this.formData.SubmissionTime ? this.formData.SubmissionTime[0] + ' 00:00:00' : ''
            params.endTime = this.formData.SubmissionTime ? this.formData.SubmissionTime[1] + ' 23:59:59' : ''
            params.pageNum = this.tablePage.pageNum
            params.pageSize = this.tablePage.pageSize
            if (this.activeName == 'planModel') {
                getSupplyOrderDeliveryCount(params).then(res => {
                    this.confirmNum = res.result.waitPublishCount
                })
            } else {
                getSupplyOrderProductDeliveryCount(params).then(res => {
                    this.confirmNum = res.result.waitPublishCount
                })
            }
        },
        //仓库验收入库标准
        instockStandard() {
            this.$refs.instockStanDardDialog.open()
        },
        //导出
        exprotHandler() {
            if (this.tableData.length === 0) {
                this.$message.warning('暂无数据可导出')
                return
            }
            let params = {}
            if (this.activeName == 'planModel') {
                const formInfo = {
                    ...this.formData
                }
                formInfo.startTime = this.formData.SubmissionTime ? this.formData.SubmissionTime[0] + ' 00:00:00' : ''
                formInfo.endTime = this.formData.SubmissionTime ? this.formData.SubmissionTime[1] + ' 23:59:59' : ''
                formInfo.pageNum = this.tablePage.pageNum
                formInfo.pageSize = this.tablePage.pageSize
                params = {
                    taskBean: 'SUPPLY_ORDER_REMOTE_LISTSUPPLYORDER',
                    colNameDesc: this.tableColumns.filter(item => item.field != 'operation1').map(item => item.title).join(','),
                    colName: this.tableColumns.filter(item => item.field != 'operation1').map(item => item.field).join(','),
                    moduleName: 'SUPPLY_ORDER',
                    menuDesc: '供应商订单',
                    exportParams: JSON.stringify(formInfo)
                }
            } else {
                const formInfo = {
                    ...this.formData
                }
                formInfo.startTime = this.formData.SubmissionTime ? this.formData.SubmissionTime[0] + ' 00:00:00' : ''
                formInfo.endTime = this.formData.SubmissionTime ? this.formData.SubmissionTime[1] + ' 23:59:59' : ''
                formInfo.pageNum = this.tablePage.pageNum
                formInfo.pageSize = this.tablePage.pageSize
                params = {
                    taskBean: 'SUPPLY_REFUNDORDERREMOTE_LISTSUPPLYORDERPRODUCT',
                    colNameDesc: this.tableColumns.filter(item => item.field != 'operation2').map(item => item.title).join(','),
                    colName: this.tableColumns.filter(item => item.field != 'operation2').map(item => item.field).join(','),
                    moduleName: 'SUPPLY_ORDER',
                    menuDesc: '供货单商品明细',
                    exportParams: JSON.stringify(formInfo)
                }
            }
            this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        },
        // 获取物流方式枚举
        apiGetSupplyGoodsDelivery() {
            getSupplyGoodsDelivery().then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.deliveryMethodOptions = result
                }
            })
        },
        //获取供货单状态枚举
        apiGetSupplyGoodsStatus() {
            getSupplyGoodsStatus().then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.supplyStatusOptions = result
                }
            })
        },
        //重置
        resetForm() {
          this.tablePage.pageSize= 20
            this.formData = {
                SubmissionTime: [defaultBeginTime, defaultEndTime], // 提交时间
                supplyCode: "",
                productInfo: "",
                supplyStatus: null,
                deliveryMethod: null
            }
            this.searchList()
        },
        //查询列表
        getList(type) {
            this.loading = true
            const params = {
                ...this.formData
            }
            params.startTime = this.formData.SubmissionTime ? this.formData.SubmissionTime[0] + ' 00:00:00' : ''
            params.endTime = this.formData.SubmissionTime ? this.formData.SubmissionTime[1] + ' 23:59:59' : ''
            params.pageNum = this.tablePage.pageNum
            params.pageSize = this.tablePage.pageSize
            delete params.SubmissionTime
            if (type == 'confirm') {
                params.supplyStatus = 1
            }
            if (this.activeName == 'planModel') {
                getSupplyGoodsList(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0) {
                        this.tableKey++;
                        this.tableData = result.list
                        this.tablePage.total = result.total || 0
                        this.tablePage.pageNum = result.pageNum || 1
                        this.loading = false
                        if (type !== 'confirm') {
                            this.totalNum = result.total
                        } else {
                            this.confirmNum = result.total
                        }
                    } else {
                        this.$message({
                            type: 'error',
                            message: msg
                        })
                        this.loading = false
                    }
                })
            } else {
                getSupplyGoodsProductList(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0) {
                        this.tableKey++;
                        this.tableData = result.list
                        this.tablePage.total = result.total || 0
                        this.tablePage.pageNum = result.pageNum || 1
                        if (type !== 'confirm') {
                            this.totalNum = result.total
                        } else {
                            this.confirmNum = result.total
                        }
                        this.loading = false
                    } else {
                        this.$message({
                            type: 'error',
                            message: msg
                        })
                    }
                })
            }
            this.apiGetWaitPublishCount()
        },
        //取消订单
        cancelhandler(row) {
            this.$confirm('是否取消订单，取消后不可恢复！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const parmas = {
                    supplyCode: row.supplyCode
                }
                cancelSupplyGoods(parmas).then(res => {
                    const { code, msg } = res
                    if (code === 0) {
                        this.$message({
                            type: 'success',
                            message: '取消成功'
                        })
                        this.getList(this.statistics == 1 ? 'all' : 'confirm')
                    } else {
                        this.$message({
                            type: 'error',
                            message: msg
                        })
                    }
                })
            }).catch(() => { })
        },
        //更改表格出库单号
        orderCodeChange(value, row) {
            if (row.supplyStatus == 2 || row.supplyStatus == 3) {
                if (value == '') {
                    this.$message({
                        type: 'warning',
                        message: '收货中、已完成的出库单号不能为空'
                    })
                    return
                }
            }
            const params = {
                supplyCode: row.supplyCode,
                businessOutCode: row.businessOutCode
            }
            updateSupplyGoods(params).then(res => {
                const { code, msg } = res
                if (code === 0) {
                    this.$message({
                        type: 'success',
                        message: '修改成功'
                    })
                    this.getList(this.statistics == 1 ? 'all' : 'confirm')
                } else {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                }
            })
        },
        //确认发货
        confirmhandler(row) {
            this.$router.push({
                path: '/order/OrderSupplierConfirm',
                query: { supplyCode: row.supplyCode }
            })
        },
        searchList() {
            this.tablePage.pageNum = 1
            this.getList(this.statistics == 1 ? 'all' : 'confirm')
        },
        /**点击tabs栏 */
        handleClick(tab, event) {
            this.tableKey++;
            if (tab.name === "planModel") {
                this.tableColumns = planColumns()
                this.statistics = 1
                this.searchList()
                // this.tableData = this.tableData1
            } else {
                this.tableColumns = productColumns()
                this.statistics = 1
                // this.tableData = this.tableData2
                this.searchList()
            }
        },
        // 分页器改变处理
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList(this.statistics == 1 ? 'all' : 'confirm');
        },
        selectAllEvent() {

        },
        selectChangeEvent() {

        },
        statusHandler(status) {
            this.tableKey++;
            this.statistics = status
            if (status == 2) {
                this.getList('confirm')
            } else {
                this.getList('all')
            }
        },
        detailhandler(row) {
            this.$router.push({
                path: '/order/supplier/detail',
                query: { supplyCode: row.supplyCode }
            })
        },
        addHandler() {
            this.$router.push({
                path: '/order/OrderSupplierAdd',
            })
        }
    }
}
</script>
<style scoped>
.btn-status-wrap {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    font-size: 12px;
}

.btn-status-wrap .btn-status {
    border-color: #ccc;
    background-color: #f5f5f5;
    color: #333;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: default;
}

.btn-status-wrap .btn-status-right {
    margin-left: 10px;
}

.btn-status-wrap .status-activity {
    border: 1px solid #67c23a;
    background-color: #ffffff;
}

.btn-status-wrap .btn-status:hover {
    border-color: #67c23a;
    color: #333;
}
</style>
