<template>
    <xyy-dialog ref="addGroupAlert" width="500px" title="新增控销组" :closeOnClickModal="false">
        <div class="dialog-content">
            <div class="input-tip">请输入控销组名称</div>
            <el-form :model="form" :rules="rules" ref="formRef">
                <el-form-item prop="groupName">
                    <el-input v-model.trim="form.groupName" placeholder="请输入控销组名称" clearable
                        @clear="onClear"></el-input>
                </el-form-item>
            </el-form>
        </div>

        <template slot="footer">
            <div class="dialog-footer">
                <el-button @click="$refs.addGroupAlert.close()">取 消</el-button>
                <el-button type="primary" @click="addGroup" :loading="loading">确 定</el-button>
            </div>
        </template>
    </xyy-dialog>
</template>

<script>
import { addControlledStore } from '@/api/product/controlledStoreManagement';
export default {
    name: "addGroupAlert",
    data() {
        return {
            loading: false,
            form: {
                groupName: ""
            },
            rules: {
                groupName: [
                    { required: true, message: '请输入控销组名称', trigger: 'blur' },
                ]
            }
        }
    },
    methods: {
        close() {
            this.$refs.addGroupAlert.close()
        },
        open() {
            this.form.groupName = ""
            this.$refs.addGroupAlert.open()
            this.$nextTick(() => {
                this.$refs.formRef && this.$refs.formRef.clearValidate()
            })
        },
        async addGroup() {
            try {
                const valid = await this.$refs.formRef.validate()
                if (!valid) return

                this.loading = true
                const res = await addControlledStore({
                    name: this.form.groupName
                })
                if (res.code === 0) {
                    this.$emit('add-group', res.result)
                    this.$refs.addGroupAlert.close()
                }else {
                    this.$message.error(res.msg)
                }
            } catch (error) {
                console.error(error)
            } finally {
                this.loading = false
            }
        },
        onClear() {
            this.form.groupName = ""
        }
    }
}
</script>

<style scoped>
.dialog-content {
    padding: 20px;
}

.input-tip {
    margin-bottom: 15px;
    color: #606266;
}

.dialog-footer {
    text-align: right;
}

.dialog-footer .el-button {
    margin-left: 10px;
}

.error-tip {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 5px;
}

.el-input.is-error>>>.el-input__inner {
    border-color: #f56c6c;
}
</style>