<template>
  <div>
      <xyy-dialog key="dialog1" ref="productSearch" title="商品列表" width="80%" :footerShow="false">
          <xyy-panel title="查询条件">
              <!-- 按钮组 start-->
              <btn-group slot="tools" :btn-list="btnListTop" />
              <el-form :model="formData" ref="formData" label-width="100px" class="clearfix">
                  <el-row >
                      <el-col :lg="10" :md="10">
                          <el-form-item label="商品信息" prop="productInfo">
                              <el-input v-model="formData.productInfo" placeholder="商品名称/ERP商品编码/商品编码/产品编码"
                                  clearable></el-input>
                          </el-form-item>
                      </el-col>
                      <el-col :lg="7" :md="7">
                          <el-form-item label="供货单号" prop="supplyCode">
                              <el-input v-model="formData.supplyCode" placeholder="请输入"
                                  clearable></el-input>
                          </el-form-item>
                      </el-col>
                      <el-col :lg="7" :md="7">
                          <el-form-item label="供货仓库" prop="orgName">
                              <el-input v-model="formData.orgName" disabled></el-input>
                          </el-form-item>
                      </el-col>
                  </el-row>
              </el-form>
          </xyy-panel>
          <xyy-panel title="商品列表">
              <vxe-table ref="xTable" :loading="loading" highlight-current-row :data="tableData" empty-text="暂未查询到匹配的记录"
                  @cell-dblclick="reuturnInfo"
                  :key="tableKey"
                  :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }">
                  <vxe-table-column type="seq" title="序号" width="80" />
                  <template>
                      <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                          :title="item.title" :width="item.width" :fixed="item.fixed">
                          <template #header>
                      <div v-if="item.hit">
                        <el-tooltip placement="top">
                          <template slot="content">
                            <div v-for="(it,index) in item.hit" :key="index">
                              <div>{{it}}</div>
                            </div>
                          </template>
                          <div>
                            <span>{{item.title}}</span>
                            <span>
                              <svg-icon  icon-class="prompt-icon"/>
                            </span>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        {{item.title}}
                      </div>
                    </template>
                          <template v-slot="{ row }">
                            <div v-if="item.field === 'storeTime'">
                              <span>{{row.storeTime.split('T')[0] }}</span>
                          </div>
                            <div v-else>
                              <span>{{ row[item.field] }}</span>
                          </div>
                          </template>
                      </vxe-table-column>
                  </template>
              </vxe-table>
              <div class="pager">
                <vxe-pager
        border
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :page-sizes="tablePage.pageSizes"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total',
        ]"
        @page-change="handlePageChange"
      />
              </div>
          </xyy-panel>
      </xyy-dialog>
  </div>
</template>

<script>
import { productDataColumns } from '../config'
import { getRefundProductList } from '@/api/order/returnGoods'
import { pageConfig } from '@/utils';
export default {
  name: 'productSearch',
  data() {
      return {
          btnListTop: [
              {
                  label: "查询",
                  type: "primary",
                  clickEvent: this.searchList,
                  code: "",
                  plain: 'false',
                  icon: 'search-icon',
                  permission:'returnGoods:list:query',
              },
          ],
          formData: {
              productInfo: '',
              supplyCode: '',
              orgCode: '',
              orgName:'',
          },
          tablePage: pageConfig(),
          tableKey: 0,
          tableColumns: productDataColumns(),
          tableData: [],
          loading: false,
      }
  },
  mounted(){

  },
  methods: {
    // 查询列表
    searchList(flag) {
        this.loading = true
        if (!flag) {
            this.tablePage.pageNum = 1
        }
        const params = {
            pageNum: this.tablePage.pageNum,
            pageSize: this.tablePage.pageSize,
            productInfo: this.formData.productInfo,
            supplyCode: this.formData.supplyCode,
        }
        getRefundProductList(params).then(res => {
          if (res.code === 0) {
            this.tableData = res.result.list
            this.tablePage.total = res.result.total

          }else{
              this.$message.error(res.msg)
          }

        }).finally(() => {
            this.loading = false
            this.tableKey++
        })
    },
      //返回数据
      reuturnInfo({ row }) {
          this.$emit('echoData', row)
      },
      // 打开弹窗
      open(orgName) {
        this.tablePage.pageNum = 1
        this.tablePage.pageSize = 100
        this.tablePage.total = 0
        this.tableData = []
        this.formData.productInfo = ''
        this.formData.supplyCode = ''
        this.formData.orgName = orgName
        this.$refs.productSearch.open()
        this.searchList()


      },
      //关闭弹窗
      close() {
          this.$refs.productSearch.close()
      },
      // 分页器改变处理
      handlePageChange({currentPage, pageSize}) {
          this.tablePage.pageNum = currentPage;
          this.tablePage.pageSize = pageSize;
            this.searchList(true)
        },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper{
    height:230px !important;
}
</style>
