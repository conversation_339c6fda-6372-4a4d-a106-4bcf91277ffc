export function controlTabCol() {
  return [
    { field: "id", title: "控销组ID", width: 120 },
    { field: "name", title: "控销组名称", width: 120 },
    { field: "merchantNums", title: "控销药店", width: 120 },
    { field: "createTimeStr", title: "创建时间", width: 120 },
    { field: "updateTimeStr", title: "更新时间", width: 120 },
    { field: "operation", title: "操作", width: 120 },
  ];
}

export function controlStoreCol() {
  return [
    { field: "merchantId", title: "药店ID", width: 120 },
    { field: "merchantName", title: "药店名称", width: 120 },
    // { field: "merchantMobile", title: "手机号", width: 120 },
  ];
}

export function controlProductCol() {
  return [
    { field: "skuId", title: "商品编码", width: 120 },
    { field: "showName", title: "商品展示名称", width: 120 },
    { field: "approvalNumber", title: "批准文号", width: 120 },
    { field: "manufacturer", title: "生产厂家", width: 120 },
    { field: "spec", title: "规格", width: 120 },
    { field: "statusName", title: "商品状态", width: 120 },
  ];
}

export function changeLogCol() {
  return [
    { field: "changeInfo", title: "变更内容", width: 120 },
    // { field: "changeType", title: "变更类型", width: 120 },
    { field: "createTime", title: "变更时间", width: 120 },
    { field: "updateUser", title: "操作人", width: 120 },
  ];
}

export function addCustomerCol() {
  return [
    { field: "buyerId", title: "药店ID", width: 120 },
    { field: "buyerName", title: "药店名称", width: 120 },
    // { field: "accountStatusDesc", title: "开户状态", width: 120 },
    // { field: "address", title: "店铺地址", width: 150 },
  ];
}
