export function listColumns(){
    return [
        { field: 'promotionId', title: '活动ID', width: 100 },
        { field: 'title', title: '活动名称', width: 200 },
        { field: 'activeTime', title: '活动时间', width: 220},
        { field: 'statusText', title: '活动状态', width: 100},
        { field: 'createTime', title: '创建时间', width: 220},
        { field: 'operation1', title: '操作', width: 200,fixed: "right"}
    ]
}

export function operationLogColumns(){
    return [
        { field: 'type', title: '变更类型', width: 80},
        { field: 'content', title: '变更内容', width: 300},
        { field: 'createName', title: '变更人', width: 80},
        { field: 'createTime', title: '操作时间', width: 100}
    ]
}
export function addTableColumns(){
    return [
        { field: 'erpProductCode', title: '本公司商品编码', width: 200 },
        { field: 'csuid', title: '商品编码', width: 200 },
        { field: 'url', title: '商品图片', width: 80, className: 'viewImg'},
        { field: 'productName', title: '商品名称/规格/厂家', width: 280,className: 'productName'},
        { field: 'dosageForm', title: '商品剂型', width: 150},
        { field: 'suggestPrice', title: '销售价（元）', width: 150},
        { field: 'accessPrice', title: '平台建议价（元）', width: 150,hit:['活动价需小于等于平台建议价']},
        { field: 'activityPrice', title: '活动价（元）', width: 230,className: 'hdInput'},
        { field: 'hx', title: '环线活动价（元）', width: 150},
        { field: 'stock', title: '商品库存', width: 150},
        { field: 'statusText', title: '商品状态', width: 150},
    ]
}
export function addColumns(){
  return [
    { field: 'erpProductCode', title: '本公司商品编码', width: 200 },
    { field: 'csuid', title: '商品编码', width: 200 },
    { field: 'url', title: '商品图片', width: 80,className: 'viewImg'},
    { field: 'productName', title: '商品名称/规格/厂家', width: 300, className: 'productName'},
    { field: 'dosageForm', title: '商品剂型', width: 150},
    { field: 'suggestPrice', title: '销售价（元）', width: 150},
    { field: 'hx', title: '环线活动价（元）', width: 220},
    { field: 'stock', title: '商品库存', width: 150},
    { field: 'statusText', title: '商品状态', width: 220},
  ]
}
