<template>
    <div>
        <el-dialog append-to-body ref="changeLog" :visible.sync="dialogVisible" :title="title" width="80%"
            :before-close="handleClose">
            <xyy-panel :titleShow='false'>
                <div :key="tabKey" class="table-box">
                    <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto"
                        :max-height="tableHeight" :data="tableData" :key="tableKey"
                        :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }"
                        :show-overflow="false">
                        <vxe-table-column type="seq" title="序号" width="80" />
                        <template>
                            <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                                :title="item.title" :min-width="item.width" :fixed="item.fixed">
                                <template v-slot="{ row }">
                                    <div v-if="item.field === 'changeInfo'">
                                        <div v-for="(change, index) in row.changeInfo" :key="index">
                                            {{ change }}
                                        </div>
                                    </div>
                                    <div v-else>
                                        {{ row[item.field] }}
                                    </div>
                                </template>
                            </vxe-table-column>
                        </template>
                    </vxe-table>
                </div>
                <div class="pager">
                    <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                        :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                            'PrevPage',
                            'JumpNumber',
                            'NextPage',
                            'FullJump',
                            'Sizes',
                            'Total',
                        ]" @page-change="handlePageChange" />
                </div>
            </xyy-panel>
        </el-dialog>
    </div>
</template>

<script>
import { queryLog } from '@/api/product/controlledStoreManagement';
import { pageConfig } from '@/utils';
import { changeLogCol } from '../config';
export default {
    name: 'changeLog',
    data() {
        return {
            dialogVisible: false, // 弹窗显示状态
            title: '', // 弹窗标题
            productCode: '', // 商品编码
            erpProductCode: '', // 商品ERP编码
            loading: false, // 加载
            tableData: [], // 表格数据
            tableColumns: changeLogCol(), // 表格列
            tabKey: 0, // 表格key
            tableKey: 0, // 表格key
            tablePage: pageConfig(), // 表格分页
            tableHeight: 400, // 表格高度
            rowData: {}, // 行数据
        }
    },
    methods: {
        loadData() {
            this.loading = true
            const params = {
                groupId: this.rowData.id,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
            }
            queryLog(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.tableData = result.list
                    this.tablePage.total = result.total
                } else {
                    this.$message.error(msg)
                }
            }).finally(() => {
                this.loading = false
                this.tableKey++
            })
        },
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage
            this.tablePage.pageSize = pageSize
            this.loadData()
        },
        open(data) {
            this.dialogVisible = true
            this.title = '变更记录'
            this.$nextTick(() => {
                this.rowData = data
                this.loadData()
            })
        },
        handleClose() {
            this.dialogVisible = false
        },
    },
}
</script>

<style lang="scss" scoped></style>