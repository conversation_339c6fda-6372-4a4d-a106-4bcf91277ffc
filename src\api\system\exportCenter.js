import request from '@/utils/request'

//获取导出文件列表
export function getExportList(query) {
    return request({
        url: '/export/getExportList',
        method: 'post',
        data: query
    })
}

//获取模块&菜单枚举
export function getModuleEnum() {
    return request({
        url: '/export/queryModuleMenus',
        method: 'post',
    })
}

//导出文件
//taskBean: '',
// colNameDesc: "",
// colName:"",
// moduleName:"",
// menuDesc:"",
// exportParams:""
export function exportFile(query) {
    return request({
        url: '/export/submitExportTask',
        method: 'post',
        data: query
    })
}

//下载文件
export function downloadFile(query) {
    return request({
        responseType: 'blob',
        url: '/export/download',
        method: 'get',
        params: query
    })
}