<template>
    <div class="app-container">
        <xyy-panel title="单据信息">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTop" />
            <el-form ref="form" :model="formData" :rules="rules" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="选择供货仓">
                            <el-select v-model="formData.orgName" placeholder="请选择" disabled>
                                <el-option v-for="item in storeList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="供货单号">
                            <el-input v-model="formData.supplyCode" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="创建日期">
                            <el-input v-model="formData.createTimeStr" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="单据状态">
                            <el-input v-model="formData.supplyStatusName" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="仓库联系方式">
                            <el-input v-model="formData.wmsContact" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="12" :md="12">
                        <el-form-item label="供货仓库地址">
                            <el-input v-model="formData.wmsAddress" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="创建人">
                            <el-input v-model="formData.createUser" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="供货商家">
                            <el-input v-model="formData.supplierName" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="下单人/联系电话">
                            <el-input v-model="formData.buyerContact" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="12" :md="12">
                        <el-form-item label="供货商地址">
                            <el-input v-model="formData.supplierAddress" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="选择配送方式" prop="deliveryMethod">
                            <el-select v-model="formData.deliveryMethod" placeholder="请选择" clearable>
                                <el-option v-for="item in ways" :key="item.code" :label="item.name" :value="item.code">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="填写出库单号" prop="businessOutCode">
                            <el-input v-model="formData.businessOutCode" maxlength="50" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <vxe-table ref="xTableAmount" :loading="loading" highlight-current-row height="auto" :data="tableAmountData"
                style="margin-top:10px;width: 1000px;height: 95px;">
                <vxe-table-column title="汇总" width="80" />
                <vxe-table-column v-for="item in addAmountColumns" :key="item.field" :field="item.field"
                    :title="item.title" :width="item.width" />
            </vxe-table>
        </xyy-panel>
        <xyy-panel title="供货商品列表">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTabale" style="float:right !important" />
            <div id="table_content">
                <div :key="tabKey" class="table-box">
                    <vxe-table ref="xTable" :loading="loading" highlight-current-row height="400px"
                        :row-style="rowStyle" :data="tableData" :key="tableKey" @cell-click="cellClickEvent"
                        @checkbox-change="selectChangeEvent" :checkbox-config="{ highlight: true }">
                        <vxe-table-column type="checkbox" width="80" fixed="left" />
                        <vxe-table-column v-for="item in tableColumns" :key="item.title" :field="item.field" :fixed="item.fixed"
                            :title="item.title" :width="item.width">
                            <template v-slot:default="{ row }">
                                <div v-if="item.field === 'supplyCount'">
                                    <el-input v-model="row[item.field]" @input="orderNumInput($event, row)"
                                        @change="computeTotalAmount(row)"></el-input>
                                </div>
                                <div v-else-if="item.field === 'supplyPrice'">
                                    <el-input v-model="row[item.field]" @input="priceInput($event, row)"
                                        @change="computeTotalAmount(row)"></el-input>
                                </div>
                                <div v-else-if="item.field === 'remark'">
                                    <el-input maxlength="200" show-word-limit placeholder="不超过200字"
                                        v-model="row[item.field]"></el-input>
                                </div>
                                <div v-else>
                                    {{ row[item.field] }}
                                </div>
                            </template>
                        </vxe-table-column>
                    </vxe-table>
                </div>
                <div>
                    <div id="total">
                        <div id="total_content">
                            价税合计：
                            <span>{{ `￥${totalTaxValue}` }}</span>
                        </div>
                        <!-- <div id="total_content">
                        金额合计：
                        <span>{{ totalAmount }}</span>
                    </div> -->
                        <!-- <div id="total_content">
                        税额合计：
                        <span>{{ totalTaxAmount }}</span>
                    </div> -->
                    </div>
                </div>
            </div>
        </xyy-panel>

    </div>
</template>
<script>
import {
    getSupplyGoodsDetail,
    getSupplyGoodsDelivery,
    confirmSupplyGoods
} from "@/api/order/supplyGoods"
import { confirmAmountColumns, confirmColumns } from "./config";
export default {
    name: "OrderSupplierConfirm",
    data() {
        return {
            storeList: [],
            btnListTop: [
                {
                    label: "确认发货",
                    type: "primary",
                    clickEvent: this.confirmShipment,
                    loading: false,
                    code: "",
                    plain: 'false',
                    // icon: 'search-icon',
                    permission: 'supplier:confirm:submit'
                },
                {
                    label: "返回",
                    clickEvent: this.backToPage,
                    code: "",
                    plain: 'false',
                    // icon: 'search-icon',
                    permission: 'supplier:confirm:back'
                },
            ],
            btnListTabale: [
                // { 
                //     label: "新增",
                //     type: "primary",
                //     clickEvent: this.searchList,
                //     code: "",
                //     icon: 'search-icon',
                //     permission: 'supplier:confirm:add'
                // },
                {
                    label: "删除",
                    type: "danger",
                    clickEvent: this.deleteRow,
                    permission: 'supplier:confirm:delete'
                },
            ],
            formData: {
                supplyCode: this.$route.query.supplyCode,
                orgCode: '',
                orgName: '',
                createTime: "",
                supplyStatusName: '',
                deliveryMethodName: '',
                wmsLinkman: '',
                wmsPhone: '',
                wmsContact: '',
                wmsAddress: '',
                buyerName: '',
                buyerPhone: '',
                buyerContact: '',
                supplierCode: '',
                supplierName: '',
                supplierAddress: '',
                deliveryMethod: '',
                businessOutCode: '',
                orderCode: ''
            },
            tableAmountData: [],
            addAmountColumns: confirmAmountColumns(),
            tableColumns: confirmColumns(),
            tableData: [],
            rules: {
                deliveryMethod: [
                    { required: true, message: '请选择配送方式', trigger: 'blur' },
                ],
                businessOutCode: [
                    { required: true, message: '请填写出库单号', trigger: 'blur' },
                ],
            },
            loading: false,
            tabKey: 0,
            totalTaxValue: 0,   //价税合计
            totalAmount: 0, //金额合计
            totalTaxAmount: 0,   //税额合计
            currentRow: {}, //当前行数据
            ways: [],
            tableKey: 0,
        }
    },
    watch: {
        tableData: {
            handler(newVal, oldVal) {
                this.computeAmount()
            },
            deep: true
        },
    },
    activated() {
        if (localStorage.getItem('yz')) {
            if (localStorage.getItem('yz') != this.$route.query.supplyCode) {
                this.apiGetSupplyGoodsDetail()
                localStorage.setItem('yz', this.$route.query.supplyCode)
            }
        } else {
            localStorage.setItem('yz', this.$route.query.supplyCode)
        }
    },
    destroyed() {
        localStorage.removeItem('yz')
    },
    mounted() {
        this.apiGetSupplyGoodsDelivery()
        this.apiGetSupplyGoodsDetail()
        this.$nextTick(() => {
            // utils.pageActivated()
            this.tabKey++
            this.computeAmount()
        })
    },

    methods: {
        //删除行
        deleteRow() {
            const selectRows = this.$refs.xTable.getSelectRecords()
            if (selectRows.length === 0) {
                this.$message.warning('请选择要删除的行')
                return
            }
            if (this.tableData.length === 1) {
                this.$message({
                    message: '仅有一行商品无法删除，辛苦您取消订单。',
                    type: 'warning'
                })
                return
            }
            if (selectRows.length == this.tableData.length) {
                this.$message({
                    message: '仅有一行商品无法删除，辛苦您取消订单。',
                    type: 'warning'
                })
                return
            }
            this.$confirm('确定删除此商品吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                selectRows.forEach(item => {
                    const index = this.tableData.findIndex(row => row.id === item.id)
                    this.tableData.splice(index, 1)
                })
            })
        },
        //确认发货
        confirmShipment() {
            for (let i = 0; i < this.tableData.length; i++) {
                if ((!this.tableData[i].supplyCount || this.tableData[i].supplyCount <= 0) || (!this.tableData[i].supplyPrice || this.tableData[i].supplyPrice <= 0)) {
                    this.$message.warning(`请完整填写第${i + 1}行商品${this.tableData[i].productName}的供货数量与供货单价`)
                    return
                }
                // if (!this.tableData[i].remark) {
                //     this.$message.warning(`请填写第${i + 1}行商品${this.tableData[i].productName}的备注信息`)
                //     return
                // }
            }
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    this.$confirm('请检查供货信息与配送信息是否填写正确，确认无误后提交！', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.btnListTop.find(item => item.label == '确认发货').loading = true
                        const Loading = this.$loading({
                            lock: true,
                            text: '正在提交数据……',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)',
                        })
                        const params = {
                            supplyCode: this.$route.query.supplyCode,
                            orgCode: this.formData.orgCode,
                            deliveryMethod: this.formData.deliveryMethod,
                            businessOutCode: this.formData.businessOutCode,
                            detailList: this.tableData
                        }
                        confirmSupplyGoods(params).then(res => {
                            const { msg, code } = res
                            if (code === 0) {
                                this.$message({
                                    type: 'success',
                                    message: '确认成功'
                                })
                                this.btnListTop.find(item => item.label == '确认发货').loading = false
                                Loading.close()
                                this.backToPage()
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: msg
                                })
                                this.btnListTop.find(item => item.label == '确认发货').loading = false
                                Loading.close()
                            }
                        }).catch(err => {
                            this.btnListTop.find(item => item.label == '确认发货').loading = false
                            Loading.close()
                        })
                    }).catch(() => {
                    })
                }
            })
        },
        // 获取配送方式
        apiGetSupplyGoodsDelivery() {
            getSupplyGoodsDelivery().then(res => {
                const { msg, code, result } = res
                if (code === 0) {
                    this.ways = result
                } else {
                    this.$message.error(msg)
                }
            })
        },
        // 获取订单详情
        apiGetSupplyGoodsDetail() {
            const params = {
                supplyCode: this.$route.query.supplyCode,
            }
            getSupplyGoodsDetail(params).then(res => {
                const { msg, code, result } = res
                if (code === 0) {
                    this.formData = Object.assign({}, result)
                    this.formData.wmsContact = result.wmsLinkman + '/' + result.wmsPhone
                    this.formData.buyerContact = result.buyerName + '/' + result.buyerPhone
                    delete this.formData.detailList
                    result.detailList.forEach(item => {
                        item.taxRateDesc = item.taxRate + '%'
                    })
                    this.tableData = result.detailList
                    this.tableAmountData = [
                        {
                            productTypeCount: result.productTypeCount,
                            supplyCount: result.supplyCount,
                            supplyAmount: result.supplyAmount,
                            storageCount: result.storageCount,
                            storageAmount: result.storageAmount
                        }
                    ]
                } else {
                    this.$message.error(msg)
                }
            })
        },
        //计算总额
        computeAmount() {
            let sumTotalTaxValue = 0
            // let sumTotalAmount = 0
            // let sumTotalTaxAmount = 0
            let sumRealSupplyCount = 0
            let sumRealSupplyAmount = 0
            this.tableData.forEach(item => {
                if (item.supplyPrice && item.supplyCount) {
                    // sumTotalAmount += Number(item.supplyCount) * Number(item.supplyPrice)
                    // sumTotalTaxAmount += Number((Number(item.supplyCount) * Number(item.supplyPrice) * Number(item.tax)))
                    sumTotalTaxValue += Number(item.supplyPrice) * Number(item.supplyCount)
                    sumRealSupplyCount += Number(item.supplyCount)
                    sumRealSupplyAmount += Number(item.supplyPrice) * Number(item.supplyCount)
                }
            })
            // sumTotalTaxValue = (sumTotalAmount ? sumTotalAmount : 0) + (sumTotalTaxAmount ? sumTotalTaxAmount : 0)
            this.totalTaxValue = sumTotalTaxValue.toFixed(2)
            this.$set(this.tableAmountData[0], 'realSupplyCount', sumRealSupplyCount)
            this.$set(this.tableAmountData[0], 'realSupplyAmount', sumRealSupplyAmount.toFixed(2))
            // this.totalAmount = sumTotalAmount.toFixed(2)
            // this.totalTaxAmount = sumTotalTaxAmount.toFixed(2)
        },
        //计算供货金额
        computeTotalAmount(row) {
            if (row.supplyCount && row.supplyPrice) {
                row.supplyAmount = (Number(row.supplyCount) * Number(row.supplyPrice)).toFixed(2)
            }
        },
        //校验供货单价
        priceInput(value, row) {
            // 使用正则表达式匹配数字和两位小数
            const reg = /^\d*(\.?\d{0,2})$/;
            if (reg.test(value)) {
                row.supplyPrice = value;
            } else {
                // 如果不匹配，去掉最后一个字符
                row.supplyPrice = value.slice(0, -1);
            }
        },
        //校验订单数量
        orderNumInput(value, row) {
            // 只保留数字，去除非数字字符
            let newValue = value.replace(/[^\d]/g, '');
            // 去除前导零
            newValue = newValue.replace(/^0+/, '');
            // 更新输入值
            row.supplyCount = newValue;
        },
        //获取当前行
        cellClickEvent({ row }) {
            this.currentRow = row
            this.$refs.xTable.toggleCheckboxRow(row)
        },
        // 选择框选中事件
        selectChangeEvent({ row }) {
            this.$refs.xTable.toggleCheckboxRow(row)
        },
        //返回
        backToPage() {
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
        },
        //高亮颜色
        rowStyle({ row }) {
            if ((!row.supplyCount || row.supplyCount <= 0) || (!row.supplyPrice || row.supplyPrice <= 0) || !row.remark) {
                return { background: '#EDFFEF' }
            } else {
                return {}
            }
        },
    }
}
</script>
<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper {
    height: 400px !important;
}

#table_content {
    display: flex;
    flex-direction: column;

    #total {
        display: flex;
        flex-direction: row;
        justify-content: end;

        #total_content {
            align-items: center;
            font-weight: bolder;
            margin: 10px;
        }
    }
}
</style>
