export function tableColumns(){
    return [
        { field: 'orderNo', title: '供货单号', width: 230 },
        { field: 'sourceNo', title: '业务单号', width: 230 },
        { field: 'orderTimeStr', title: '单据日期', width: 230 },
        { field: 'sourceTypeName', title: '单据类型', width: 230 },
        { field: 'orderCount', title: '实际入库数量', width: 230 },
        { field: 'realAmount', title: '实际入库金额（元）', width: 230 },
        { field: 'invoiceStatusName', title: '发票状态', width: 230 },
        { field: 'totalInvoiceAmount', title: '发票金额（元）', width: 230 },
        { field: 'enteredAmount', title: '已录入金额（元）', width: 230 },
        { field: 'waitEnterAmount', title: '待录入发票金额（元）', width: 230 },
        { field: 'taxNoList', title: '发票号', width: 230 },
        { field: 'waitWriteOffAmount', title: '待核销金额', width: 230 },
        { field: 'writeOffAmount', title: '已核销金额', width: 230 },
        { field: 'reason', title: '失败原因', width: 230 },
        { field: 'auditTimeStr', title: '审核通过时间', width: 230,},
        { field: 'operation', title: '操作', width: 230, fixed: 'right' },
    ]
}
// 发票台账明细-货单明细
export function detailTwoColumns(){
  return [
    { field: 'sourceNo', title: '业务单据编号', width: 220 },
    { field: 'productCode', title: '神农商品编码', width: 220 },
    { field: 'sourceTypeName', title: '单据类型', width: 250 },
    { field: 'productName', title: '商品名称', width: 250 },
    { field: 'batchCode', title: '商品批号', width: 250 },
    { field: 'spec', title: '规格', width: 250 },
    { field: 'productUnit', title: '单位', width: 250},
    { field: 'supplyPrice', title: '含税单价', width: 160 },
    { field: 'taxRate', title: '税率', width: 250},
    { field: 'realCount', title: '实际入库数量', width: 250},
    { field: 'noTaxAmount', title: '不含税金额', width: 180},
    { field: 'taxAmount', title: '税额', width: 200},
    { field: 'amount', title: '含税金额', width: 160},


  ]
}
export function invoiceColumns(){
    return [
        { field: 'taxNo', title: '发票号', width: 230, required: true },
        { field: 'invoiceTimeStr', title: '发票时间', width: 230, required: true },
        { field: 'auditNoTaxAmount', title: '发票不含税金额（元）', width: 230, required: true },
        { field: 'auditAmount', title: '发票含税金额（元）', width: 230, required: true },
        { field: 'taxRate', title: '税率', width: 230 },
        { field: 'auditTax', title: '税额', width: 230},
        { field: 'operation', title: '发票详情', width: 230 },
    ]
}

export function ordersColumns(){
    return [
        { field: 'orderNo', title: '供货单号', width: 230 },
        { field: 'sourceNo', title: '业务单号', width: 230 },
        { field: 'orderTimeStr', title: '单据日期', width: 230 },
        { field: 'sourceTypeName', title: '单据类型', width: 230 },
        { field: 'orderCount', title: '实际入库数量', width: 230 },
        { field: 'realAmount', title: '实际入库金额（元）', width: 230 },
        { field: 'waitEnterAmount', title: '待录入发票金额（元）', width: 230 },
        { field: 'auditAmount', title: '本次开票金额（元）', width: 230 },
    ]
}

export function addInvoiceColumns(){
    return [
        { field: 'taxNo', title: '发票号', width: 230, required: true },
        { field: 'invoiceTime', title: '发票时间', width: 230, required: true },
        { field: 'noTaxAmount', title: '发票不含税金额（元）', width: 230, required: true },
        { field: 'invoiceTax', title: '发票含税金额（元）', width: 230, required: true },
        { field: 'taxRate', title: '税率', width: 230},
        { field: 'auditTax', title: '税额', width: 230},
        { field: 'operation', title: '操作', width: 230 },
    ]
}

export function confirmOrderColumns() {
    return [
        { field: 'sourceNo', title: '业务单号', width: 230 },
        { field: 'orderNo', title: '供货单号', width: 230 },
        { field: 'orderTimeStr', title: '单据日期', width: 230 },
        { field: 'sourceTypeName', title: '单据类型', width: 230 },
        { field: 'orderCount', title: '实际入库数量', width: 230 },
        { field: 'realAmount', title: '实际入库金额（元）', width: 230 },
        { field: 'waitEnterAmount', title: '待录入发票金额（元）', width: 230 },
        { field: 'auditAmount', title: '本次开票金额（元）', width: 230 },
        { field: 'operation', title: '操作', width: 230, fixed: 'right' },
    ]
}

export function realInvoiceColumns(){
    return [
        { field: 'taxNo', title: '发票号', width: 230 },
        { field: 'invoiceTime', title: '发票时间', width: 230 },
        { field: 'amount', title: '发票含税总金额', width: 230 },
        { field: 'taxRate', title: '发票税率（%）', width: 230 },
        { field: 'operation', title: '上传发票', width: 230 },
    ]
}

export function selectInvoiceColumns(){
    return [
        { field: 'taxNo', title: '发票号', width: 160 },
        { field: 'invoiceTimeStr', title: '发票时间', width: 160 },
        { field: 'amount', title: '发票含税总金额（元）', width: 160 },
        { field: 'usedAmount', title: '已使用（元）', width: 160 },
        { field: 'unUsedAmount', title: '剩余未使用（元）', width: 160 },
        { field: 'operation', title: '操作', width: 160 },
    ]
}

export function refundDetailColumns(){
    return [
        { field: 'orderCode', title: '原单号', width: 230 },
        { field: 'originalOrderCode', title: '业务单号', width: 230 },
        { field: 'productName', title: '商品名称', width: 230 },
        { field: 'csuid', title: '商品编码', width: 230 },
        { field: 'batchCode', title: '商品批号', width: 230 },
        { field: 'taxRate', title: '税率', width: 150 },
        { field: 'supplyCount', title: '供货单数量', width: 230 },
        { field: 'storageCount', title: '实际入库数量', width: 230 },
        { field: 'supplyPrice', title: '入库单价（元）', width: 230 },
        { field: 'basePayPrice', title: '销售价/活动价（元）', width: 230 },
        { field: 'supplyPriceDifference', title: '供货价差金额（元）', width: 230 },
        { field: 'salesAmount', title: '有效销售/退货数量', width: 230 },
        { field: 'commercialRebate', title: '商业返利', width: 230 },
    ]
}