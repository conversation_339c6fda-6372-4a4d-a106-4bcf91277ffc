import request from '@/utils/request'

//供货单列表查询
export function getSupplyGoodsList(data) {
  return request({
    url: '/order/supplyOrder/queryList',
    method: 'post',
    data
  })
}

//供货单商品列表查询
export function getSupplyGoodsProductList(data) {
  return request({
    url: '/order/supplyOrder/queryProductList',
    method: 'post',
    data
  })
}

//供货单确认发货
export function confirmSupplyGoods(data) {
  return request({
    url: '/order/supplyOrder/confirmDelivery',
    method: 'post',
    data
  })
}

//供货单保存
export function saveSupplyGoods(data) {
  return request({
    url: '/order/supplyOrder/saveSupplyOrder',
    method: 'post',
    data
  })
}

//供货单修改三方出库单号
export function updateSupplyGoods(data) {
  return request({
    url: '/order/supplyOrder/updateBusinessOutCode',
    method: 'post',
    data
  })
}

//获取全部合作机构
export function getAllCooperation() {
  return request({
    url: '/order/base/getCooperationOrg',
    method: 'post'
  })
}

//获取供应商信息
export function getSupplierInfo(data) {
  return request({
    url: '/order/base/getBusiness',
    method: 'post',
    data
  })
}

//供货单取消
export function cancelSupplyGoods(data) {
  return request({
    url: '/order/supplyOrder/cancelSupplyOrder',
    method: 'post',
    data
  })
}

//供货单状态获取
export function getSupplyGoodsStatus() {
  return request({
    url: '/order/supplyOrder/getSupplyStatus',
    method: 'post'
  })
}

//供货单配送方式获取
export function getSupplyGoodsDelivery() {
  return request({
    url: '/order/supplyOrder/getSupplyDeliveryMethod',
    method: 'post'
  })
}

//供货单详情
export function getSupplyGoodsDetail(data) {
  return request({
    url: '/order/supplyOrder/viewSupplyOrder',
    method: 'post',
    data
  })
}

//获取供货单号
export function getSupplyGoodsNo() {
  return request({
    url: '/order/supplyOrder/getSupplyCode',
    method: 'post'
  })
}

//获取供应商信息
export function getBusiness() {
  return request({
    url: '/order/base/getBusiness',
    method: 'post',
  })
}

//获取全部合作机构
export function getAllCooperationOrg() {
  return request({
    url: '/order/base/getCooperationOrg',
    method: 'post'
  })
}

//获取商品信息
export function getGoodsInfo(data) {
  return request({
    url: '/product/query',
    method: 'post',
    data
  })
}

//供货单保存
export function saveSupplyGoodsInfo(data) {
  return request({
    url: '/order/supplyOrder/saveSupplyOrder',
    method: 'post',
    data
  })
}

//获取供货订单待确认发货数量
export function getSupplyOrderDeliveryCount(data) {
  return request({
    url: '/order/supplyOrder/queryList/status',
    method: 'post',
    data
  })
}

//获取供货单商品明细待确认发货数量
export function getSupplyOrderProductDeliveryCount(data) {
  return request({
    url: '/order/supplyOrder/queryProductList/status',
    method: 'post',
    data
  })
}

//获取仓对应的拦截品
export function getSupplyOrderProductInterceptCount(data) {
  return request({
    url: '/order/base/getOrgProductScope',
    method: 'post',
    data
  })
}
