<template>
    <div>
        <xyy-dialog :title='title' ref="platformMarkupRulesDialog" width='60%' :footerShow="false"  @on-close="onClose">
          <div v-if="type == 'common'">
            <xyy-panel :titleShow="false">
              <div  class="table-box">
                  <vxe-table ref="xTable"
                      :loading="loading"
                      highlight-current-row
                      height="500px"
                      :data="tableConfig.data"
                      >
                      <vxe-colgroup title="环线规则">
                        <vxe-column field="line" title="环线" width="100px"></vxe-column>
                        <vxe-column field="areaName" width="300px" title="省" align="left" show-overflow="break-word"></vxe-column>
                      </vxe-colgroup>
                      <vxe-colgroup title="商家加点规则">
                        <vxe-colgroup title="单体">
                          <vxe-column field="singleNormal" title="常规药品"></vxe-column>
                          <vxe-column field="singleParticle" title="颗粒"></vxe-column>
                          <vxe-column field="singleWeight" title="重水剂"></vxe-column>
                        </vxe-colgroup>
                        <vxe-colgroup title="连锁">
                          <vxe-column field="chainNormal" title="常规药品"></vxe-column>
                          <vxe-column field="chainParticle" title="颗粒"></vxe-column>
                          <vxe-column field="chainWeight" title="重水剂"></vxe-column>
                        </vxe-colgroup>
                      </vxe-colgroup>
                  </vxe-table>
              </div>
            </xyy-panel>
            <xyy-panel :titleShow="false">
              <div v-if="specialRate">
                <div>
                  特价活动加点系数:{{ specialRate }}
                </div>
              </div>
              <div v-if="pintuanRadio">
                <div>
                  团购活动加点系数:{{ pintuanRadio }}
                </div>
              </div>
              <div style="color: red;">
                  备注：特价及团购活动加点系数为在普通商品加点规则基础上折扣力度
              </div>
            </xyy-panel>
          </div>
          <div v-else-if="type === 'part'">
            <xyy-panel :titleShow="false">
                <div class="table-box">
                  <vxe-table ref="yTable"
                      :loading="yloading"
                      highlight-current-row
                      height="500px"
                      :data="partTableData"
                      >
                      <vxe-table-column type="seq" title="序号" width="80" />
                      <vxe-table-column v-for="item in partRuleTableColumn" :key="item.field" :field="item.field"
                        :title="item.title" :min-width="item.width" :fixed="item.fixed">
                        </vxe-table-column>
                    </vxe-table>
                </div>
            </xyy-panel>
            <xyy-panel :titleShow="false">
              <div style="color: red;">
                备注：这些商品重量重，售价低，正常加点规则无法覆盖运费，故需进行单独加点，请您供货时慎重选择品种，以防加点过高影响销售
              </div>
            </xyy-panel>
          </div>
        </xyy-dialog>
    </div>
</template>

<script>
import { basicLoop,getSpecialRate } from '@/api/product/addReduction'
import { partRuleTableColumn } from '../config'

export default {
  name: 'platformMarkupRulesDialog',
  components: {
  },
  data() {
    return {
      loading: false,
      specialRate:null,
      pintuanRadio:null,
      tableConfig: {
        data: [],
      },
      type: '',
      partRuleTableColumn: partRuleTableColumn(),
      yloading: false,
      partTableData: [],
      title: '',
    }
  },
  created() {
  },
  methods: {
    /**关闭弹窗 */
    onClose() {
      this.specialRate = null
      this.pintuanRadio = null
        this.$refs.platformMarkupRulesDialog.close()
        this.$emit('on-close')
    },
    /**打开弹窗 */
    open(type){
        this.type = type
        if(type == 'common'){
          this.title = '通用商品加价规则（在商家定价的基础上加点百分比）'
        }else if(type == 'part'){
          this.title = '部分商品加价规则（在商家定价的基础上加点百分比）'
        }
        this.queryList()
        this.$refs.platformMarkupRulesDialog.open()
    },
    handleFormSubmit() {
      this.queryList()
    },
    async queryList() {
      this.loading = true
      try {
        const res = await basicLoop();
        if (res.code == 0) {
          this.tableConfig.data = res.result
        } else {
          // this.$message.error(res.message || '获取列表失败')
        }
        const rateRes = await getSpecialRate();
        if (rateRes.code == 0) {
          this.specialRate = rateRes.result.tejiaRadio
          this.pintuanRadio = rateRes.result.pintuanRadio
          rateRes.result.customLoopPrices.forEach(item => {
            item.level0 = item.level0 + '%'
            item.level1 = item.level1 + '%'
            item.level2 = item.level2 + '%'
            item.level3 = item.level3 + '%'
            item.level4 = item.level4 + '%'
          })
          this.partTableData = rateRes.result.customLoopPrices
        } else {
           this.$message.error(res.msg || '获取列表失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
  }
}
</script>

<style scoped>

</style>
