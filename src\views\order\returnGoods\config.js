// 退货单管理列表
export function planColumns(){
    return [
        { field: 'orgName', title: '合作仓库', width: 220 },
        { field: 'refundCode', title: '退货单号', width: 160 },
        { field: 'refundTypeCount', title: '品种数', width: 160 },
        { field: 'supplyCode', title: '供货单号', width: 160},
        { field: 'businessOutCode', title: '本公司出库单号', width: 160 },
        { field: 'refundCount', title: '退货数量', width: 160},
        { field: 'refundAmount', title: '退货单金额', width: 160},
        { field: 'createTimeStr', title: '退单下单时间', width: 200},
        { field: 'outTimeStr', title: '退货出库时间', width: 160},
        { field: 'orderStatusName', title: '退单状态', width: 160},
        { field: 'deliveryMethodName', title: '退货方式', width: 160},
        { field: 'operation1', title: '操作', width: 200,fixed: "right"}
    ]
}
//新增退货单退货商品列表
export function returnColumns(){
    return [
        { field: 'orgName', title: '合作仓库', width: 220 },
        { field: 'erpProductCode', title: '本公司产品编码', width: 250 },
        { field: 'csuid', title: '商品编码', width: 250 },
        { field: 'productCode', title: '产品编码', width: 250},
        { field: 'standardProductId', title: '标准库id', width: 160 },
        { field: 'productName', title: '商品名称', width: 160},
        { field: 'supplyCode', title: '供货单号', width: 180},
        { field: 'storageCode', title: '入库单号', width: 200},
        { field: 'batchCode', title: '批号', width: 160},
        { field: 'skuCategory', title: '商品类别', width: 160},
        { field: 'spec', title: '规格', width: 200},
        { field: 'productUnit', title: '单位', width: 100},
        { field: 'manufacturer', title: '生产厂家', width: 250},
        { field: 'code', title: '69码', width: 160},
        { field: 'approvalNumber', title: '批准文号', width: 160},
        { field: 'mediumPackageNum', title: '中包装数', width: 160},
        { field: 'pieceLoading', title: '件包装数', width: 160},
        { field: 'refundStorageCount', title: '退货库可退数量', width: 160, hit:['所选品批下当前退货库内总可退数量，不区分批次'] },
        { field: 'maxRefundCount', title: '最大可退数量', width: 160, hit:['所选供货入库单当前在库库存，仅为所选入库单可退数是']},
        { field: 'refundCount', title: '退货数量', width: 160},
        { field: 'supplyPrice', title: '供货单价', width: 160},
        { field: 'refundAmount', title: '退货金额', width: 160},
        { field: 'taxRateName', title: '税率', width: 160},
    ]
}
//退货单详情退货商品列表
export function returnDetailColumns(){
  return [
      { field: 'orgName', title: '合作仓库', width: 220 },
      { field: 'erpProductCode', title: '本公司产品编码', width: 250 },
      { field: 'csuid', title: '商品编码', width: 250 },
      { field: 'productCode', title: '产品编码', width: 250},
      { field: 'standardProductId', title: '标准库id', width: 160 },
      { field: 'productName', title: '商品名称', width: 160},
      { field: 'supplyCode', title: '供货单号', width: 180},
      { field: 'storageCode', title: '入库单号', width: 200},
      { field: 'batchCode', title: '批号', width: 160},
      { field: 'skuCategory', title: '商品类别', width: 160},
      { field: 'spec', title: '规格', width: 200},
      { field: 'productUnit', title: '单位', width: 100},
      { field: 'manufacturer', title: '生产厂家', width: 250},
      { field: 'code', title: '69码', width: 160},
      { field: 'approvalNumber', title: '批准文号', width: 160},
      { field: 'mediumPackageNum', title: '中包装数', width: 160},
      { field: 'pieceLoading', title: '件包装数', width: 160},
      { field: 'refundStorageCount', title: '退货库可退数量', width: 160, hit:['所选品批下当前退货库内总可退数量，不区分批次'] },
      { field: 'maxRefundCount', title: '最大可退数量', width: 160, hit:['所选供货入库单当前在库库存，仅为所选入库单可退数是']},
      { field: 'refundCount', title: '退货数量', width: 160},
      { field: 'supplyPrice', title: '供货单价', width: 160},
      { field: 'refundAmount', title: '退货金额', width: 160},
      { field: 'realOutCount', title: '实际退货数量', width: 160},
      { field: 'realOutAmount', title: '实际退货金额', width: 160},
      { field: 'rateStr', title: '税率', width: 160},
  ]
}

//选择退货商品列表
export function productDataColumns() {
  return [
      { field: 'orgName', title: '下单仓', width: 220 },
      { field: 'supplyCode', title: '供货单号', width: 200 },
      { field: 'storageCode', title: '入库单号', width: 200 },
      { field: 'storeTime', title: '入库时间', width: 150 },
      { field: 'erpProductCode', title: '本公司商品编码', width: 150 },
      { field: 'csuid', title: '商品编码', width: 150 },
      { field: 'productCode', title: '产品编码', width: 150 },
      { field: 'productName', title: '商品名称', width: 150 },
      { field: 'batchCode', title: '批号', width: 160},
      { field: 'refundStorageCount', title: '退货库可退数量', width: 160, hit:['所选品批下当前退货库内总可退数量，不区分批次'] },
      { field: 'maxRefundCount', title: '最大可退数量', width: 160, hit:['所选供货入库单当前在库库存，仅为所选入库单可退数是']},
      { field: 'skuCategory', title: '商品类别', width: 150 },
      { field: 'spec', title: '规格', width: 200 },
      { field: 'productUnit', title: '单位', width: 100 },
      { field: 'manufacturer', title: '生产厂家', width: 220 },
      { field: 'code', title: '69码', width: 150 },
      { field: 'storageCount', title: '入库数量', width: 120 },
      { field: 'supplyPrice', title: '供货单价', width: 120 },
  ]
}
