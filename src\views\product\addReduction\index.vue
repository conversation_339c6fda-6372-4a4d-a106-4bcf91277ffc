<template>
<div class="app-container">
  <xyy-panel title='查询条件'>
    <btn-group slot="tools" :btn-list="btnListTop"/>
    <el-form ref="formData" :model="formData" label-width="120px">
        <el-row :gutter="20">
            <el-col :lg="6" :md="6">
                <el-form-item label="本公司编码" v-if="isShow">
                    <el-input v-model="formData.erpProductCode" placeholder="请输入本公司编码" />
                </el-form-item>
            </el-col>
            <el-col :lg="6" :md="6">
                <el-form-item label="商品编码" v-if="isShow">
                    <el-input v-model="formData.csuidStr" placeholder="请输入商品编码" />
                </el-form-item>
            </el-col>
            <el-col :lg="6" :md="6">
                <el-form-item label="批准文号" v-if="isShow">
                    <el-input v-model="formData.approvalNumber" placeholder="请输入批准文号" />
                </el-form-item>
            </el-col>
            <el-col :lg="6" :md="6">
                <el-form-item label="生成厂家" v-if="isShow">
                    <el-input v-model="formData.manufacturer" placeholder="请输入生成厂家" />
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :lg="6" :md="6">
                <el-form-item label="商品名称" v-if="isShow">
                    <el-input v-model="formData.productName" placeholder="请输入商品名称"/>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :lg="6" :md="6">
              <el-form-item label="商品来源" v-if="isShow">
                <el-checkbox v-model="formData.isOnePrice" @change="goodsResources">一口价商品</el-checkbox>
                <el-checkbox v-model="formData.isGroup" @change="goodsResources">拼团商品</el-checkbox>
              </el-form-item>
            </el-col>
        </el-row>
    </el-form>
  </xyy-panel>
  <xyy-panel :titleShow='false'>
    <div slot="tools" style="position: relative;">
      <div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane  name="shelvedProducts">
            <div slot="label">
              <span>已上架商品</span>
              <span v-if="tabs.shelvedProducts">({{tabs.shelvedProducts}})</span>
            </div>
          </el-tab-pane>
          <el-tab-pane name="pendingShelving">
            <div slot="label">
              <span>待上架商品</span>
              <span  v-if="tabs.pendingShelving">({{tabs.pendingShelving}})</span>
            </div>
          </el-tab-pane>
          <el-tab-pane name="deleteCount">
            <div slot="label">
              <span>已删除</span>
              <span  v-if="tabs.deleteCount">({{tabs.deleteCount}})</span>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div style="position: absolute;right: 0px;top: 0px;">
        <div style="display:flex;">
          <el-button type="primary" v-hasPermi="['product:addReduction:activitySettings']" @click="activitySettings()" :loading="activitySettingLoading">活动商品上下架设置</el-button>
          <el-button type="primary" v-if="activeName == 'shelvedProducts'" style="background: #00B955;border-color: #00B955;" v-hasPermi="['product:addReduction:pendingShelving']" @click="pendingChange({},2)">下架</el-button>
          <el-button type="primary" v-if="activeName == 'pendingShelving'" style="background: #00B955;border-color: #00B955;" v-hasPermi="['product:addReduction:shelvedProducts']"  @click="shelvedChange({},2)">上架</el-button>
          <el-button @click="handleToBePublished()" v-hasPermi="['product:addReduction:toBePublished']">
            <span>待发布商品</span>
            <span style="color:red;margin-left:5px;">{{tabs.toBePublished}}</span>
          </el-button>
          <el-dropdown trigger="click" style="margin-left: 12px;">
            <el-button v-hasPermi="['product:addReduction:editbusinessManagement']">控销管理</el-button>
            <el-dropdown-menu slot="dropdown" class="custom-dropdown-menu">
              <el-dropdown-item @click.native="businessManagement('store')">控销店铺管理</el-dropdown-item>
              <el-dropdown-item @click.native="businessManagement('circle')">控销商圈管理</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown trigger="click" style="margin-left: 12px;">
            <el-button v-hasPermi="['product:addReduction:platformMarkupRules']">平台加价规则</el-button>
            <el-dropdown-menu slot="dropdown" class="custom-dropdown-menu">
              <el-dropdown-item @click.native="platformMarkupRules('common')">通用商品加价规则</el-dropdown-item>
              <el-dropdown-item @click.native="platformMarkupRules('part')">部分商品加价规则</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
    <div  :key="tabKey" class="table-box">
      <vxe-table ref="xTable"
        :loading="loading"
        highlight-current-row
        :row-style="rowStyle"
        :data="tableData"
        :key="tableKey"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
        :show-overflow="false"
        >
        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
        <template >
          <vxe-table-column v-for="item in tableColumn" :key="item.field" :field="item.field" :title="item.title" :min-width="item.width" :fixed="item.fixed">
            <template #header>
              <div v-if="item.hit">
                <el-tooltip placement="top">
                  <template slot="content">
                    <div v-for="(it,index) in item.hit" :key="index">
                      <div>{{it}}</div>
                    </div>
                  </template>
                  <div>
                    <span>{{item.title}}</span>
                    <span>
                      <svg-icon  icon-class="prompt-icon"/>
                    </span>
                  </div>
                </el-tooltip>
              </div>
              <div v-else>
                {{item.title}}
              </div>
            </template>
            <template v-slot:default="slotProps">
              <!-- 本公司商品编码 -->
              <div v-if="item.field === 'erpProductCode'">
                <div v-if="slotProps.row.activityType == 1 || slotProps.row.activityType == 2">
                  {{ slotProps.row[item.field] }}
                </div>
                <div v-else>
                  <el-input v-model="slotProps.row[item.field]" @change="erpProductCodeChange(slotProps.row)" class="custom-input">
                    <svg-icon slot="suffix" icon-class="new-edit" style="cursor: pointer;"></svg-icon>
                  </el-input>
                </div>
              </div>
              <div v-else-if="item.field === 'csuid'">
                <div style="text-align: left;">csuid: <span>{{ slotProps.row.csuid }}</span></div>
                <div style="text-align: left;">编码: <span>{{ slotProps.row.productCode }}</span></div>
                <div v-if="slotProps.row.activityType == 1 || slotProps.row.activityType == 2"
                class="group-icon">拼团</div>
              </div>
              <!-- 商品名称/规格/生产厂家 -->
              <div v-else-if="item.field === 'productName'">
                <div style="text-align: left;">
                  <div class="clickWriting" >商品名称:<span @click="toYbm(slotProps.row)">{{slotProps.row.productName}}</span></div>
                  <div class="clickWriting" >规格:<span @click="toYbm(slotProps.row)">{{slotProps.row.spec}}</span></div>
                  <div class="clickWriting" >生产厂家:<span @click="toYbm(slotProps.row)">{{slotProps.row.manufacturer}}</span></div>
                  <div v-if="slotProps.row.activityType == 1" style="color: red;">钱多多自动上架拼团(同步一口价)</div>
                  <div v-else-if="slotProps.row.activityType == 2" style="color: red;">钱多多自动上架拼团(同步特价活动)</div>
                </div>
              </div>
              <!-- 拆零包装/单位 -->
              <div v-else-if="item.field === 'splitName'">
                <div>
                  <div>{{slotProps.row.splitName}}</div>
                  <div>
                    <span v-if="slotProps.row.isSplit == 1">{{slotProps.row.split}}</span>
                    <span v-else>{{slotProps.row.mediumPackageNum}}</span>
                    /
                    <span>{{slotProps.row.productUnit}}</span></div>
                </div>
              </div>
              <!-- 销售价 -->
              <div v-else-if="item.field === 'suggestPrice'">
                <div v-if="slotProps.row.activityType == 1 || slotProps.row.activityType == 2">
                  <div style="color: red; font-weight: bold; text-align: left;">
                    拼团价格：<span>{{slotProps.row.suggestPrice}}</span>
                  </div>
                  <div style="color: red; font-weight: bold; text-align: left;">
                    起拼数量： <span>{{slotProps.row.activityLeastNum}}</span>
                  </div>
                  <div style="color: red; font-weight: bold; text-align: left;">
                    最低订单价格：<span>{{slotProps.row.activityLeastPrice}}</span>
                  </div>
                </div>
                <div v-else>
                  <el-input :value="slotProps.row[item.field]" @change="suggestPriceChange(slotProps.row,slotProps.$rowIndex)" @input="suggestPriceInput($event,slotProps.row)" class="custom-input">
                    <svg-icon slot="suffix" icon-class="new-edit" style="cursor: pointer;"></svg-icon>
                  </el-input>
                </div>
              </div>
              <!-- 销售价排名 -->
              <div v-else-if="item.field === 'warehouseRank'">
                <div style="text-align: left;">
                  <div>当前仓排名:<span>{{slotProps.row.warehouseRank}}</span></div>
                  <div>全仓排名:<span>{{slotProps.row.allWarehouseRank}}</span></div>
                  <div>全仓最低价:<span>{{slotProps.row.allWarehouseLowestPrice}}</span></div>
                </div>
              </div>
              <!-- 参考价格 -->
              <div v-else-if="item.field === 'latestSupplyPrice'">
                <div style="text-align: left;">
                  <div>最新供货价:<span>{{slotProps.row.latestSupplyPrice}}</span></div>
                  <div v-if="slotProps.row.needShowPlatformPrice">平台推荐价:<span>{{slotProps.row.latestSupplyPrice}}</span></div>
                </div>
              </div>
              <!-- 环线价 -->
              <div v-else-if="item.field === 'createUser3'">
                <div>
                  <div class="clickWriting" @click="createUserLook(slotProps.row)">查看环线价</div>
                </div>
              </div>
              <!-- 活动描述 -->
               <div v-else-if="item.field === 'activeDesc'">
                  <div v-if="slotProps.row.activityType == 1 || slotProps.row.activityType == 2" style="text-align: left;">
                    <div>活动时间：</div>
                    <div>{{ slotProps.row.activityStartTimeStr }}</div>
                    <div>至</div>
                    <div>{{ slotProps.row.activityEndTimeStr }}</div>
                  </div>
                  <div v-else>
                    <div style="text-align: left;" v-if="slotProps.row.specialPrice">
                        <div>特价价格:<span>{{slotProps.row.specialPrice}}</span></div>
                        <div class="clickWriting" @click="createUserLook(slotProps.row,'special')">查看特价环线价</div>
                        <div style="color: red;">{{ slotProps.row.specialDesc }}</div>
                    </div>
                  </div>
               </div>
              <!-- 控销 -->
              <div v-else-if="item.field === 'uniform'">
                <div>
                  <el-switch v-model="slotProps.row.uniform" :active-value="1" :inactive-value="0"
                   v-if="slotProps.row.activityType != 1 && slotProps.row.activityType != 2" @change="uniformChange($event,slotProps.row)"></el-switch>
                </div>
              </div>
              <!-- 图片 -->
              <div v-else-if="item.field === 'imageUrl'">
                <div>
                  <div class="clickWriting" @click="imgLook(slotProps.row)">
                    <img v-if="slotProps.row.bigImgList" :src="slotProps.row.bigImgList[0]" class="img" style="width: 50px;height: 50px;"/>
                  </div>
                </div>
              </div>
              <!--操作按钮-->
              <div v-else-if="item.field === 'options'">
                <div v-if="slotProps.row.activityType == 1 || slotProps.row.activityType == 2 || activeName == 'deleteCount'">
                  <div style="color: #00B955; font-weight: bolder;">——</div>
                </div>
                <div v-else>
                  <div v-if="activeName == 'shelvedProducts'" style="margin-bottom: 12px;">
                    <a href="#" class="clickWriting" v-hasPermi="['product:addReduction:pendingShelving']"  @click="pendingChange(slotProps.row,1)">下架</a>
                  </div>
                  <div v-if="activeName == 'pendingShelving'" style="margin-bottom: 12px;">
                    <a href="#" class="clickWriting" v-hasPermi="['product:addReduction:shelvedProducts']" @click="shelvedChange(slotProps.row,1)">上架</a>
                  </div>
                  <div style="margin-bottom: 12px;">
                    <a href="#" class="clickWriting" v-hasPermi="['product:addReduction:editbusinessManagement']" v-if="slotProps.row.activityType == 0" @click="editbusinessManagement(slotProps.row)">控销商圈设置</a>
                  </div>
                  <div>
                    <a href="#" class="clickWriting" v-hasPermi="['product:addReduction:shoppingLimit']"  @click="handleShoppingLimit(slotProps.row)">起购限购设置</a>
                  </div>
                </div>
              </div>
              <div v-else>
                {{ slotProps.row[item.field] }}
              </div>
            </template>
          </vxe-table-column>
        </template>
      </vxe-table>
    </div>
    <div class="pager" >
      <vxe-pager
        border
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :page-sizes="tablePage.pageSizes"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total',
        ]"
        @page-change="handlePageChange"
      />
    </div>
    <el-image
      style="display: none;"
      :src="url"
      ref="imageRef"
      :preview-src-list="srcList">
    </el-image>
    <!-- 发布商品弹窗 -->
    <productList ref="productList" @search="search(1)" ></productList>
    <!-- 平台加价规则弹窗 -->
    <platformMarkupRulesDialog ref="platformMarkupRulesDialog"></platformMarkupRulesDialog>
    <!-- 查看环线价弹窗 -->
    <viewRingLinePrice ref="viewRingLinePrice" :viewRingLinePriceRow="viewRingLinePriceRow"></viewRingLinePrice>
    <!-- 修改销售价弹窗 -->
    <calcRankDiaglog ref="calcRankDiaglog" @onClose="calcRankClose" :rowIndex="rowIndex" :row="row" :calcRankRow="calcRankRow" />
    <!-- 控销商圈设置 -->
    <subordinateTeamOverview ref="subordinateTeamOverview" :row="subordinateTeamOverviewRow" @onDialogChange="onDialogChange"/>
    <!-- 起购限购设置 -->
    <shoppingLimit ref="shoppingLimit"></shoppingLimit>
  </xyy-panel>
</div>
</template>

<script>
import { pageConfig } from "@/utils";
import utils from "@/utils";
import {queryList,closeProductUniform,unShelf,putaway,getStatic,updateErpProductCode,calcRank,checkAccess}   from '@/api/product/addReduction'
import {shelvedProductsCol} from "./config"
import productList from "./components/productList.vue" //发布商品弹窗
import platformMarkupRulesDialog from "./components/platformMarkupRulesDialog.vue" //平台加价规则弹窗
import viewRingLinePrice from "./components/viewRingLinePrice.vue" //平台加价规则弹窗
import calcRankDiaglog from "./components/calcRankDiaglog.vue" //平台加价规则弹窗
// import selectBusinessCircle from "./components/selectBusinessCircle.vue" //选择商圈弹窗
import subordinateTeamOverview from "./components/subordinateTeamOverview.vue" //选择商圈弹窗
import shoppingLimit from "./components/shoppingLimit.vue"; //起购限购设置
import XEUtils from 'xe-utils'
export default {
  name: "ProductUpAndDown",
  components:{
    productList,
    platformMarkupRulesDialog,
    viewRingLinePrice,
    calcRankDiaglog,
    // selectBusinessCircle,
    subordinateTeamOverview,
    shoppingLimit
  },
  data() {
    return {
      subordinateTeamOverviewRow:undefined,//当前行进入控销商圈设置弹窗
      busAreaId:undefined,//业务商圈id
      saleType:undefined,//选中类型
      businessRow:undefined,//设置业务商圈选中行
      isShow:true,//是否展开
      viewRingLinePriceRow:undefined,//查看环线入参
      calcRankRow:undefined,//计算商品排名
      row:undefined,//计算商品排名输入当前行
      rowIndex:undefined,//计算商品排名当前行索引
      formData: {
          erpProductCode:'',//本商家编码
          csuidStr: '',//商品编码
          manufacturer: '',//生产厂家
          approvalNumber: '',//批准文号
          productName: '',//商品名称/通用名
          isOnePrice:false, //是否为一价
          isGroup:false,  //是否为拼团
      },
      tablePage: pageConfig(),
      tabKey:0,
      activeName:'shelvedProducts',
      tableColumn: shelvedProductsCol(),
      tableData: [], // 表格数据,
      btnListTop: [
          {
              label: "查询",
              type: "primary",
              clickEvent: ()=>this.searchHandler(1),
              code: "",
              plain: 'false',
              permission:'product:addReduction:search'

          },
          {
              label: "重置",
              clickEvent: this.refresh,
              code: "",
              plain: 'false',
              permission:'product:addReduction:refresh'
          },
          {
              label: "收起",
              clickEvent: ()=>{this.isShow = !this.isShow;this.tabKey++;},
              showForm:true,
              show:this.isShow,
          },
      ],
      currentRow: null, //选中数据
      loading:false,
      tabs:{
        shelvedProducts:null,
        pendingShelving:null,
        toBePublished:null,
        deleteCount:null,
      },
      srcList:[],//图片列表
      url:'',//查看图片
      tableKey:0,//两个表格切换刷新
      activityTypes: [],  //商品来源
      activitySettingLoading: false,  //活动商品上下架设置按钮遮罩
    };
  },
  mounted(){
    this.$nextTick(()=>{
      this.search(1)
    })
  },
  methods: {
    goodsResources(){
      if(this.formData.isGroup && this.formData.isOnePrice){
        this.activityTypes = [0, 1, 2]
        this.search(1)
      }else if(this.formData.isOnePrice){
        this.activityTypes = [0]
        this.search(1)
      }else if(this.formData.isGroup){
        this.activityTypes = [1, 2]
        this.search(1)
      }else {
        this.activityTypes = []
        this.search(1)
      }
    },
    // 起购限购设置
    handleShoppingLimit(row){
      this.$refs.shoppingLimit.open(row)
    },
    selectChange(row) {

    },
    searchHandler(){
      this.tablePage.pageNum = 1;
      this.search(1);
    },
    /**控销商圈设置 */
    onDialogChange() {
      // 刷新页面
      this.search();
    },
    /**销售价修改 */
    async suggestPriceChange(row,index){
      if(row.suggestPrice == '' || row.suggestPrice == undefined){
        this.$message.error('销售价不可为空');
        row.suggestPrice = row.oldSuggestPrice;
        return;
      }
      if(row.suggestPrice <= 0){
        this.$message.error('销售价不可小于0');
        row.suggestPrice = row.oldSuggestPrice;
        return
      }
      let params ={
        barcode:row.barcode,
        price:row.suggestPrice,
      }
      try{
        const res = await calcRank([params])
        if(res.code == 0){
          // row.oldSuggestPrice = row.suggestPrice;
          this.row = row;
          this.calcRankRow = res.result[0];
          this.rowIndex = index;
          this.$nextTick(()=>{
            this.$refs.calcRankDiaglog.open();
          })
        }else{
          this.$message.error(res.msg)
          row.suggestPrice = row.oldSuggestPrice;
        }
      }catch(err){
        row.suggestPrice = row.oldSuggestPrice;
      }
    },
    /**取消销售价的修改 */
    calcRankClose(index,flag){
      console.log();

      this.search()
    },
    /**销售价格输入 */
    suggestPriceInput(value,row){
      // 使用正则验证最多两位小数
      const decimalRegex = /^\d+(\.\d{0,2})?$/;
      // 检查输入值是否符合规则
      if (!decimalRegex.test(value)) {
        // 如果不符合规则，将值限制为最多两位小数并截取整数部分的前7位
        const trimmedValue = value
          .toString()
          .replace(/[^\d.]/g, '') // 去掉非数字和小数点的字符
          .replace(/(\..*?)\..*/g, '$1') // 保证只保留一个小数点
          .replace(/^0+(?!\.|$)/, '0') // 去掉多余的前导零
          .match(/^(\d{0,7})(\.\d{0,2})?/); // 截取符合规则的部分
        row.suggestPrice = trimmedValue
          ? trimmedValue[1] + (trimmedValue[2] || '') // 拼接整数部分和小数部分
          : 0; // 如果输入为空，则设置为0
      } else {
        // 输入符合规则时，直接赋值
        row.suggestPrice = value;
      }
    },
    /**本公司商品编码修改 */
    async erpProductCodeChange(row){
      let params ={
        id:row.id,
        erpProductCode:row.erpProductCode,
      }
      try{
        const res = await updateErpProductCode(params)
        console.log();

        console.log(res,'lwq');
        if(res.code == 0){
          this.$message({
            message: res.msg,
            type: 'success'
          });
          row.oldErpProductCode = row.erpProductCode;
          this.search()
        }else{
          row.erpProductCode = row.oldErpProductCode;
        }
      }catch(err){
        row.erpProductCode = row.oldErpProductCode;
      }
    },
    /**待发布商品 */
    handleToBePublished(){
      this.$refs.productList.open()
    },
    /**控销商圈设置 */
    editbusinessManagement(row){
      this.subordinateTeamOverviewRow = row;
      this.$refs.subordinateTeamOverview.open()
    },
    /**控销商圈管理 */
    businessManagement(type){
      if(type == 'circle') {
        this.$router.push('/product/BusinessManagement')
      } else {
        this.$router.push('/product/ControlledStoreManagement')
      }
    },
    /**平台加价规则 */
    platformMarkupRules(type){
      this.$refs.platformMarkupRulesDialog.open(type);
    },
    /**活动商品上下架设置 */
    async activitySettings(){
      try{
        this.activitySettingLoading = true;
        const res = await checkAccess()
        if(res.code == 0){
          this.activitySettingLoading = false;
          if(res.result == 0){
            this.$message.error('活动数据更新中，无法修改配置，请稍后再试')
            return;
          }
          this.$router.push('/product/activitySettings')
        }else {
          this.$message.error('活动数据更新中，无法修改配置，请稍后再试')
          this.activitySettingLoading = false;
        }
      }catch(err){
        console.log(err);
        this.activitySettingLoading = false;
      }
    },
    /**上架 */
    async shelvedChange(row,type){
      let idList = [];
      if(type == 1){
        idList[0] = row.id
      }else if(type == 2){
        let checkList = this.$refs.xTable.getCheckboxRecords();  //获取选中
        checkList = checkList.filter(item => item.activityType == 0) // 过滤掉活动商品
        idList = checkList.map(item=> item.id);
      }
      if(idList.length == 0){
        this.$message.warning('请选择非活动商品');
        return;
      }
      try{
        const res = await putaway({idList:idList})
        if(res.code === 0){
           this.$message({
            message: res.msg,
            type: 'success'
          });
          this.search(1)
        }else{
           this.$message.error(res.msg);
        }
      }catch(err){
        console.log(err);
      }
    },
    /**下架 */
    async pendingChange(row,type){
      let idList = [];
      if(type == 1){
        idList[0] = row.id
      }else if(type == 2){
        let checkList = this.$refs.xTable.getCheckboxRecords();  //获取选中
        checkList = checkList.filter(item => item.activityType == 0) // 过滤掉活动商品
        idList = checkList.map(item=> item.id);
      }
      if(idList.length == 0){
        this.$message.warning('请选择非活动商品');
        return;
      }
      try{
        const res = await unShelf({idList:idList})
        if(res.code === 0){
           this.$message({
            message: res.msg,
            type: 'success'
          });
          this.search(1)
        }else{
           this.$message.error(res.msg);
        }
      }catch(err){
        console.log(err);
      }
    },
    /**控销按钮切换 */
    async uniformChange(val,row){
      console.log(val,row);
      if(val === 1){
        this.editbusinessManagement(row)
      }else if(val === 0){
        let params = {
          id:row.id
        }
        try{
          const res = await closeProductUniform(params)
          if(res.code === 0){
             this.$message({
              message: res.msg,
              type: 'success'
            });
            this.search();
          }else{
             this.$message.error(res.msg);
             row.uniform = 1;
             this.search();
          }
        }catch(err){
          row.uniform = 1
          console.log(e)
          this.search();
        }
      }
    },
    /**查看图片 */
    imgLook(row){
      if (row.bigImgList) {
        this.srcList = row.bigImgList;
        this.url = this.srcList[0] || '';
        this.$nextTick(() => {
          this.$refs.imageRef.showViewer = true; // 触发预览
        });
      } else {
        this.srcList = [];
        this.url = '';
        this.$message.error('没有图片');
      }
    },
    /**查看环线价 */
    createUserLook(row,type){
      this.viewRingLinePriceRow = {...row};
      this.$refs.viewRingLinePrice.open(type);
    },
    /**点击商品名称/规格/生产厂家跳转药帮忙 */
    toYbm(row){
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        window.open(`https://new-www.test.ybm100.com/search/skuDetail/${row.csuid}.htm`);
      }else if(process.env.NODE_ENV === 'staging'){
        window.open(`https://www.ybm100.com//search/skuDetail/${row.csuid}.htm`);
      }else if(process.env.NODE_ENV === 'production'){
        window.open(`https://www.ybm100.com//search/skuDetail/${row.csuid}.htm`);
      }
    },
    // 动态设置行高
    rowStyle({ row, rowIndex }) {
      return {
        height:'80px !important',
      }; // 统一设置行高为 60px
    },
    /**点击tabs栏 */
    handleClick(tab, event) {
      // this.tableKey++;
      // this.tabKey++;
      this.tablePage.pageNum = 1;
      this.tablePage.pageSize = 20;
      this.search(1);
    },
    // 分页器改变处理
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNum = currentPage;
      this.tablePage.pageSize = pageSize;
      this.search();
    },
    /**单选 */
    selectChangeEvent({ checked, records }){
    },
    /**全选 */
    selectAllEvent({ checked, records }){
      console.log(111);
    },
    /**重置 */
    refresh(){
      Object.keys(this.formData).forEach(item=>{
        this.formData[item] = '';
      })
      this.activityTypes = [];
      this.tablePage.pageNum = 1;
      this.tablePage.pageSize = 20;
      this.search()
    },
    /**查询 */
    async search(flag){
      let params ={
        ...this.formData,
        ...this.tablePage,
        type:this.activeName == 'shelvedProducts' ? 1 : this.activeName == 'pendingShelving' ? 0 : 20,
        activityTypes: this.activityTypes.length ? this.activityTypes : null
      }
      delete params.total
      this.loading = true
      try{
        const res = await queryList(params);
        if (res.code === 0) {
          this.loading = false
          this.tableData = res.result.list;
          if(this.tableData){
            this.tableData.forEach(item=>{
              item.oldErpProductCode = item.erpProductCode;
              item.oldSuggestPrice = item.suggestPrice;
              item.needShowPlatformPrice = true
              if(item.activityType == 1 || item.activityType == 2){
                item.activityEndTimeStr = XEUtils.toDateString(item.activityEndTime, 'yyyy-MM-dd HH:mm:ss')
                item.activityStartTimeStr = XEUtils.toDateString(item.activityStartTime, 'yyyy-MM-dd HH:mm:ss')
                if(item.latestSupplyPrice < item.suggestPrice){
                  item.needShowPlatformPrice = false
                }
              }
            })
          }
          this.tablePage.total = res.result.total // 总数据数量
          this.tableKey++;
        }
        if(flag){
          try{
            const res = await getStatic();
            if (res.code === 0) {
              this.tabs.shelvedProducts = res.result.shelfCount;
              this.tabs.pendingShelving = res.result.waitShelfCount;
              this.tabs.toBePublished = res.result.waitPublishCount;
              this.tabs.deleteCount = res.result.deleteCount;
            }
          }catch (error) {
            // this.$message.error(error);
            this.loading = false
            this.tableData = []
          }
        }
      }catch (error) {
        // this.$message.error(error);
        this.loading = false
        this.tableData = []
      }
      this.$nextTick(() => {
        this.$refs.xTable.recalculate(true)
      })
    },
    handleClose(){

    },
    goTarget(href) {
      window.open(href, "_blank");
    },
  },
};
</script>

<style scoped lang="scss">
.custom-input::v-deep .el-input__suffix {
  display: flex;
  align-items: center; /* 垂直居中 */
}

.group-icon{
  background: #00B955;
  color: #fff;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 12px;
  width: 40px;
  text-align: center;
}

.custom-dropdown-menu{

  ::v-deep .el-dropdown-menu__item:hover {
  background-color: #00B955 !important; /* 替换为你想要的背景颜色 */
  color: #FFFFFF !important;             /* 替换为你想要的字体颜色 */
  }
}

// ::v-deep .vxe-body--column{
//   max-height: 100px !important;
// }
// ::v-deep .vxe-cell{
//   max-height: 100px !important;
// }
</style>