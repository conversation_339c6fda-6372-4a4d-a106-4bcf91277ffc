<template>
  <el-dialog
    title="创建子账户"
    :visible.sync="dialogVisible"
    width="40%"
    :before-close="cancel"
    :close-on-click-modal="false"
  >
    <xyy-panel :titleShow="false" style="overflow: auto; max-height: 600px;">
      <div>
        商户名称：
        <el-input v-model="row.name" disabled style="width: 70%;"></el-input>
      </div>
      <div style="margin-top: 10px;">
        <span  class="clickWriting" style="float: right;" @click="addChild">新增</span>
        <childCount v-for="(item,index) in formData" :key="index" :title="index" :data="item"  ref="childRefs" @resetPwd="resetPwd"></childCount>
      </div>
    </xyy-panel>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </span>

  </el-dialog>
</template>
<script>
import childCount from "./childCount.vue";
import {addChildAccount,getChildAccountList, resetChildAccountPassword} from "@/api/system/tenant";
export default {
  name: "AddChild",

  components: {
    childCount
  },
  props: {

  },
  data() {
    return {
      dialogVisible: false,
      row: {},
      formData:[]
    };
  },
  methods: {
    resetPwd(contactMobile, contactName){
      this.$confirm(`
      <p>重置密码后</p>
      <p>联系人：${contactName}</p>
      <p>账号：${contactMobile}</p>
      <p>密码初始化为123456，确认是否重置？</p>
      `, '提示', {
        confirmButtonText: '重置',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true
      }).then(() => {
        const params = {
          userName: contactMobile,
        }
        resetChildAccountPassword(params).then(res => {
          const { code, msg } = res;
          if(code == 200){
            this.$message.success(msg);
          }else {
            this.$message.error(msg);
          }
        })
      }).catch(() => {
      })
    },
    cancel() {
      this.dialogVisible = false;
      this.formData = [];
    },
    open(row , data) {
      this.row = row;
      this.dialogVisible = true;
      getChildAccountList({tenantId:row.id}).then(res=>{
        if(res.code == 200){
          this.formData = res.data?.map(item => {
            item.disabled = true;
            item.contactName = item.nickName
            item.contactMobile = item.userName;
            return item;
          });
          if (data) {
            // console.log(111);

            this.formData.push({
              contactName:data.contactName,
              contactMobile:data.contactMobile,
              status:data.status
            });
          }
          // this.formData = res.data;
        }else{
          this.$message.error(res.msg);
        }

      })

    },
    addChild(){
      this.formData.push({});
    },
    async submitForm() {
      let isValid = true;
      const childRefs = this.$refs.childRefs;

      if (Array.isArray(childRefs)) {
        // 触发所有子组件的 validate 方法，并等待校验结果
        const validationResults = await Promise.all(childRefs.map((child) => child.validate()));
        isValid = validationResults.every((result) => result);
      }

      if (isValid) {
        const params =  this.formData.filter(item => !item.disabled).map(item => {
          return {
            ...item,
            tenantId: this.row.id
          }
        });
        addChildAccount(params).then(res=>{
          if(res.code == 200){
            this.$message.success("子账户创建成功");
            this.dialogVisible = false;
            this.formData = [];
            this.$emit("success",res);
          }else{
            this.$message.error(res.msg);
          }
        })

      } else {
        console.log("子账户校验失败");
      }
    },
  },
};
</script>
<style scoped lang="scss">
</style>
