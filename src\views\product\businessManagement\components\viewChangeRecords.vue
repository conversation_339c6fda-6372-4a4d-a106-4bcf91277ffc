<template>
  <div>
    <xyy-dialog
      ref="changeRecordsVis"
      :width="dialogWidth"
      title="商圈变更记录"
      height="300"
      @on-close="handleDialogClose"
    >
      <xyy-panel :titleShow="false">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          stripe
          border
          height="400"
          highlight-current-row
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
        >
          <el-table-column label="变更内容">
            <template slot-scope="{row}">
              <p
                v-for="(item, index) in row.changeInfo"
                :key="index"
              >
                {{ item }}
              </p>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="productName"
            label="变更类型"
          /> -->
          <el-table-column
            prop="createTime"
            label="变更时间"
            :formatter="formatDate"
          />
          <el-table-column
            prop="createName"
            label="操作人"
          />
        </el-table>
          <div class="pager" >
              <vxe-pager
                  border
                  :current-page="tablePage.pageNum"
                  :page-size="tablePage.pageSize"
                  :total="tablePage.total"
                  :page-sizes="tablePage.pageSizes"
                  :layouts="[
                  'PrevPage',
                  'JumpNumber',
                  'NextPage',
                  'FullJump',
                  'Sizes',
                  'Total',
                  ]"
                  @page-change="handlePageChange"
              />
          </div>
      </xyy-panel>
      <span slot='footer' class='dialog-footer'>
          <el-button type='primary' size='small' @click='sureBtn' >确 定</el-button>
      </span>
    </xyy-dialog>
  </div>
</template>

<script>
import { pageConfig } from '@/utils';
import {getLogs}   from '@/api/product/businessManagement';

export default {
  name: 'ViewChangeRecords',
  model: {
    prop: 'viewProductDialog',
    event: 'onDialogViewProduct',
  },
  props: {
    viewProductDialog: Boolean,
    row: Object,
  },
  data() {
    return {
      show: false,
      dialogWidth: '60%',
      dialogVisibl: false,
      tableData: [], // 弹窗表格
      rowIndex: '',
      pageSizes: [10, 20, 30, 40],
      tablePage: pageConfig(),
      busAreaId: '',
      isLoading: false, // 加载
      ruleForm: {
        tempName: '',
        templateType: '',
        tempStatus: '',
        branchCode: null,
        startTime: '',
      },
    };
  },
  mounted() {
  },
  methods: {
    formatDate(row, column, cellValue) {
      const date = new Date(cellValue);
      const y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? `0${MM}` : MM;
      let d = date.getDate();
      d = d < 10 ? `0${d}` : d;
      let h = date.getHours();
      h = h < 10 ? `0${h}` : h;
      let m = date.getMinutes();
      m = m < 10 ? `0${m}` : m;
      let s = date.getSeconds();
      s = s < 10 ? `0${s}` : s;
      return `${y}-${MM}-${d} ${h}:${m}:${s}`;
    },
    open() {
      this.$refs.changeRecordsVis.open()
      this.$nextTick(()=>{
        this.busAreaId = this.row.id; // 业务商圈ID
        this.searchList(); // 初始化列表
      })
    },
    handleDialogClose() {
        this.$refs.changeRecordsVis.close()
        this.$emit('on-close')
    },
    // 查询
    async searchList() {
      const params = { busId: this.busAreaId};
      Object.assign(params, { pageNum: this.tablePage.pageNum, pageSize: this.tablePage.pageSize });
      this.isLoading = true;
      try{
        const res = await getLogs(params)
        if (res.code === 0) {
          this.isLoading = false;
          this.tableData = res.result.list;
          this.tablePage.total = res.result.total; // 总数据数量
          this.tablePage.pageNum = res.result.pageNum;
        }else{
          this.$message.error(res.msg)
        }
      }catch(error){
        
      }
    },
    // 分页器改变处理
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNum = currentPage;
      this.tablePage.pageSize = pageSize;
      this.searchList();
    },
    sureBtn() {
      this.handleDialogClose();
    },
  },
};
</script>

<style scoped>
::v-deep.el-dialog__header {
  background-color: #f8f8ff;
}
</style>
