<template>
  <div>
      <xyy-dialog key="dialog1" ref="productSearch" title="添加商品" width="80%" :footerShow="false">
          <xyy-panel title="查询条件">
              <!-- 按钮组 start-->
              <btn-group slot="tools" :btn-list="btnListTop" />
              <el-form :model="formData" ref="formData" label-width="120px" class="clearfix">
                  <el-row >
                      <el-col :lg="6" :md="6">
                          <el-form-item label="本公司商品编码" prop="erpProductCode">
                              <el-input v-model.trim="formData.erpProductCode" placeholder="请输入"
                                  clearable></el-input>
                          </el-form-item>
                      </el-col>
                      <el-col :lg="6" :md="6">
                          <el-form-item label="商品编码" prop="csuid">
                              <el-input v-model.trim="formData.csuid" placeholder="请输入"
                                  clearable @input="handleInput"></el-input>
                          </el-form-item>
                      </el-col>
                      <el-col :lg="6" :md="6">
                          <el-form-item label="商品名称" prop="productName">
                              <el-input v-model.trim="formData.productName" placeholder="请输入"
                                  clearable></el-input>
                          </el-form-item>
                      </el-col>
                      <el-col :lg="6" :md="6">
                          <el-form-item label="商品状态" prop="type">
                              <el-select v-model="formData.type" placeholder="请选择" clearable>
                                <el-option label="全部" value=""></el-option>
                                <el-option label="已上架" :value="1"></el-option>
                                <el-option label="待上架" :value="0"></el-option>
                              </el-select>
                          </el-form-item>
                      </el-col>
                      </el-row>
              </el-form>
          </xyy-panel>
          <xyy-panel title="商品列表">
              <vxe-table ref="xTable" :loading="loading" highlight-current-row :data="tableData" empty-text="暂未查询到匹配的记录"
                  :row-style="rowStyle"
                  @checkbox-all="selectAllEvent"
                  @cell-click="cellClickEvent"
                  @checkbox-change="selectChangeEvent">
                  <vxe-table-column type="checkbox" width="80" />
                  <template>
                      <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                          :title="item.title" :width="item.width" :fixed="item.fixed">
                          <template #header>
                      <div v-if="item.hit">
                        <el-tooltip placement="top">
                          <template slot="content">
                            <div v-for="(it,index) in item.hit" :key="index">
                              <div>{{it}}</div>
                            </div>
                          </template>
                          <div>
                            <span>{{item.title}}</span>
                            <span>
                              <svg-icon  icon-class="prompt-icon"/>
                            </span>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        {{item.title}}
                      </div>
                    </template>
                    <template v-slot="slotProps">
                          <div v-if="item.field === 'url'"  id="imgLook">
                            <img :src="slotProps.row.minImgList[0]" alt="" @click.stop="imgLook(slotProps.row)">
                            <!-- <div @click.stop="imgLook(slotProps.row)">查看</div> -->
                          </div>
                          <div v-else-if="item.field === 'productName'" id="productName">
                            <div style="text-align: left;">
                              <div>商品名称:<span>{{slotProps.row.productName}}</span></div>
                              <div>规格:<span >{{slotProps.row.spec}}</span></div>
                              <div>生产厂家:<span >{{slotProps.row.manufacturer}}</span></div>
                            </div>
                          </div>
                          <div v-else-if="item.field === 'hx'">
                            <span class="clickWriting" style="margin-right: 10px;" @click.stop="viewRing(slotProps.row)">查看环线价</span>
                          </div>
                          <div v-else>
                              <span>{{slotProps.row[item.field] }}</span>
                          </div>
                      </template>
                      </vxe-table-column>
                  </template>
              </vxe-table>
              <div class="pager">
                <vxe-pager
                border
                :current-page="tablePage.pageNum"
                :page-size="tablePage.pageSize"
                :total="tablePage.total"
                :page-sizes="tablePage.pageSizes"
                :layouts="[
                  'PrevPage',
                  'JumpNumber',
                  'NextPage',
                  'FullJump',
                  'Sizes',
                  'Total',
                ]"
        @page-change="handlePageChange"
      />
              </div>
              <div><btn-group slot="tools" :btn-list="btnListTabale" /></div>
          </xyy-panel>

      </xyy-dialog>
       <el-image
      style="display: none;"
      :src="url"
      ref="imageRef"
      :preview-src-list="srcList">
    </el-image>
      <!-- 查看环线价 -->
      <viewRingLinePrice ref="viewRingLinePrice" :viewRingLinePriceRow="viewRingLinePriceRow"/>
  </div>
</template>

<script>
import { addColumns } from '../config'
import { pageConfig } from '@/utils';
import { queryList } from '@/api/marketing/specialActive';
import viewRingLinePrice from './viewRingLinePrice.vue';
export default {
  name: 'productSearch',
  components:{
    viewRingLinePrice
  },
  data() {
      return {
          btnListTop: [
              {
                  label: "查询",
                  type: "primary",
                  clickEvent: this.searchList,
                  code: "",
                  plain: 'false',
                  permission:'specialActive:addPro:query',
              },
              {
                  label: "重置",
                  clickEvent: this.resetForm,
                  code: "",
                  plain: 'false',
                  permission:'specialActive:addPro:reset',
              },
          ],
          btnListTabale: [
            {
              label: '确认',
              clickEvent: this.addGoods,
              type: 'primary',
              code: "",
              plain: 'false',
              permission:'specialActive:addPro:addGoods',
            },
            {
              label: '取消',
              clickEvent: this.close,
              code: "",
              plain: 'false',
              permission:'specialActive:add:back',
            },
          ],
          formData: {
            erpProductCode: '',
            csuid: '',
            productName: '',
            type: '',
          },
          tablePage: pageConfig(),
          tableColumns: addColumns(),
          tableData: [],
          selectedRows: [], // 存储已选中的数据
          viewRingLinePriceRow:undefined, //查看环线价入参
          loading: false,
          srcList:[],//图片列表
          url:'',//查看图片
          checkbox_container: new Map(),  // 存储checkbox的选中状态
      }
  },
  mounted(){

  },
  methods: {
    handleInput(value) {
      // 使用正则表达式，替换非数字字符
      this.formData.csuid = value.replace(/[^\d]/g, '');
    },

    // 动态设置行高
    rowStyle({ row, rowIndex }) {
      return {
        // height:'80px !important',
      }; // 统一设置行高为 80px
    },
    // 查询列表
    searchList(flag) {
      // 等待DOM更新完成后，保存当前checkbox的选中状态
      this.$nextTick(() => {
        this.saveCheckbox()
      })
      this.loading = true
        if (!flag) {
            this.tablePage.pageNum = 1
        }
        const params = {
            pageNum: this.tablePage.pageNum,
            pageSize: this.tablePage.pageSize,
            erpProductCode:this.formData.erpProductCode,
            csuid:this.formData.csuid,
            productName:this.formData.productName,
            type:this.formData.type,
            activityTypes: [0]
        }
        queryList(params).then(res => {
          if (res.code === 0) {
            this.tableData = res.result.list ? res.result.list : []
            this.tablePage.total = res.result.total
          } else{
            this.$message.error(res.message)
          }
        }).finally(() => {
          // 根据当前表格数据，找出之前已在checkbox_container中保存的行
          const checkedRows = this.tableData.map(item => {
              if (this.checkbox_container.has(item.id)) {
                  return item
              }
            })
            // 重新设置这些行的checkbox为选中状态
            this.$refs.xTable.setCheckboxRow(checkedRows, true)
            this.loading = false

        })

    },
    /**查看图片 */
    imgLook(row){
      if (row.bigImgList) {
        this.srcList = row.bigImgList;
        this.url = this.srcList[0] || '';
        this.$nextTick(() => {
          this.$refs.imageRef.showViewer = true; // 触发预览
        });
      } else {
        this.srcList = [];
        this.url = '';
        this.$message.error('没有图片');
      }
    },
      //重置
      resetForm(){
        this.$refs.formData.resetFields()
        this.searchList()
      },
      selectAllEvent(){
        },
      // 表格行选择事件
      selectChangeEvent({ row, checked }) {
            // 切换当前行的选中状态：如果当前行已经选中则取消选中，反之亦然
            this.$refs.xTable.toggleCheckboxRow(row)

            // 如果当前行未被选中，则从容器中删除该行数据，避免残留已选状态的数据
            if (!checked) {
                if (this.checkbox_container.has(row.id)) {
                    this.checkbox_container.delete(row.id)
                }
            }
        },

        // 表格行点击事件
        cellClickEvent({ row }) {
            // 点击事件时切换当前行的选中状态
            this.$refs.xTable.toggleCheckboxRow(row)

            // 如果当前行经过切换后状态为未选中，则从容器中删除该行数据
            if (!this.$refs.xTable.isCheckedByCheckboxRow(row)) {
                if (this.checkbox_container.has(row.id)) {
                    this.checkbox_container.delete(row.id)
                }
            }
        },

        // 保存checkbox的选中状态
        saveCheckbox() {
            // 获取所有通过checkbox选中的表格行数据
            const rows = this.$refs.xTable.getCheckboxRecords()

            // 遍历每一行选中的数据，并将其存放到checkbox_container中
            // 这样可以保持选中状态的数据，供后续的数据操作使用
            rows?.forEach((v) => {
                this.checkbox_container.set(v.id, v)
            })
        },
      //返回数据
      addGoods() {
        this.saveCheckbox()
        const rows = []
        this.checkbox_container.forEach(v => {
          v.isShow = true
         rows.push(v)
        })
        if (rows.length === 0) {
          this.$message.warning('请选择商品')
          return
        }
        this.$emit('echoData', rows)
      },
      //查看环线价
      viewRing(row){
        this.viewRingLinePriceRow = {...row}
        this.$refs.viewRingLinePrice.open('false')
      },
      // 打开弹窗
      open() {
        this.tablePage.pageNum = 1
        this.tablePage.pageSize = 20
        this.tablePage.total = 0
        this.tableData = []
        this.$refs.productSearch.open()
        this.$nextTick(() => {
          this.$refs.formData.resetFields()
          this.searchList()
          this.$refs.xTable.setAllCheckboxRow(false)
          this.checkbox_container.clear()
        })
      },
      //关闭弹窗
      close() {
        this.$refs.productSearch.close()
      },
      // 分页器改变处理
      handlePageChange({currentPage, pageSize}) {
          this.tablePage.pageNum = currentPage;
          this.tablePage.pageSize = pageSize;
          this.searchList(true)
        },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-body--column{
  max-height: 100px !important;
}
::v-deep .vxe-cell{
  max-height: 100px !important;
}
::v-deep .vxe-table--body-wrapper{
    height:230px !important;
}
// ::v-deep .viewImg{
//   position: relative;
// }
// ::v-deep .productName{

// .vxe-cell,.c--title{
//   overflow: visible !important;
// }
// }
#imgLook{
  img {
    // position: absolute;
    // top: 50%;
    // left:50%;
    // margin-left: -32px;
    // margin-top: -32px;
    width: 64px;
    height: 64px;
    cursor: pointer;
  }
}
// ::v-deep .vxe-body--row{

//   .vxe-body--column{
//     .vxe-cell,.c--title{
//       overflow: visible !important;
//     }
//   }
// }
// #productName{
//   position: absolute;
//   margin: auto;
//   top: 10%;
// }
</style>
