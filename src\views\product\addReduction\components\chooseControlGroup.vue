<template>
    <div>
        <xyy-dialog title="选择控销组" ref="chooseControlGroup" width="60%" :before-close="handleClose">
            <xyy-panel :titleShow='false'>
                <btn-group slot="tools" :btn-list="btnListForm" />
                <el-form ref="form" :model="form" label-width="100px">
                    <el-row :gutter="10">
                        <el-col :lg="12" :md="12">
                            <el-form-item label="控销组ID">
                                <el-input v-model="form.groupId" @input="groupIdInput" />
                            </el-form-item>
                        </el-col>
                        <el-col :lg="12" :md="12">
                            <el-form-item label="控销组名称">
                                <el-input v-model="form.groupName" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div :key="tabKey" class="table-box">
                    <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto"
                        :max-height="tableHeight" :data="tableData" :key="tableKey" :show-overflow="false"
                        
                        @cell-click="handleCellClick" @radio-change="handleRadioChange">
                        <vxe-table-column type="radio" width="50" />
                        <vxe-table-column type="seq" title="序号" width="80" />
                        <template>
                            <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                                :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            </vxe-table-column>
                        </template>
                    </vxe-table>
                </div>
                <div class="pager">
                    <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                        :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                            'PrevPage',
                            'JumpNumber',
                            'NextPage',
                            'FullJump',
                            'Sizes',
                            'Total',
                        ]" @page-change="handlePageChange" />
                </div>
            </xyy-panel>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleSubmit">确 定</el-button>
                <el-button @click="handleClose">取 消</el-button>
            </div>
        </xyy-dialog>
    </div>
</template>

<script>
import { queryControlledStore } from '@/api/product/controlledStoreManagement'
import { chooseCustomerCol } from '../config.js'
import { pageConfig } from '@/utils/index.js'
import XEUtils from 'xe-utils'
export default {
    name: "addCustomers",
    data() {
        return {
            btnListForm: [
                {
                    label: "查询",
                    type: 'primary',
                    clickEvent: this.searchList,
                    code: "",
                    plain: 'false',
                    permission: 'product:addReduction:search'
                },
                {
                    label: "重置",
                    type: 'info',
                    clickEvent: this.resetForm,
                    code: "",
                    plain: 'false',
                    permission: 'product:addReduction:refresh'
                },
            ],
            form: {
                groupId: "",
                groupName: "",
            },
            tabKey: 0,
            tableData: [],
            tableKey: 0,
            loading: false,
            tableHeight: 300,
            tableColumns: chooseCustomerCol(),
            tablePage: pageConfig(),
        }
    },
    methods: {
        groupIdInput(value) {
            let newVal = value.trim();
            newVal = newVal.replace(/[^\d]/g, '');
            this.form.groupId = newVal;
        },
        getList() {
            this.loading = true
            const params = {
                ...this.form,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
            }
            queryControlledStore(params).then((res) => {
                const { code, msg, result } = res
                if (code === 0) {
                    result.list?.forEach(item => {
                        item.createTimeStr = XEUtils.toDateString(item.createTime, 'yyyy-MM-dd HH:mm:ss')
                        item.updateTimeStr = XEUtils.toDateString(item.updateTime, 'yyyy-MM-dd HH:mm:ss')
                    })
                    this.tableData = result.list
                    this.tablePage.total = result.total
                } else {
                    this.$message.error(msg)
                }
            }).finally(() => {
                this.tableKey++
                this.loading = false;
            })
        },
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage
            this.tablePage.pageSize = pageSize
            this.getList()
        },
        handleRadioChange({ row }) { },
        handleCellClick({ row }) {
            this.$refs.xTable.setRadioRow(row)
        },
        handleSubmit() {
            const row = this.$refs.xTable.getRadioRecord()
            if (!row) {
                this.$message.warning('请选择一个控销组')
                return
            }
            this.$emit('chooseControlGroup', row)
            this.handleClose()
        },
        resetForm() {
            this.form = {
                groupId: "",
                groupName: "",
            }
            this.searchList()
        },
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
        open() {
            this.form = {
                groupId: "",
                groupName: "",
            }
            this.$refs.chooseControlGroup.open()
            this.searchList()
        },
        handleClose() {
            this.$refs.chooseControlGroup.close()
        },
    },
}
</script>

<style></style>