<template>
  <div class="app-container">
    <xyy-panel :titleShow="false">
       <!-- 按钮组 start-->
       <btn-group slot="tools" :btn-list="btnListTop"/>
       <el-form ref="formData" :model="formData" label-width="120px" class="clearfix" :rules="rules">
        <el-row :gutter="20">
          <el-col :lg="8" :md="8">
            <el-form-item  label="活动开始时间" prop="startTime">
              <el-date-picker
                        style="width: 100%;"
                          :show-now="false"
                          v-model="formData.startTime"
                          :picker-options="pickerOptionsStart"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          type="datetime"
                          placeholder="选择日期时间"
                          popper-class="lwq-datepicker"
                          >

                        </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="8">
            <el-form-item label="活动结束时间" prop="endTime">
              <el-date-picker
               style="width: 100%;"
                          v-model="formData.endTime"
                          :picker-options="pickerOptionsEnd"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          type="datetime"
                          placeholder="选择日期时间"
                          popper-class="lwq-datepicker">
                        </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :lg="16" :md="16">
            <el-form-item label="活动名称" prop="title">
              <el-input v-model.trim="formData.title" placeholder="非必填项，50字以内" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>

        </el-row>

       </el-form>
    </xyy-panel>
    <xyy-panel title="" :titleShow="false">
      <div style="display: flex;justify-content: space-between;">
        <btn-group  slot="tools" :btn-list="btnListTabale" style="float: none;"/>
        <div class="searchProduct" >
        <el-input v-model="searchProduct" placeholder="查询已添加商品" style="width:250px;"/>
        <el-button type="primary" @click="handelSearch" style="margin-left: 10px;">查询</el-button>
        <el-button  @click="handelReset">重置</el-button>
      </div>

      </div>

      <div class="redTips"><img src="@/assets/images/tips.svg" alt="" style="width: 16px;height: 16px; margin: 12px">修改特价时请仔细核对价格，如果出现价格改错且销售出去的订单，一律由商家自己承担损失！</div>


      <div class="table-box">
              <vxe-table ref="xTable" :loading="loading" highlight-current-row height="400" :data="tableData.filter(item => item.isShow)"
                  :key="tableKey"
                  :row-style="rowStyle"
                   empty-text=""
                  @checkbox-all="selectAllEvent"
                  @cell-click="cellClickEvent"
                  @checkbox-change="selectChangeEvent">
                  <vxe-table-column type="checkbox" width="80" />
                  <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field" :title="item.title" :width="item.width" :className="item.className">
                    <template #header>
                      <div v-if="item.hit && checkAccess">
                        <el-tooltip placement="top">
                          <template slot="content">
                            <div v-for="(it,index) in item.hit" :key="index">
                              <div>{{it}}</div>
                            </div>
                          </template>
                          <div>
                            <span>{{item.title}}</span>
                            <span>
                              <svg-icon  icon-class="prompt-icon"/>
                            </span>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        {{item.title}}
                      </div>
                    </template>
                      <template v-slot="slotProps">

                          <div v-if="item.field === 'hx'">
                            <span class="clickWriting" style="margin-right: 10px;" @click.stop="viewRing(slotProps.row)">查看环线价</span>
                          </div>
                          <div v-else-if="item.field === 'url'"  id="imgLook">
                            <img :src="slotProps.row.minImgList[0]" alt="" @click.stop="imgLook(slotProps.row)">
                          </div>
                          <div v-else-if="item.field === 'productName'" id="productName">
                            <div style="text-align: left;">
                              <div>商品名称:<span>{{slotProps.row.productName}}</span></div>
                              <div>规格:<span >{{slotProps.row.spec}}</span></div>
                              <div>生产厂家:<span >{{slotProps.row.manufacturer}}</span></div>
                            </div>
                          </div>
                          <div v-else-if="item.field === 'activityPrice'" id="hdInput">
                            <el-input
                              class="custom-input"
                              v-model="slotProps.row[item.field]"
                              @input="validateNumber($event, slotProps.row, item.field)"
                              placeholder="请输入"
                            ><svg-icon slot="suffix" icon-class="new-edit" style="cursor: pointer;"></svg-icon></el-input>
                            <div v-if="slotProps.row.priceMessage" style="color: red;font-size: 13px;position: absolute;">{{ slotProps.row.priceMessage }}</div>
                          </div>
                          <div v-else>
                              <span>{{slotProps.row[item.field] }}</span>
                          </div>
                      </template>
                  </vxe-table-column>
              </vxe-table>
          </div>

    </xyy-panel>
    <el-image
      style="display: none;"
      :src="url"
      ref="imageRef"
      :preview-src-list="srcList">
    </el-image>
    <!-- 选择商品 -->
    <productSearch ref="addProduct" @echoData="echoData"/>
    <!-- 查看环线价 -->
    <viewRingLinePrice ref="viewRingLinePrice" :viewRingLinePriceRow="viewRingLinePriceRow"/>
    <!-- 批量导入 -->
    <BatchInput ref="batchInput" @success="handleImportSuccess"/>
    <!-- 导入反馈 -->
     <el-dialog title="批量导入商品反馈" :visible.sync="inputVisible" width="30%" v-if="inputVisible">
        <xyy-panel :titleShow="false">
          <div style="margin-bottom: 10px;">
            批量导入成功{{ inputResult.successNum }}条，失败{{ inputResult.failureNum }}条，失败原因见错误文件。
          </div>
          <div>
            <sapan class="clickWriting" @click="downloadErrorFile">错误文件下载</sapan>
          </div>
        </xyy-panel>

          <div slot="footer" class="dialog-footer">
            <el-button @click="inputVisible = false" type="primary">确认</el-button>
          </div>

     </el-dialog>
  </div>
</template>
<script>
import {saveSpecial,checkAccess} from '@/api/marketing/specialActive'
import { addTableColumns } from './config'
import viewRingLinePrice from './components/viewRingLinePrice.vue'
import productSearch from './components/productSearch.vue'
import BatchInput from './components/batchInput.vue'

export default {
  name: 'SpecialActiveAdd',
  components: {
    productSearch,
    BatchInput,
    viewRingLinePrice
  },
  data() {
    return {
      checkAccess: false, //是否检查准入价
      inputVisible: false, //批量导入反馈弹窗
      inputResult: {},
      activeId: '',
      type: '',
      loading:false,
      formData: {
        title: '',
        startTime: null,
        endTime: null,
      },
      searchProduct: '', //查询已添加商品
      viewRingLinePriceRow:undefined, //查看环线价行数据
      pickerOptionsStart: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        },
        selectableRange: new Date().getHours() + ":" + (new Date().getMinutes()) + ":00 - 23:59:00",
      },
      pickerOptionsEnd: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        },
        selectableRange: new Date().getHours() + ":" + (new Date().getMinutes()) + ":00 - 23:59:00",
      },
      rules: {
        startTime: [
          { required: true, message: '请选择活动开始时间', trigger: 'change' },
          { validator: this.validateStartTime, trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择活动结束时间', trigger: 'change' },
          { validator: this.validateEndTime, trigger: 'change' }
        ]
      },
      btnListTop: [
        {
          label: '提交',
          clickEvent: this.submit,
          code: "",
          plain: 'false',
          type:'primary',
          permission:'specialActive:add:submit'
        },
        {
          label: '取消',
          clickEvent: this.backToPage,
          code: "",
          plain: 'false',
          permission:'specialActive:add:back'
        }
      ],

      btnListTabale: [
        {
          label: '添加商品',
          clickEvent: this.addGoods,
          code: "",
          plain: 'false',
          type:'primary',
          permission:'specialActive:add:addGoods'
        },
        {
          label: '批量导入商品',
          clickEvent: this.btachAddGoods,
          code: "",
          plain: 'false',
          type:'primary',
          permission:'specialActive:add:btachAddGoods'
        },
        {
          label: '批量删除',
          clickEvent: this.delGoods,
          type: "danger",
          code: "",
          plain: 'false',
          permission:'specialActive:add:delGoods'
        }
      ],
      tableData: [
        // {
        //   erpProductCode: 'ERP001',
        //   productCode: 'PC001',
        //   productName: '阿莫西林胶囊',
        //   isShow: true,
        //   pt:8,
        //   xs:9
        // },
        // {
        //   erpProductCode: 'ERP002',
        //   productCode: 'PC002',
        //   productName: '布洛芬片',
        //   isShow: true
        // },
        // {
        //   erpProductCode: 'ERP003',
        //   productCode: 'PC003',
        //   productName: '感冒灵颗粒',
        //   isShow: true
        // },
        // {
        //   erpProductCode: 'ERP004',
        //   productCode: 'PC004',
        //   productName: '板蓝根冲剂',
        //   isShow: true
        // },
        // {
        //   erpProductCode: 'ERP005',
        //   productCode: 'PC005',
        //   productName: '维生素C片',
        //   isShow: true
        // }
      ],
      srcList:[],//图片列表
      url:'',//查看图片
      tableColumns: addTableColumns(),
      tableKey: 0
    }
  },
  mounted() {
    this.checkAccessPrice()
  },
  watch: {
    'formData.startTime'(val) {
      if (val && !this.isToday(val)) {
        this.$nextTick(() => {
          this.pickerOptionsStart.selectableRange = '00:00:00 - 23:59:59'
        })

      } else {
        this.$nextTick(() => {
          this.pickerOptionsStart.selectableRange = new Date().getHours() + ":" + (new Date().getMinutes()) + ":00 - 23:59:00"
        })
      }
    },
    'formData.endTime'(val) {
      if (val && !this.isToday(val)) {
        this.pickerOptionsEnd.selectableRange = '00:00:00 - 23:59:59'
      } else {
        this.pickerOptionsEnd.selectableRange = new Date().getHours() + ":" + (new Date().getMinutes()) + ":00 - 23:59:00"
      }
    }
  },
  methods: {
    // 日期选择器禁用时间判断是否为今天
    isToday(dateStr) {
    const today = new Date();
    const selectedDate = new Date(dateStr); // 将 yyyy-MM-dd HH:mm:ss 转换为 Date 对象
    return selectedDate.toDateString() === today.toDateString(); // 比较日期部分
  },
    // 校验开始时间
    validateStartTime(rule, value, callback) {
      if (this.formData.endTime && value && new Date(value).getTime() > new Date(this.formData.endTime).getTime()) {
        callback(new Error('开始时间不能大于结束时间'));
      } else {
        callback();
      }
    },

    // 校验结束时间
    validateEndTime(rule, value, callback) {
      if (this.formData.startTime && value && new Date(value).getTime() < new Date(this.formData.startTime).getTime()) {
        callback(new Error('结束时间不能小于开始时间'));
      } else {
        callback();
      }
    },
    //是否检查准入价
    async checkAccessPrice(){
      const res = await checkAccess()
      if(res.code === 0){
        this.checkAccess = res.result.checkAccess
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态设置行高
    rowStyle({ row, rowIndex }) {
      return {
        height:'80px !important',
      }; // 统一设置行高为 80px
    },
    backToPage() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push({path: '/marketing/SpecialActive'})
    },
    selectAllEvent(){
        },
     // 选择行
    selectChangeEvent({ row }) {
      this.$refs.xTable.toggleCheckboxRow(row)


    },
     //点击表格行
    cellClickEvent({ row }) {
      this.$refs.xTable.toggleCheckboxRow(row)
    },
    //查询已添加商品
    handelSearch(){
      const searchStr = this.searchProduct ? this.searchProduct.toString().toLowerCase() : '';

      this.tableData.forEach(item => {
        if (searchStr) {
          item.isShow = (item.erpProductCode && item.erpProductCode.toLowerCase().includes(searchStr)) ||
                        (item.csuid && item.csuid.toString().toLowerCase().includes(searchStr)) ||
                        (item.productName && item.productName.toLowerCase().includes(searchStr));
                        console.log(item.isShow);

        } else {
          item.isShow = true; // 搜索框为空，显示所有数据
        }
        this.tableKey++
});

    },
    //重置
    handelReset(){
      this.searchProduct = ''
      // 重置时显示所有数据
      this.tableData.forEach(item => {
        item.isShow = true
      })
    },
    /**查看图片 */
    imgLook(row){
      if (row.bigImgList) {
        this.srcList = row.bigImgList;
        this.url = this.srcList[0] || '';
        this.$nextTick(() => {
          this.$refs.imageRef.showViewer = true; // 触发预览
        });
      } else {
        this.srcList = [];
        this.url = '';
        this.$message.error('没有图片');
      }
    },
    //添加商品
    addGoods(){
      this.$refs.addProduct.open()
    },
    echoData(rows){
      const barcodes = new Set(this.tableData.map(item => item.barcode));
      if(rows.some(item => barcodes.has(item.barcode))){
        return this.$message.warning('商品已存在，请勿重复添加！');
      }
      this.tableData.unshift(...rows);
      this.$refs.addProduct.close()
    },
    //查看环线价
    viewRing(row){
      if(!row.activityPrice){
        return this.$message.warning('请先填写活动价格')
      }
      this.viewRingLinePriceRow = {...row}
      this.$refs.viewRingLinePrice.open()
    },
    //删除商品
    delGoods(){
          const rows = this.$refs.xTable.getCheckboxRecords()
          if (rows.length === 0) {
             this.$message.warning('请选择商品')
              return false
          }
          this.$confirm('您确定要删除已添加商品吗？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
          }).then(() => {
              rows.forEach(item => {
                  const index = this.tableData.findIndex(row => row._XID === item._XID)
                  this.tableData.splice(index, 1)
              })
              this.$message.success('删除成功')
          })
        },
    //提交
    submit() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          // 表单验证通过，继续提交逻辑
          if (this.tableData.length === 0) {
            return this.$message.warning('请添加商品')
          } else {
            //活动价空
            const empty = this.tableData.some(item => !item.activityPrice)
            if (empty) {
              return this.$message.warning('活动价为必填项')
            }
            //活动价输入异常
            const needCheck = this.tableData.some(item => item.needCheck)
            if (needCheck) {
              return this.$message.warning('请检查活动价')
            }
             // 校验开始时间是否至少比当前时间晚1秒
          const now = new Date().getTime();
          const startTime = new Date(this.formData.startTime).getTime();
          if (startTime - now < 1000) {
            this.$message.warning('活动开始时间必须晚于当前时间');
            return;
          }
            const loading = this.$loading({
              lock: true,
              text: '加载中',
              spinner: 'el-icon-loading',
              background: 'rgba(255,255,255, 0.8)',
            })
            const promotionSkuExtendDTOList = this.tableData.map(item => {
              return {
                skuId: item.csuid,
                skuPrice: +item.activityPrice
              }
            })
            const params = {
              title: this.formData.title,
              startTime: this.formData.startTime,
              endTime: this.formData.endTime,
              promotionSkuExtendDTOList
            }
            saveSpecial(params).then(res => {
              if (res.code === 0) {
                if(res.result.result){
                  this.$message.success('提交成功')
                  setTimeout(() => {
                    this.backToPage()
                  }, 500)
                }else{
                  return this.$confirm(`${res.result.failMsg}`, '温馨提示', {
                      confirmButtonText: '下载失败文件',
                      cancelButtonText: '取消',
                      type: 'warning'
                  }).then(() => {
                    this.downloadFile(res.result.failUrl)
                  })
                }

              } else {
                this.$message.error(res.msg || '提交失败')
              }
            }).finally(() => {
              loading.close()
            })
          }
        } else {
          return
        }
      })
    },
    //修改下载失败文件名
   downloadFile(url1) {
  const url = url1;
  const fileName = "特价活动创建失败错误文件.xlsx"; // 自定义文件名

  fetch(url)
    .then(response => response.blob()) // 转换为 Blob
    .then(blob => {
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName; // 设置新的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    })
    .catch(error => console.error("下载失败", error));
  },
    //批量导入商品
    btachAddGoods() {
      const erpCodes = this.tableData.map(item => item.erpProductCode).toString()
      this.$refs.batchInput.open(erpCodes)
    },
    //导入成功回调
    handleImportSuccess(data) {
      this.inputResult = data
      // 处理导入的数据
      if (data.successNum > 0) {
        data.rows.forEach(item => {
          item.isShow = true
          item.csuid = item.skuId
          item.statusText = item.statusName
        })
       this.tableData.unshift(...data.rows)
      }
      if(data.failureNum > 0){
        this.inputVisible = true
      }
    },
    //下载错误文件
    downloadErrorFile() {
      const url = this.inputResult.failureExcelFileDownloadUrl;
  const fileName = "批量导入商品失败错误文件.xlsx"; // 自定义文件名

  fetch(url)
    .then(response => response.blob()) // 转换为 Blob
    .then(blob => {
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName; // 设置新的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    })
    .catch(error => console.error("下载失败", error));
    },
    validateNumber(value, row, field) {
      // 如果为空，直接返回
      if (!value) {
        row[field] = ''
        row.priceMessage = ''
        return
      }
      // 先将中文小数点替换为英文小数点
      value = value.replace(/。/g, '.')

       // 只允许数字和小数点
       value = value.replace(/[^\d.]/g, '')
      // 将多个小数点替换为单个小数点
      value = value.replace(/\.{2,}/g, '.')
      // 确保只保留第一个小数点
      value = value.replace(/(\.[^.]*)\./g, '$1')
      // 限制最多两位小数
      value = value.replace(/(\d+\.\d{2})\d*/g, '$1')
      // 第一位不能为小数点
      value = value.replace(/^\./g, '')

      // 转换为数字进行比较
// 转换为数字进行比较
const num = parseFloat(value);

if (num < 0) {
  value = '';
  row.priceMessage = '';
} else if (num === 0) {
  row.priceMessage = '活动价格需大于0';
} else {
  // 定义错误提示
  const priceMessages = {
    both: '活动价格需小于销售价和平台建议价',
    sales: '活动价格需小于销售价',
    access: '活动价格需小于等于平台建议价'
  };

  if (!this.checkAccess) {
    // 当没有checkAccess时，检查活动价格与销售价
    if (num >= row.suggestPrice) {

      row.priceMessage = priceMessages.sales;
      row.needCheck = true;
    } else {
      row.priceMessage = '';
      row.needCheck = false;
    }
  } else {
    // 当有checkAccess时，检查活动价格与销售价和平台建议价
    if (row.accessPrice !== undefined && row.accessPrice !== null) {
      // 如果有 accessPrice
      if (num >= row.suggestPrice && num > row.accessPrice) {
        row.priceMessage = priceMessages.both;
        row.needCheck = true;
      } else if (num >= row.suggestPrice) {

        row.priceMessage = priceMessages.sales;
        row.needCheck = true;
      } else if (num > row.accessPrice) {
        row.priceMessage = priceMessages.access;
        row.needCheck = true;
      } else {
        row.priceMessage = '';
        row.needCheck = false;
      }
    } else {
      // 如果没有 accessPrice，跳过与平台建议价相关的判断
      if (num >= row.suggestPrice) {

        row.priceMessage = priceMessages.sales;
        row.needCheck = true;
      } else {
        row.priceMessage = '';
        row.needCheck = false;
      }
    }
  }




      }
      row[field] = value
    },
  }
}

</script>

<style scoped lang="scss">
::v-deep .vxe-body--column{
  max-height: 100px !important;
}
::v-deep .vxe-cell{
  max-height: 100px !important;
}
.custom-input::v-deep .el-input__suffix {
  display: flex;
  align-items: center; /* 垂直居中 */
}
.redTips{
  background-color: #FEF0F0;
  height: 38px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;

  img {
    width: 16px;
    height: 16px;
    margin: 0 12px;
  }

  div {
    display: flex;
    align-items: center;

    span {
      display: flex;
      align-items: center;

      img {
        margin:0; // 只保留左边距
      }
    }
  }
}
::v-deep .viewImg{
  position: relative;
}
#imgLook{
  img {
    position: absolute;
    top: 50%;
    left:50%;
    margin-left: -32px;
    margin-top: -32px;
    width: 64px;
    height: 64px;
    cursor: pointer;
  }
}
::v-deep .hdInput{
.vxe-cell,.c--title{
  overflow: visible !important;
}
}
// ::v-deep .productName{
// .vxe-cell,.c--title{
//   overflow: visible !important;
// }
// }
.searchProduct{
  margin-bottom: 10px;
}
// #productName{
//   position: absolute;
//   margin: auto;
//   top: 10%;
// }

</style>
<style  lang="scss">
.lwq-datepicker {
  .el-picker-panel__footer {
    button:nth-child(1) {
      display: none !important;
    }
  }
}
</style>

