<template>
  <div>
    <xyy-dialog
      title="查看业务商圈"
      :visible="show"
      :width="dialogWidth"
      ref="viewBusinessDialog"
      @on-close="handleDialogClose"
    >
        <xyy-panel :titleShow="false">
          <business-circle-detail :detailId="detailId" :isPage="false" />
        </xyy-panel>
      <span slot="footer">
        <el-button type="primary" style="margin-top: 10px" size="small" @click="sureBtn"
          >确 定</el-button
        >
      </span>
    </xyy-dialog>
  </div>
</template>

<script>
import businessCircleDetail from './business-circle-detail.vue'

export default {
  components: { businessCircleDetail },
  name: 'viewBusinessCircle',
  data() {
    return {
      show: false,
      dialogWidth: '80%',
      detailId:'',
    }
  },
  model: {
    prop: 'dialogShow',
    event: 'onDialogChange'
  },
  props: {
    dialogShow: Boolean,
    row: Object
  },
  mounted() {
  },
  methods: {
    handleDialogClose() {
      this.detailId = '';
      this.$refs.viewBusinessDialog.close();
      this.$emit('onDialogChange', false)
    },
    open() {
      this.$nextTick(()=>{
        this.detailId = this.row.id;
        this.$refs.viewBusinessDialog.open();
      })
    },

    sureBtn() {
      this.handleDialogClose()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep.el-dialog__header {
  background-color: #f8f8ff;
}
</style>
