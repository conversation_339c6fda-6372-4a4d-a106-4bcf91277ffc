<template>
  <div>
    <div class="app-container home"  v-show="isShow">
      <div class="side side-left">
        <el-card class="part part01" shadow="never">
          <div class="headline">
            <span>今日实时业绩</span>
            <span class="timer">更新时间：{{ currentTime }}</span>
          </div>
          <el-row :gutter="21">
            <el-col class="card-wrap" style="width: 23%;">
              <el-card shadow="never" class="card-col">
                <div class="card-title">销售下单</div>
                <div><span>￥</span>2000.89</div>
              </el-card>
            </el-col>
            <el-col class="card-wrap" style="width: 23%;">
              <el-card shadow="never" class="card-col">
                <div class="card-title">销售下单</div>
                <div><span>￥</span>7838.89</div>
              </el-card>
            </el-col>
            <el-col class="card-wrap" style="width: 23%;">
              <el-card shadow="never" class="card-col">
                <div class="card-title">销售下单</div>
                <div><span>￥</span>5200.89</div>
              </el-card>
            </el-col>
            <el-col class="card-wrap" style="width: 30%;">
              <el-card shadow="never" class="last-card">
                <div class="list"><span class="tags-msg">当月入库</span> ￥981.61</div>
                <div class="list"><span class="tags-msg">当月出库</span> ￥42,308.09</div>
                <div class="list"><span class="tags-msg">当月入库</span> ￥25,224.03</div>
              </el-card>
            </el-col>
          </el-row>
          <div class="echarts" ref="barChart">
            <!-- echarts will be initialized here -->
          </div>
        </el-card>
        <el-card class="part part02" shadow="never">
          <div class="chart-header">
            <div class="chart-title">近6个月供货</div>
            <div class="view-detail">查看详情<svg-icon icon-class="home-detail-icon" style="margin-left:3px;"/></div>
          </div>
          <div class="echarts" ref="mixChart"></div>
        </el-card>
      </div>
      <div class="side side-right">
        <el-card class="part part01" shadow="never">
          <div class="headline">
            <span>季度销量</span>
            <span class="timer">周期：2025-02-20 — 2025-02-22 </span>
          </div>
          <div class="content">
              <div class="content-title">季度完成
                <el-tooltip class="item" effect="dark" content="Top Left 提示文字" placement="top-start">
                  <svg-icon icon-class="home-help-icon" style="cursor: pointer; margin-left: 5px;"/>
                </el-tooltip>
              </div>
            <div class="payment"><span>￥</span>9,147,336.01</div>
          </div>
          <div class="progess">
            <div class="head-line">
              <span>实际完成度：63.97%</span>
              <span>本周期目标： ￥14,300,300.00</span>
            </div>
            <el-progress :stroke-width="8" :percentage="100" status="success"></el-progress>
          </div>
        </el-card>
        <el-card class="part notice" shadow="never">
          <div class="head-line">系统公告</div>
          <div class="list-wrap">
            <div class="list">1、【2.12日发版内容】：此处展示系统公告文案，此处展示系统公告...</div>
            <div class="list">2、【1.25日发版内容】：此处展示系统公告文案，此处展示系统公告...</div>
            <div class="list">3、【1.13日发版内容】：此处展示系统公告文案，此处展示系统公告...</div>
          </div>
        </el-card>
      </div>
    </div>
    <el-dialog
      title="当前密码安全等级低，请修改密码"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      center
      width="400px">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input 
            v-model="passwordForm.oldPassword" 
            type="password" 
            placeholder="请输入旧密码"
            show-password>
          </el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password" 
            placeholder="请输入新密码"
            show-password>
          </el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新密码"
            show-password>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPassword">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="系统公告"
      :visible.sync="noticeListVisible"
      :close-on-click-modal="false"
      center
      width="500px">
      <div id="noticeList"  v-html="noticeContent">
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import Cookies from "js-cookie";
import { isPassWeak ,noticeList} from '@/api/login'
import { updateUserPwd } from "@/api/system/user";
export default {
  name: "Index",
  data() {
    // 密码验证函数
    const validateOldPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入旧密码'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else if (!/^(?=.*[A-Za-z])(?=.*\d)[\w\W]{8,20}$/.test(value)) {
        callback(new Error('必须包含数字和字母，且长度为8~20位'));
      } else if (value === this.passwordForm.oldPassword) {
        callback(new Error('新密码不能与旧密码相同'));
      } else {
        if (this.passwordForm.confirmPassword !== '') {
          this.$refs.passwordForm.validateField('confirmPassword');
        }
        callback();
      }
    };
    // 确认密码验证函数
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };

    return {
      // 版本号
      noticeListVisible:false,
      noticeContent:'',
      version: "3.8.3",
      barChart: null,
      mixChart: null,
      currentTime: this.getCurrentTime(),
      name:this.$store.state.user.name,
      dialogVisible: false,
      isShow:false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, validator: validateOldPassword, trigger: 'blur' }
        ],
        newPassword: [
          { required: true, validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    };
  },
  created(){
    this.isPassWeakFlase()
  },
  mounted() {
    this.initBarChart()
    this.initMixChart()
  },
  beforeDestroy() {
    if (this.barChart) {
      this.barChart.dispose()
    }
    if (this.mixChart) {
      this.mixChart.dispose()
    }
  },
  methods: {
    // 提交密码修改
    submitPassword() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          try {
            // 修改密码
            const res = await updateUserPwd(this.passwordForm.oldPassword, this.passwordForm.newPassword);
            if(res.code == 0 || res.code == 200){
              this.$message.success('密码修改成功');
              this.dialogVisible = false;
              // this.isShow = true;
              this.resetForm();
              let params = {
                pageNum:1,
                pageSize:1,
                noticeTitle:this.$store.state.user.orgCode,
                noticeType:2
              }
              const resTwo = await noticeList(params);
              if(resTwo.code === 0 || resTwo.code === 200){
                if(resTwo.total > 0 && resTwo.rows.length > 0){
                  this.noticeContent = resTwo.rows[0].noticeContent
                  this.noticeListVisible = true;
                }
              }
              }else{
                this.$message.error(res.msg);
              }
          } catch (error) {
            // this.$message.error('密码修改失败');
          }
        }
      });
    },

    // 重置表单
    resetForm() {
      this.passwordForm.oldPassword = '';
      this.passwordForm.newPassword = '';
      this.passwordForm.confirmPassword = '';
      this.$refs.passwordForm.clearValidate();
    },

    // 修改isPassWeakFlase方法
    async isPassWeakFlase(){
      try{
        const res = await isPassWeak();
        if(res.code == 0 || res.code == 200){
          if(res.weakPass){
            // this.isShow = false,
            this.dialogVisible = true; // 显示修改密码弹窗
          }else{
            let params = {
              pageNum:1,
              pageSize:1,
              noticeTitle:this.$store.state.user.orgCode,
              noticeType:2
            }
            const resTwo = await noticeList(params);
            if(resTwo.code === 0 || resTwo.code === 200){
              if(resTwo.total > 0 && resTwo.rows.length > 0){
                this.noticeContent = resTwo.rows[0].noticeContent
                this.noticeListVisible = true;
              }
            }
          }
        }else{
          this.$message({
              type: 'error',
              message: res.msg
          })
        }
      }catch(err){
        console.log(err);
        // this.$message({
        //     type: 'error',
        //     message: err
        // })
      }
    },
    initBarChart() {
      this.barChart = echarts.init(this.$refs.barChart)
      const option = {
        title: {
          text: '销售趋势',
          textStyle: {
            color: '#8F939A',
            fontSize: 12,
            fontWeight: 'normal'
          },
          left: 0,
          top: 0
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              opacity: 0
            }
          },
          backgroundColor: '#FFFFFF',
          borderWidth: 0,
          padding: [8, 12],
          textStyle: {
            color: '#666666'
          },
          formatter: function(params) {
            return `${params[0].name}<br/><span style="display:inline-block;margin-right:5px;border-radius:50%;width:6px;height:6px;background-color:#00B955"></span>￥${params[0].value}`
          }
        },
        xAxis: {
          type: 'category',
          data: ['2月14', '2月15', '2月16', '2月17', '2月18', '2月19', '2月20', '2月21', '2月22'],
          axisLine: {
            lineStyle: {
              color: '#8F939A'
            }
          },
          axisLabel: {
            fontSize: 12,
            formatter: function(value, index) {
              return `{a|${value}}`
            },
            rich: {
              a: {
                color: '#8F939A',
              },
              b: {
                color: '#222222',
              }
            }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '40px',
          containLabel: true
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            fontSize: 12,
            color: '#8F939A'
          },
          axisLine: {
            lineStyle: {
              color: '#8F939A'
            }
          }
        },
        series: [
          {
            name: '销售额',
            type: 'bar',
            data: [12000, 8000, 14000, 17000, 21000, 7000, 13000, 8000, 0],
            itemStyle: {
              color: '#00B955'
            },
            barWidth: '16px'
          }
        ]
      }
      
      this.barChart.setOption(option)
      
      // 添加鼠标事件监听
      this.barChart.on('mouseover', (params) => {
        if (params.componentType === 'series') {
          const xAxisData = option.xAxis.data;
          const newLabels = xAxisData.map((item, index) => {
            return index === params.dataIndex ? `{b|${item}}` : `{a|${item}}`
          });
          
          this.barChart.setOption({
            xAxis: {
              axisLabel: {
                formatter: function(value, index) {
                  return newLabels[index]
                }
              }
            }
          });
        }
      });
      
      this.barChart.on('mouseout', () => {
        this.barChart.setOption({
          xAxis: {
            axisLabel: {
              formatter: function(value) {
                return `{a|${value}}`
              }
            }
          }
        });
      });
      
      window.addEventListener('resize', () => {
        this.barChart.resize()
      })
    },
    initMixChart() {
      this.mixChart = echarts.init(this.$refs.mixChart)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              opacity: 0
            }
          }
        },
        legend: {
          data: ['销售额', '同比增长', '环比增长']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '68px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['2024年9月', '2024年10月', '2024年11月', '2024年12月', '2025年01月', '2025年02月'],
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            fontSize: 12,
            formatter: function(value, index) {
              return `{a|${value}}`
            },
            rich: {
              a: {
                color: '#8F939A'
              },
              b: {
                color: '#222222'
              }
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '',
            min: 0,
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: '销售额',
            type: 'bar',
            data: [50000, 30000, 55000, 78000, 42000, 30000],
            itemStyle: {
              color: '#00B955'
            },
            barWidth: '16px'
          },
          {
            name: '同比增长',
            type: 'line',
            data: [52000, 36000, 57000, 45000, 44000, 43000],
            itemStyle: {
              color: '#F3A717'
            },
          },
          {
            name: '环比增长',
            type: 'line',
            data: [49000, 33000, 55000, 43000, 42000, 30000],
            itemStyle: {
              color: '#2486EE'
            },
          },
        ]
      }
      this.mixChart.setOption(option)
      
      // 添加鼠标事件监听
      this.mixChart.on('mouseover', (params) => {
        if (params.componentType === 'series') {
          const xAxisData = option.xAxis.data;
          const newLabels = xAxisData.map((item, index) => {
            return index === params.dataIndex ? `{b|${item}}` : `{a|${item}}`
          });
          
          this.mixChart.setOption({
            xAxis: {
              axisLabel: {
                formatter: function(value, index) {
                  return newLabels[index]
                }
              }
            }
          });
        }
      });
      
      this.mixChart.on('mouseout', () => {
        this.mixChart.setOption({
          xAxis: {
            axisLabel: {
              formatter: function(value) {
                return `{a|${value}}`
              }
            }
          }
        });
      });
      
      window.addEventListener('resize', () => {
        this.mixChart.resize()
      })
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
    getCurrentTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  },
};
</script>

<style lang="scss" scoped>
#noticeList{
  font-size:18px;
}
::v-deep .el-dialog__title{
  font-size:18px;
  color: red;
}
.home {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  height: 100%;
  .part{
    padding: 16px;
  }
  .side-left{
    width: 64%;
    .part01{
      height: 458px;
      .headline{
        font-weight: 500;
        color: #222222;
        font-size: 20px;
        display: flex;
        justify-content: space-between;
        font-family: PingFangSC-Medium;
        align-items: center;
        margin-bottom: 15px;
        .timer{
          color: #666666;
          font-size: 14px;
          font-weight: normal;
        }
      }
      .echarts {
        width: 100%;
        height: 252px;
        margin-top: 12px;
      }
      .card-wrap{
        height: 108px;
        .card-col{
          background: #F5F5F5;
          font-size: 24px;
          padding: 20px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .card-title{
            color: #222222;
            font-size: 16px;
            margin-bottom: 10px;
          }
          span{
            font-size: 24px;
          }

          // 添加媒体查询，在小屏幕时缩小字体
          @media screen and (max-width: 1440px) {
            font-size: 20px;
            .card-title {
              font-size: 14px;
            }
            span {
              font-size: 20px;
            }
          }
        }
        .last-card{
          height: 100%;
          padding: 15px 18px 0 18px;
          font-weight: 500;
          .list{
            margin-bottom: 5px;
            font-size: 16px;  // 默认字体大小
            .tags-msg{
              margin-bottom: 20px;
              font-weight: 400;
            }

            // 添加媒体查询，在小屏幕时缩小字体
            @media screen and (max-width: 1300px) {
              font-size: 12px;
            }
          }
        }
      }
    }
    .part02 {
      margin-top: 12px;
      position:relative;
      .chart-header {
        position: absolute;
        display: flex;
        justify-content: space-between;
        width: 100%;
        left: 0;
        top: 10px;
        padding: 0 20px;
        .chart-title {
          color: #222222;
          font-size: 20px;
          line-height: 30px;
        }
        .view-detail {
          color: #00B955;
          font-size: 14px;
          line-height: 30px;
          cursor: pointer;
        }
      }
      .echarts {
        width: 100%;
        height: 300px;
        margin-top: 0px;
      }
    }
  }
  .side-right{
    width: 35%;
    .part01{
      .headline{
        font-weight: 500;
        color: #222222;
        font-size: 20px;
        display: flex;
        justify-content: space-between;
        font-family: PingFangSC-Medium;
        align-items: center;
        margin-bottom: 15px;
        .timer{
          color: #666666;
          font-size: 14px;
          font-weight: normal;
        }
      }
      .content{
        height: 108px;
        padding: 20px;
        background:#F5F5F5;
        border-radius: 4px;
        .content-title{
          color:#222222;
          font-size: 16px;
        }
        .payment{
          color:#28272C;
          font-size: 36px;
          span{
            font-size: 24px;
          }
        }
      }
      .progess{
        margin-top:20px;
        .head-line{
          display: flex;
          justify-content: space-between;
          color: #222222;
          font-size: 16px;
        }
      }
    }
    .notice{
      margin-top: 12px;
      .head-line{
        font-weight: 500;
        color:#222222;
        font-size: 20px;
      }
      .list-wrap{
        margin-top:16px;
        color:#333333;
        font-size: 16px;
        .list{
          margin-bottom: 16px;
        }
        .list:hover{
          color: #00B955;
          cursor: pointer;
        }
      }
    }
  }
}
::v-deep .el-card .el-card__body{
  padding: 0
}
::v-deep .el-progress-bar{
  padding: 0;
}
::v-deep .el-progress.is-success .el-progress-bar__inner {
  background-color: #00B955;
}
::v-deep .el-icon-circle-check {
  display: none; // 隐藏外部百分比和图标
}

::v-deep .el-dialog {
  margin-top: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

::v-deep .el-dialog__body {
  padding: 20px 30px;
}
</style>

