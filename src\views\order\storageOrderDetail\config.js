export function orderDetailCol(){
  return [
      { field: 'orgName', title: '合作仓库', width: 220 },
      { field: 'supplyCode', title: '供货单号', width: 160 },
      { field: 'createTimeStr', title: '订单下单时间', width: 160 },
      { field: 'businessOutCode', title: '本公司出库单号', width: 160},
      { field: 'storageCode', title: '入库单号', width: 160},
      { field: 'erpProductCode', title: '本公司商品编码', width: 200},
      { field: 'csuid', title: '商品编码', width: 160},
      { field: 'productCode', title: '产品编码', width: 160},
      { field: 'standardProductId', title: '标准库id', width: 160},
      { field: 'productName', title: '商品名称', width: 200},
      { field: 'manufacturer', title: '生产厂家', width: 200},
      { field: 'supplyPrice', title: '供货单价', width: 200},
      { field: 'supplyCount', title: '供货数量', width: 200},
      { field: 'storageCount', title: '实际入库数量', width: 200},
      { field: 'storageAmount', title: '实际入库金额', width: 200},
      { field: 'storeTimeStr', title: '实际入库时间', width: 200},
      { field: 'batchCode', title: '已收货批号', width: 200},
      { field: 'manufactureTime', title: '生产日期', width: 200},
      { field: 'validityTime', title: '有效期至', width: 200}
  ]
}
