@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  height: calc(100vh - 80px);
  overflow: auto;
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}
.app-container {
  padding: 10px 0;
}
.clickWriting{
  color:#00B955;
  cursor:pointer;
}
.clickWriting:hover{
  color:#00B955;
  cursor:pointer;
}
.el-checkbox__input.is-checked + .el-checkbox__label,
.el-tabs__item.is-active,
.el-tabs__item:hover{
  color:#00B955;
}
.el-tabs__active-bar {
  background: #00B955;
}
.el-switch.is-checked .el-switch__core{
  background: #00B955;
  border-color: #00B955;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner,
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-button--primary,
.el-button--small:hover {
  color: #FFFFFF;
  background-color: #00B955;
  border-color: #00B955;
}
.el-checkbox__inner,
.el-checkbox__inner:hover,
.el-checkbox__inner:enabled,
.el-checkbox__inner:focus,
.el-checkbox__inner:active,
.el-checkbox__inner:not(:checked) {
  border-color: #00B955;
}
.vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before,
.vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before {
  background-color: #00B955 !important;
  border-color: #00B955 !important;
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--unchecked-icon:hover,
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon:hover::before {
  border-color: #00B955 !important;
}
.el-input__inner {
  height: 38px;
  line-height: 38px;
  width: 100%;
}
.el-form-item__content .el-date-editor.el-input__inner{
  width: 100%;
}
.vxe-table--main-wrapper .el-input__inner{
  height: 30px;
}
/********el-descriptions start**********/
.el-descriptions-label {
  width: 180px;
  max-width: 150px;
}

.el-descriptions-content {
  max-width: 1px;
}

.el-descriptions-desc {
  max-height: 500px;
  overflow: auto;
}

.el-descriptions-label-small {
  width: 100px;
  max-width: 70px;
}

.el-input {
  .el-input__inner {
    &:focus {
      border-color: #00B955 !important;
      border-width: 1px !important;  // 保持和 hover 状态一样的边框宽度
      box-shadow: none !important;   // 移除阴影效果
    }
    &:hover {
      border-color: #00B955 !important;
      border-width: 1px !important;
    }
  }
}
/********修改默认primary按钮颜色**********/
.el-button--primary {
   background-color: #00B955 !important;
   border-color: #00B955 !important;
  &:hover,
  &:focus {
    background-color: #33C775 !important;  // 可以设置略浅一点的颜色作为悬停状态
  }
  &:active {
    background-color: #009944 !important;  // 可以设置略深一点的颜色作为激活状态
  }
  &.is-loading {
    background-color: #00B955 !important;
    border-color: #00B955 !important;
    &:before {
      background-color: rgba(255, 255, 255, 0.35);
    }
  }
}
.el-button--primary span{
  color: #fff;
}
.el-button--success:hover, .el-button--success:focus {
  background: #42d885 !important;
  border-color: #42d885 !important;
  color: #FFFFFF !important;
}
.el-button--info:hover, .el-button--info:focus {
  background: #a6a9ad !important;
  border-color: #a6a9ad !important;
  color: #FFFFFF !important;
}
.el-button--warning:hover, .el-button--warning:focus {
  background: #ffc833 !important;
  border-color: #ffc833 !important;
  color: #FFFFFF !important;
}
.el-button--danger:hover, .el-button--danger:focus {
  background: #ff6d6d !important;
  border-color: #ff6d6d !important;
  color: #FFFFFF !important;
}
.el-button--default:hover, .el-button--default:focus{
  background: #dbffec;
  border-color: #00B955;
  color: #00B955;
}
.el-button--primary.is-plain {
  border-color: #33C775 !important;
  background: #ebffef !important;
  span{
    color: #009944;
  }
  &:hover,
  &:focus{
    background-color: #33C775 !important;  // 可以设置略浅一点的颜色作为悬停状态
    border-color: #33C775 !important;
    span{
      color: #FFFFFF;
    }
  }
}
.el-button--success.is-plain:hover, .el-button--success.is-plain:focus {
  background: #13ce66;
  border-color: #13ce66;
  color: #FFFFFF;
}
.el-button--info.is-plain:hover, .el-button--info.is-plain:focus {
  background: #909399;
  border-color: #909399;
  color: #FFFFFF;
}
.el-button--warning.is-plain:hover, .el-button--warning.is-plain:focus {
  background: #ffba00;
  border-color: #ffba00;
  color: #FFFFFF;
}
.el-button--danger.is-plain:hover, .el-button--danger.is-plain:focus {
  background: #ff4949;
  border-color: #ff4949;
  color: #FFFFFF;
}
.el-button.is-plain:hover, .el-button.is-plain:focus {
  background: #dbffec;
  border-color: #00B955;
  color: #00B955;
}
.el-button span{
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
}
.btn-status-wrap div{
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 14px
}
.vxe-cell div{
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
}
.vxe-cell--title div{
  font-family: PingFangSC-Medium;
  font-weight: 600;
  font-size: 14px;
  color: #222222;
}
.vxe-cell--title{
  font-family: PingFangSC-Medium;
  font-weight: 600;
  font-size: 14px;
  color: #222222;
}
.vxe-body--row{
  height: 55px !important;
}
// .el-dropdown-menu__item:not(.is-disabled):hover, .el-dropdown-menu__item:focus{
//   background:#00B955; 
// }
.el-radio__input.is-checked .el-radio__inner{
  &:hover{
    background:#00B955;
    color: #ffffff;
  }
  &:focus{
    background:#00B955;
    color: #ffffff;
  }
}
// .el-dropdown-menu__item:not(.is-disabled):hover, 
// .el-dropdown-menu__item:focus{
//   background:#00B955;
//   color: #ffffff;
// }
.el-select{
  width: 100%;
}
.el-form{
  padding-right: 10px;
}
.el-radio__inner:hover,
.el-range-editor.is-active:hover{
  border-color: #33C775 !important;
}
.el-date-table td.start-date span,
.el-date-table td.end-date span,
.el-date-table td.start-date span:hover,
.el-date-table td.end-date span:hover{
  border-color: #33C775 !important;
  background:#00B955;
  color: #ffffff !important;
}
.vxe-loading .vxe-loading--spinner:after, 
.vxe-loading .vxe-loading--spinner:before{
  border-color: #33C775 !important;
  background:#00B955 !important;
}
.el-date-table td.in-range div{
  background:#e8fef2;
}
.el-date-table td.today span,
.el-date-table td.in-range div:hover{
  background:#e8fef2;
  color: #33C775 !important;
}
.el-select-dropdown__item.selected,
.el-date-table td.available:hover{
  color: #33C775 !important;
}
.vxe-pager .vxe-pager--num-btn:not(.is--disabled).is--active{
  border-color: #33C775 !important;
  color: #33C775 !important;
}
.vxe-select-option.is--selected{
  color: #33C775 !important;
}
.vxe-table--render-default .vxe-body--row.row--current{
  background-color: #f5fffa !important;
}
.vxe-table--render-default{
  color: #000000 !important;
}
/**pageloading**/
.el-loading-mask{
  .el-loading-spinner {
      i {
          color: #33C775 !important;
      }

      .el-loading-text {
          color: #33C775 !important;
      }
  }
}
.vxe-table--body-wrapper{
  height: auto !important;
}

.el-dialog__body{
  padding: 0;
}