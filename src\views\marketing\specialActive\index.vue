<template>
    <div class="app-container">
      <div class="tips">
        <div class="head">
          活动说明：
        </div>
        <p>
          1、特价活动可选择普通商品，在指定时间内设置促销价格，到期自动恢复原价。
        </p>
        <p>
          2、活动价需低于普通商品销售价及平台建议价，平台会每日更新平台建议价，高于建议价的活动会失效。
        </p>
        <p>
          3、系统会根据活动覆盖区域、客户类型、商品剂型进行差异化加点，设置的活动价格非客户可见价格。
        </p>
      </div>

      <xyy-panel title="查询条件">
        <!-- 按钮组 start-->
      <btn-group slot="tools" :btn-list="btnListTop"/>
      <el-form ref="formData" :model="formData" label-width="120px" class="clearfix">
        <el-row :gutter="10">
          <el-col :lg="8" :md="8">
            <el-form-item label="活动名称" prop="title">
              <el-input v-model.trim="formData.title" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="8">
            <el-form-item label="商品编码" prop="csuId">
              <el-input v-model.trim="formData.csuId" placeholder="请输入" @input="handleInput"/>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="8">
            <el-form-item label="本公司编码" prop="erpProductCode">
              <el-input v-model.trim="formData.erpProductCode" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :lg="8" :md="8">
            <el-form-item label="商品名称" prop="skuName">
              <el-input v-model.trim="formData.skuName" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="8">
            <el-form-item label="活动ID" prop="promotionId">
              <el-input v-model.trim="formData.promotionId" placeholder="请输入" @input="handleInputP"/>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="8">
            <el-form-item label="活动时间" prop="activeTime">
              <el-date-picker v-model="formData.activeTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"
                style="width: 100%"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :lg="8" :md="8">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker v-model="formData.createTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"
                :picker-options="pickerOptions" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      </xyy-panel>
      <xyy-panel :titleShow="false">
        <div class="redTips" v-if="countEnum.invalidSkuCount > 0 "><img src="@/assets/images/tips.svg" alt="" style="width: 16px;height: 16px; margin: 12px"><div>{{ countEnum.invalidSkuCount }}个商品价格高于平台建议价，活动失效  <span style="color: #222222;cursor: pointer; margin-left:24px; color: #00B955;" @click="statusHandler(false)" v-if="formData.valid === 1">查看</span> <span v-else style="margin-left: 24px; cursor: pointer; color: #00B955;" @click="reset" >取消查看</span></div></div>
        <div style="position: relative;">
          <!-- 按钮组 start -->
         <btn-group slot="tools" :btn-list="btnListTabale" style="position: absolute; right: 0; top: 0; " />
        <div class="btn-status-wrap">
            <div :class="['btn-status', statistics == '' ? 'status-activity' : '']" @click="statusHandler('')">
                全部（{{countEnum.totalCount}}）</div>
            <div :class="['btn-status btn-status-right', statistics == '2' ? 'status-activity' : '']"
                 @click="statusHandler(2)">未开始（{{countEnum.unStartCount}}）</div>
            <div :class="['btn-status btn-status-right', statistics == '3' ? 'status-activity' : '']"
              @click="statusHandler(3)">进行中（{{countEnum.startingCount}}）</div>
            <div :class="['btn-status btn-status-right', statistics == '5' ? 'status-activity' : '']"
              @click="statusHandler(5)">已结束（{{countEnum.stopOrOffLineCount}}）</div>
          </div>
        </div>
        <div :key="tabKey" class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData" :key="tableKey" empty-text="暂未查询到匹配的记录">
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">

                                <div v-if="item.field === 'operation1'">
                                    <span class="clickWriting" style="margin-right: 10px;" v-if="slotProps.row.statusText !== '已结束'" @click="viewHandler('edit',slotProps.row)">修改</span>
                                    <span class="clickWriting" style="margin-right: 10px;" @click="viewHandler('view',slotProps.row)">查看</span>
                                    <span class="clickWriting" style="margin-right: 10px;" v-if="slotProps.row.statusText !== '已结束'" @click="closeActive(slotProps.row)">结束活动</span>
                                    <span class="clickWriting" style="margin-right: 10px;" @click="operationLogHandler(slotProps.row)">活动变更日志</span>
                                </div>
                                <div v-else-if="item.field === 'activeTime'">
                                    <span>{{slotProps.row.startTime }} 至 {{slotProps.row.endTime}}</span>
                                </div>
                                <div v-else-if="item.field === 'createTime'">
                                    <span>{{slotProps.row.createTime}}</span>
                                </div>
                                <div v-else>
                                    {{ slotProps.row[item.field] }}
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>

      <div class="pager">
       <vxe-pager
        border
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :page-sizes="tablePage.pageSizes"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total',
        ]"
        @page-change="handlePageChange"
      />
      </div>

      </xyy-panel>
      <OperationLog  ref="operationLog"/>
    </div>
</template>

<script>
import {closeSpecial,getSpecialList,getSpecialCount} from '@/api/marketing/specialActive'
import { exportFile } from '@/api/system/exportCenter'
import { pageConfig }  from "@/utils";
import { listColumns } from './config'
import OperationLog from './components/operationLog.vue'
import XEUtils from 'xe-utils'
const end = new Date();
const start = XEUtils.getWhatDay(end, -6); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd'); // 将结束日期格式化为字符串
export default {
    name: 'SpecialActive',
    components: {
      OperationLog
    },
    data() {
        return {
          formData: {
            businessStatus: '', // '' 全部 2未开始 3进行中 5已结束
            valid: 1,//有效标志 1有效0无效
            csuId:'', //商品编码
            erpProductCode:'', //本公司编码
            skuName:'', //商品名称
            title:'', //活动名称
            promotionId:'', //活动id
            activeTime:null, //活动时间
            createTime:null //创建时间
          },
          countEnum:{},
          btnListTop: [
            {
              label: "查询",
              type: "primary",
              clickEvent: this.searchListAndCount,
              code: "",
              plain: 'false',
              permission:'specialActive:list:query'
            },
            {
              label: "重置",
              clickEvent: this.reset,
              code: "",
              plain: 'false',
              permission:'specialActive:list:reset'
            }
          ],
          btnListTabale:[
            {
              label: "新增",
              type: "primary",
              clickEvent: this.addHandler,
              code: "",
              plain: 'false',
              permission:'specialActive:list:add'
            },
            {
              label: "导出",
              clickEvent: this.exportHandler,
              code: "",
              plain: 'false',
              permission:'specialActive:list:export'
            }
          ],
           // 时间限制
           pickerOptions: {
              disabledDate(time) {
                const today = new Date();
                today.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59.999
                return time.getTime() > today.getTime()
              },
            },
            tableData:[{}],
            tablePage: pageConfig(),
            statistics:'',//列表状态
            tableKey:0,
            tabKey:0,
            loading:false,
            tableColumns:listColumns(),
            row:{}
        }
    },
    mounted() {
     this.searchListAndCount()
    },
    activated(){
      this.searchListAndCount()
    },
    methods: {
      handleInput(value) {
      // 使用正则表达式，替换非数字字符
      this.formData.csuId = value.replace(/[^\d]/g, '');
    },
    handleInputP(value) {
      // 使用正则表达式，替换非数字字符
      this.formData.promotionId = value.replace(/[^\d]/g, '');
    },
      // 查询列表
      searchList(flag) {
        this.loading = true;
        if(flag !== true){
          this.tablePage.pageNum = 1;
        }
        const params = {
          pageNum: this.tablePage.pageNum,
          pageSize: this.tablePage.pageSize,
          csuId: this.formData.csuId,
          erpProductCode: this.formData.erpProductCode,
          skuName: this.formData.skuName,
          title: this.formData.title,
          promotionId: this.formData.promotionId,
          businessStatus: this.formData.businessStatus,
          valid: this.formData.valid === 1 ? undefined : 0,
          startTime: this.formData.activeTime ? this.formData.activeTime[0] : '',
          endTime: this.formData.activeTime ? this.formData.activeTime[1]  : '',
          createStartTime: this.formData.createTime ? this.formData.createTime[0]  : '',
          createEndTime: this.formData.createTime ? this.formData.createTime[1] : '',
        }
        getSpecialList(params).then(res => {
          this.tableData = res.result.list ? res.result.list : [];
          this.tablePage.total = res.result.total

        }).finally(() => {

          this.loading = false;
        })


      },
     // 查询列表和统计数据
     searchListAndCount(flag) {
        this.loading = true;
        if(flag !== true){
          this.tablePage.pageNum = 1;
        }
        const params = {
          pageNum: this.tablePage.pageNum,
          pageSize: this.tablePage.pageSize,
          csuId: this.formData.csuId,
          erpProductCode: this.formData.erpProductCode,
          skuName: this.formData.skuName,
          title: this.formData.title,
          promotionId: this.formData.promotionId,
          businessStatus: this.formData.businessStatus,
          valid: this.formData.valid === 1 ? undefined : 0,
          startTime: this.formData.activeTime ? this.formData.activeTime[0] : '',
          endTime: this.formData.activeTime ? this.formData.activeTime[1]  : '',
          createStartTime: this.formData.createTime ? this.formData.createTime[0]  : '',
          createEndTime: this.formData.createTime ? this.formData.createTime[1]  : '',
        }
        getSpecialList(params).then(res => {
          this.tableData = res.result.list ? res.result.list : [];
          this.tablePage.total = res.result.total

        }).finally(() => {
          this.getCount()
          this.loading = false;
        })


      },
      // 查询数量
      getCount(){
        const params = {
          pageNum: this.tablePage.pageNum,
          pageSize: this.tablePage.pageSize,
          csuId: this.formData.csuId,
          erpProductCode: this.formData.erpProductCode,
          skuName: this.formData.skuName,
          title: this.formData.title,
          promotionId: this.formData.promotionId,
          //businessStatus: this.formData.businessStatus,
          valid: this.formData.valid === 1 ? undefined : 0,
          startTime: this.formData.activeTime ? this.formData.activeTime[0]  : '',
          endTime: this.formData.activeTime ? this.formData.activeTime[1] : '',
          createStartTime: this.formData.createTime ? this.formData.createTime[0]  : '',
          createEndTime: this.formData.createTime ? this.formData.createTime[1] : '',
        }
        getSpecialCount(params).then(res => {
          if(res.code == 0){
            this.countEnum = res.result
          }else{
            this.$message({
              type: 'error',
              message: res.msg
            })
          }

        })
      },
      // 分页器改变处理
      handlePageChange({currentPage, pageSize}) {
          this.tablePage.pageNum = currentPage;
          this.tablePage.pageSize = pageSize;
          this.searchList(true)
        },
      reset() {
        this.tablePage.pageSize = 20
        this.formData.businessStatus = ''
        this.statistics = ''
        this.formData.valid = 1
        this.$refs.formData.resetFields();
        this.formData.activeTime = null
        this.searchListAndCount();
      },
      //查询状态变更
      statusHandler(status) {
        if(status === false){
          this.statistics = ''
          this.formData.businessStatus = ''
          this.formData.valid = 0
          this.searchListAndCount()

        }else{
          this.statistics = status
          this.formData.businessStatus = status
          this.searchList()
        }



      },
      //活动变更日志
      operationLogHandler(row) {
        this.row = row
        this.$refs.operationLog.open(row)
      },
      //结束活动
      closeActive(row){
        this.$confirm('确认结束活动吗！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
              closeSpecial({promotionId:row.promotionId}).then( res => {
                if(res.code == 0){
                  this.$message({
                    type: 'success',
                    message: '结束活动成功!'
                  })
                }else{
                  this.$message({
                    type: 'error',
                    message: res.msg
                  });
                }
              }).finally(() => {
                this.searchListAndCount()
              })

            })
      },
      //导出
      exportHandler(){
        if (this.tableData.length === 0) {
            this.$message.warning('暂无数据可导出')
            return
          }
          const formInfo = {
          pageNum: this.tablePage.pageNum,
          pageSize: this.tablePage.pageSize,
          ...this.formData,
          startTime: this.formData.activeTime ? this.formData.activeTime[0]  : '',
          endTime: this.formData.activeTime ? this.formData.activeTime[1]  : '',
          createStartTime: this.formData.createTime ? this.formData.createTime[0] : '',
          createEndTime: this.formData.createTime ? this.formData.createTime[1]  : '',
        }
        let params = {
              taskBean: 'specialPromotionExportTask',
              colNameDesc: '合作仓库,活动ID,活动名称,活动开始时间,活动结束时间,活动状态,本公司商品编码,商品编码,商品名称,规格,厂家,商品剂型,销售价,活动价,库存,商品状态,活动创建时间',
              colName: 'orgName,promotionId,promotionName,startTime,endTime,statusText,erpProductCode,productCode,productName,spec,manufacturer,dosageForm,suggestPrice,activityPrice,stock,productStatus,createTime',
              moduleName: 'PROMOTION',
              menuDesc: '特价活动明细',
              exportParams: JSON.stringify(formInfo)
          }

          this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
      },
      // 查看
      viewHandler(type,row){
        if(type === 'edit'){
          this.$router.push({
            path: '/marketing/SpecialActiveEdit',
            query: { promotionId: row.promotionId }
          })
        } else if(type === 'view'){
          this.$router.push({
          path: '/marketing/SpecialActiveView',
          query: { promotionId: row.promotionId }
        })
        } else{
          this.$router.push({
            path: '/marketing/SpecialActiveAdd',
          })
        }

      },
      // 新增
      addHandler(){
        this.$router.push({
          path: '/marketing/SpecialActiveAdd',
        })
      }
    }
}
</script>

<style scoped lang="scss">
.tips {
  padding: 10px 15px;
  background-color: #f5f5f5;
  .head {
    color: #222222;
    margin-bottom: 10px;
    font-weight: 500;
    font-size: 14px;
  }

  p {
    color: #222222; // orange-red color
    margin: 8px 0;
    font-size: 14px;
    font-weight: 400;
  }
}
.redTips{
  background-color: #FEF0F0;
  height: 38px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;

  img {
    width: 16px;
    height: 16px;
    margin: 0 12px;
  }

  div {
    display: flex;
    align-items: center;

    span {
      display: flex;
      align-items: center;

      img {
        margin:0; // 只保留左边距
      }
    }
  }
}
.btn-status-wrap {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    font-size: 12px;
}

.btn-status-wrap .btn-status {
    border-color: #ccc;
    background-color: #f5f5f5;
    color: #333;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: default;
}

.btn-status-wrap .btn-status-right {
    margin-left: 10px;
}

.btn-status-wrap .status-activity {
    border: 1px solid #67c23a;
    background-color: #ffffff;
}

.btn-status-wrap .btn-status:hover {
    border-color: #67c23a;
    color: #333;
}
</style>

