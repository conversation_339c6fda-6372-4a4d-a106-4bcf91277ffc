<template>
    <div class="app-container">
        <xyy-panel :titleShow="false">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTop" />
            <el-row :gutter="20">
                <el-col :lg="6" :md="6">
                    提现申请单号：{{ formData.withdrawNo }}
                </el-col>
                <el-col :lg="6" :md="6">
                    申请时间：{{ formData.applyDateStr }}
                </el-col>
                <el-col :lg="6" :md="6">
                    申请提现金额：{{ formData.amount }}
                </el-col>
                <el-col :lg="6" :md="6">
                    银行回执单：
                    <span class="clickWriting" @click="downloadReceipt()" v-if="formData.bankReceipt">下载回执单</span>
                    <span style="color: gray;" v-else>暂无回执单</span>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :lg="6" :md="6">
                    开户行：{{ formData.openBank }}
                </el-col>
                <el-col :lg="6" :md="6">
                    开户名称：{{ formData.receiveName }}
                </el-col>
                <el-col :lg="6" :md="6">
                    银行账号：{{ formData.bankAccount }}
                </el-col>
                <el-col :lg="6" :md="6">
                    打款时间：{{ formData.payTimeStr }}
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :lg="6" :md="6">
                    唯一值：{{ formData.uniqueKey }}
                </el-col>
                <el-col :lg="6" :md="6">
                    申请人：{{ formData.operator }}
                </el-col>
                <el-col :lg="6" :md="6">
                    打款状态：{{ formData.payStatusStr }}
                </el-col>
                <el-col :lg="6" :md="6">
                    失败原因：{{ formData.failureReason }}
                </el-col>
            </el-row>
        </xyy-panel>
        <xyy-panel :titleShow="false">
            <div class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" empty-text="暂无提现记录明细数据">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'invoiceStatusStr'">
                                    <el-tag type="info" v-if="slotProps.row.invoiceStatus == -1"
                                        style="font-size: 14px;">{{
                                            slotProps.row.invoiceStatusStr }}</el-tag>
                                    <el-tag type="warning" v-else-if="slotProps.row.invoiceStatus == 0"
                                        style="font-size: 14px;">{{ slotProps.row.invoiceStatusStr }}</el-tag>
                                    <el-tag type="primary" v-else-if="slotProps.row.invoiceStatus == 1"
                                        style="font-size: 14px;">{{ slotProps.row.invoiceStatusStr }}</el-tag>
                                    <el-tag type="success" v-else-if="slotProps.row.invoiceStatus == 2"
                                        style="font-size: 14px;">{{ slotProps.row.invoiceStatusStr }}</el-tag>
                                </div>
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
        </xyy-panel>
    </div>
</template>

<script>
import { getWithdrawalHistoryDetail, getWithdrawalHistoryDetailByOrderNo } from "@/api/finance/withdrawalHistory";
import { withdrawalDetailColumns } from "../config";
import { downloadInvoiceFile } from '@/api/finance/InvoiceLedger'
export default {
    data() {
        return {
            btnListTop: [
                {
                    label: "返回",
                    clickEvent: this.backToPage,
                    plain: 'false',
                    permission: "finance:withdrawalDetails:back"
                },
            ],
            formData: {
                withdrawNo: "", //提现申请单号
                applyDateStr: "", //申请时间
                amount: "", //申请提现金额
                bankReceipt: false, //银行回执单
                openBank: "", //开户行
                receiveName: "", //开户名称
                bankAccount: "", //银行账号
                approvalEndTimeStr: "", //打款时间
                uniqueKey: "", //唯一值
                operator: "", //申请人
                payStatusStr: "", //打款状态
                failureReason: "", //失败原因
            },
            loading: false, //加载中
            tableData: [],  //表格数据
            tableKey: 0,    //表格刷新
            tableColumns: withdrawalDetailColumns(),
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.apiGetWithdrawalHistoryDetailByOrderNo()
            this.apiGetWithdrawalHistoryDetail()
        })
    },
    methods: {
        // 下载回执单
        downloadReceipt() {
            if (!this.formData.receiptUrl) {
                this.$message.warning('暂无银行回执单，请稍后再试下')
                return
            }
            downloadInvoiceFile(this.formData.receiptUrl).then(res => {
                let blob = new Blob()
                if (this.formData.receiptUrl.includes('pdf')) {
                    blob = new Blob([res], { type: 'application/pdf' })
                } else {
                    blob = new Blob([res], { type: 'image/jpeg' })
                }
                const link = document.createElement('a')
                link.href = window.URL.createObjectURL(blob)
                link.download = `${this.formData.payTimeStr}_提现回执单.${this.formData.receiptUrl.includes('pdf') ? 'pdf' : this.formData.receiptUrl.includes('png') ? 'png' : 'jpg'}`
                document.body.appendChild(link)
                link.click()
                window.URL.revokeObjectURL(link.href)
                document.body.removeChild(link)
            })
        },
        // 获取提现记录明细
        apiGetWithdrawalHistoryDetailByOrderNo() {
            getWithdrawalHistoryDetailByOrderNo({
                withdrawNo: this.$route.query.withdrawNo
            }).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.formData = data
                    if (data.payStatus == 3) {
                        this.formData.bankReceipt = true
                    }
                } else {
                    this.$message.error(msg)
                }
            })
        },
        // 获取提现记录明细
        apiGetWithdrawalHistoryDetail() {
            this.loading = true
            const params = {
                withdrawNo: this.$route.query.withdrawNo,
                pageNum: 1,
                pageSize: 1000
            }
            getWithdrawalHistoryDetail(params).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.tableData = data.list
                    this.loading = false
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            })
        },
        //返回
        backToPage() {
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
        },
    }
}
</script>

<style></style>