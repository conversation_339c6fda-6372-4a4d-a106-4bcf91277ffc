import request from '@/utils/request'

// 查询商圈列表查询
export function queryList(data) {
  return request({
    url: '/busArea/list',
    method: 'post',
    data: data
  })
}
// 商品绑定商圈
export function bindBusArea(data) {
  return request({
    url: '/product/bindBusArea',
    method: 'post',
    data: data
  })
}
// 添加商圈
export function add(data) {
  return request({
    url: '/busArea/add',
    method: 'post',
    data: data
  })
}
// 编辑商圈
export function edit(data) {
  return request({
    url: '/busArea/edit',
    method: 'post',
    data: data
  })
}
// 省市区列表查询
export function querySaleableArea(data) {
  return request({
    url: '/busArea/querySaleableArea',
    method: 'post',
    data: data
  })
}
// 查询商圈详情（编辑）
export function getBusArea(data) {
  return request({
    url: '/busArea/get',
    method: 'post',
    data: data
  })
}
// 查询商圈详情
export function getBusAreaView(data) {
  return request({
    url: '/busArea/getView',
    method: 'post',
    data: data
  })
}
// 查询商圈变更日志
export function getLogs(data) {
  return request({
    url: '/busArea/logs',
    method: 'post',
    data: data
  })
}
// 状态修改
export function updateStatus(data) {
  return request({
    url: '/busArea/updateStatus',
    method: 'post',
    data: data
  })
}
// 删除商圈
export function deleteBusArea(data) {
  return request({
    url: '/busArea/delete',
    method: 'post',
    data: data
  })
}
// 查看商品
export function selectProductByBusId(data) {
  return request({
    url: '/busArea/selectProductByBusId',
    method: 'post',
    data: data
  })
}
// 查看商业
export function busBindProducts(data) {
  return request({
    url: '/busArea/busBindProducts',
    method: 'post',
    data: data
  })
}