export function tableColumns(){
    return [
        { field: 'withdrawNo', title: '提现申请单号', width: 230 },
        // { field: 'uniqueKey', title: '唯一值', width: 230 },
        { field: 'withdrawTypeStr', title: '提现单类型', width: 230 },
        { field: 'applyDateStr', title: '申请时间', width: 230 },
        { field: 'amount', title: '申请提现金额', width: 230 },
        { field: 'openBank', title: '开户行', width: 230 },
        { field: 'receiveName', title: '收款人', width: 230 },
        { field: 'bankAccount', title: '银行账号', width: 230 },
        { field: 'operator', title: '操作人', width: 230 },
        { field: 'payStatusStr', title: '打款状态', width: 230 },
        { field: 'approvalStatusStr', title: '审核状态', width: 230 },
        { field: 'approvalEndTimeStr', title: '审核时间', width: 230 },
        { field: 'payTimeStr', title: '打款时间', width: 230 },
        { field: 'failureReason', title: '失败原因', width: 230 },
        { field: 'operation', title: '操作', width: 230, fixed: 'right' },
    ]
}

export function withdrawalDetailColumns(){
    return [
        { field: 'orderNo', title: '供货单号', width: 230 },
        { field: 'sourceNo', title: '单据编号', width: 230 },
        { field: 'sourceTypeStr', title: '单据类型', width: 230 },
        { field: 'orderTimeStr', title: '单据日期', width: 230 },
        { field: 'orderCount', title: '单据数量', width: 230 },
        { field: 'totalInvoiceAmount', title: '实际金额（元）', width: 230 },
        { field: 'invoiceStatusStr', title: '发票状态', width: 230 },
        { field: 'enteredAmount', title: '发票金额（元）', width: 230 },
        { field: 'writeOffAmount', title: '已核销金额（元）', width: 230 },
        { field: 'waitWriteOffAmount', title: '待核销金额（元）', width: 230 },
        { field: 'amount', title: '本次核销金额（元）', width: 230 },
    ]
}

export function requestWithdrawalColumns(){
    return [
        { field: 'orderNo', title: '供货编号', width: 180 },
        { field: 'sourceNo', title: '单据编号', width: 180 },
        { field: 'sourceTypeName', title: '单据类型', width: 180 },
        { field: 'orderTimeStr', title: '单据日期', width: 180 },
        { field: 'orderCount', title: '单据数量', width: 180 },
        { field: 'totalInvoiceAmount', title: '实际金额（元）', width: 180 },
        { field: 'invoiceStatusName', title: '发票状态', width: 180 },
        { field: 'enteredAmount', title: '发票金额（元）', width: 180 },
        { field: 'writeOffAmount', title: '已核销金额（元）', width: 180 },
        { field: 'waitWriteOffAmount', title: '待核销金额（元）', width: 180, fixed: 'right' },
        { field: 'amount', title: '本次核销金额', width: 180, fixed: 'right' },
    ]
}

export function autoWriteOffDetailColumns(){
    return [
        { field: 'sourceNo', title: '单据编号', width: 230 },
        { field: 'sourceTypeStr', title: '单据类型', width: 230 },
        { field: 'orderTimeStr', title: '单据日期', width: 230 },
        { field: 'orderCount', title: '单据数量', width: 230 },
        { field: 'totalInvoiceAmount', title: '实际金额（元）', width: 230 },
        { field: 'invoiceStatusStr', title: '发票状态', width: 230 },
        { field: 'enteredAmount', title: '发票金额（元）', width: 230 },
        { field: 'writeOffAmount', title: '已核销金额（元）', width: 230 },
        { field: 'waitWriteOffAmount', title: '待核销金额（元）', width: 230 },
        { field: 'amount', title: '本次核销金额', width: 230, fixed: 'right' },
    ]
}