import fix from "highlight.js/lib/languages/fix"

export function tableColumns(){
    return [
        { field: 'settleNo', title: '结算单号', width: 230 },
        { field: 'orderNo', title: '销售单号', width: 230 },
        { field: 'tenantName', title: '商业主体', width: 230 },
        { field: 'settleDateStr', title: '结算日期', width: 230 },
        { field: 'outStoreNumber', title: '整单销售数量', width: 230 },
        { field: 'basePayMoney', title: '整单销售金额（含税）', width: 230 },
        // { field: 'commissionRate', title: '佣金率', width: 220 },
        { field: 'totalRebateMoney', title: '整单商业返利', width: 230 },
        { field: 'totalSettleMoney', title: '整单结算金额(元)', width: 230 },
        { field: 'operation', title: '操作', width: 230, fixed: 'right' },
    ]
}

export function detailColumns(){
return [
        { field: 'settleNo', title: '结算单号', width: 220 },
        { field: 'orderNo', title: '销售单号', width: 160 },
        { field: 'settleDateStr', title: '结算日期', width: 160 },
        { field: 'productName', title: '商品名称', width: 160},
        { field: 'csuid', title: '商品编码', width: 160},
        { field: 'batchCode', title: '批号', width: 160},
        { field: 'outStoreNumber', title: '销量', width: 160},
        { field: 'basePayPrice', title: '销售单价（含税）', width: 160},
        { field: 'basePayAmount', title: '销售金额（含税）', width: 160},
        { field: 'rebateMoney', title: '商业返利', width: 160},
        { field: 'settleMoney', title: '结算金额(元)', width: 160},
]
}