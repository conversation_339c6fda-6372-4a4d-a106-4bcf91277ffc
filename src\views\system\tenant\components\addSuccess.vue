<template>
 <el-dialog
  :title="title"
  :visible.sync="dialogVisible"
  width="50%"
  :before-close="cancel">
  <xyy-panel :titleShow="false">
    <vxe-table ref="xTable"
        height="auto"
        :data="tableData"
        >
          <template>
            <vxe-table-column v-for="item in tableColumn" :key="item.field" :field="item.field" :title="item.title" :min-width="item.width" :fixed="item.fixed">
              <template v-slot:default="slotProps">
                <div>
                  {{ slotProps.row[item.field] }}
                </div>
              </template>
            </vxe-table-column>
          </template>
        </vxe-table>

  </xyy-panel>
  <div style="text-align: center; font-size: 16px; color: red;" v-if="unOpenOrg.length !== 0 && title==='添加成功' ">请联系{{unOpenOrg.join(',')}}采购确认供应商信息和状态</div>
  <div style="text-align: center; font-size: 16px; color: red;" v-else-if="unOpenOrg.length !== 0 ">
    <div v-for="item in unOpenOrg" style="margin: 5px 0;" :key="item.name">
      请联系{{item.unOpenOrgCodeNames}}采购确认{{item.name}}信息和状态
    </div>
  </div>
 </el-dialog>

</template>
<script>
import{addColumns} from "../config";
export default {
  name: "AddSuccess",
  props: {

    // data: {
    //   type: Object,
    //   default: () => {}
    // }

  },
  data() {
    return {
      tableColumn: addColumns(),
      dialogVisible: false,
      title: "",
      tableData: [],
      unOpenOrg: [],
    };
  },
  methods: {
    cancel() {
      this.dialogVisible = false;
    },
    open(data) {
      this.tableData = data.data;
      this.title = data.title;
      this.unOpenOrg = data.unOpenOrg;

      this.dialogVisible = true;
    }
  }
};
</script>
<style lang="scss" scoped>
  // ::v-deep .vxe-table--body-wrapper{
  //   height:230px !important;
  // }
  ::v-deep .vxe-body--column{
  max-height: 100px !important;
}
::v-deep .vxe-cell{
  max-height: 100px !important;
}
</style>
