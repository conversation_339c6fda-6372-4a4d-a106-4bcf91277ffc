<template>
<div class="app-container">
    <xyy-panel title="查询条件">
        <!-- 按钮组 start-->
      <btn-group slot="tools" :btn-list="btnListTop"/>
      <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
        <el-row :gutter="20">
            <el-col :lg="6" :md="6">
                <el-form-item label="供货单号" prop="supplyCode">
                    <el-input v-model.trim="formData.supplyCode" placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :lg="6" :md="6">
                <el-form-item label="退货计划号" prop="refundCode">
                    <el-input v-model.trim="formData.refundCode" placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :lg="12" :md="12">
                <el-form-item label="退单创建时间" prop="SubmissionTime">
                    <el-date-picker v-model="formData.SubmissionTime" value-format="yyyy-MM-dd" type="daterange"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" />
                </el-form-item>
            </el-col>
            <!-- <el-col :lg="6" :md="6">
                <el-form-item label="商品信息">
                    <el-input v-model="formData.test2" placeholder="商品名称/本公司商品编码/ERP商品编码/csuid/标准库id"></el-input>
                </el-form-item>
            </el-col> -->
            <!-- <el-col :lg="6" :md="6">
                <el-form-item label="配送方式">
                    <el-select v-model="formData.deliveryMethod" placeholder="请选择">
                        <el-option
                            label="待确认"
                            :value="1">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col> -->
        </el-row>
        <el-row>
            <el-col :lg="6" :md="6">
                <el-form-item label="单据状态" prop="orderStatus">
                    <el-select v-model="formData.orderStatus" placeholder="请选择">
                        <el-option
                            v-for="item in orderStatusList"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
      </el-form>
    </xyy-panel>
    <xyy-panel title="退货计划单列表">
         <!-- 按钮组 start-->
        <btn-group slot="tools" :btn-list="btnListTabale" style="float:right"/>
        <div :key="tabKey" class="table-box">
            <vxe-table ref="xTable" :loading="loading" :row-style="rowStyle"  highlight-current-row height="auto"  :data="tableData" :key="tableKey" empty-text="暂未查询到匹配的记录"
                :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize,}">
                <vxe-table-column type="seq" title="序号" width="80" />
                <template >
                    <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field" :title="item.title" :width="item.width" :fixed="item.fixed">
                    <template v-slot:default="slotProps">
                        <div v-if="item.field==='operation1'">
                            <span @click.stop="detailhandler(slotProps.row)"  class="clickWriting">详情</span>
                        </div>
                        <div v-if="item.field==='supplyCode' || item.field==='businessOutCode'">
                            <div v-for="it in (slotProps.row[item.field] ? slotProps.row[item.field].split(',') : [])" :key="it">
                                <div>{{ it }}</div>
                            </div>
                        </div>
                        <div v-else-if="item.field==='operation2'">
                            <span class="clickWriting">详情</span>
                        </div>
                        <div v-else>
                            {{ slotProps.row[item.field] }}
                        </div>
                    </template>
                    </vxe-table-column>
                </template>
            </vxe-table>
        </div>
        <div class="pager">
          <vxe-pager
        border
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :page-sizes="tablePage.pageSizes"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total',
        ]"
        @page-change="handlePageChange"
      />
        </div>
  </xyy-panel>
</div>
</template>
<script>
import { exportFile } from '@/api/system/exportCenter'
import { planColumns} from "./config";
import { getRefundStatus,getRefundOrderList } from "@/api/order/returnGoods";
import XEUtils from 'xe-utils'
import { pageConfig } from '@/utils';
const end = new Date();
const start = XEUtils.getWhatDay(end, -6); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd '); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd'); // 将结束日期格式化为字符串
export default{
    name: "OrderReturnGoods",
    data(){
        return{
            btnListTop: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: this.searchList,
                    code: "",
                    plain: 'false',
                    permission:'returnGoods:list:query'

                },
                {
                    label: "重置",
                    clickEvent: this.reset,
                    code: "",
                    plain: 'false',
                    permission:'returnGoods:list:reset'
                },
            ],
            btnListTabale:[
            {
                    label: "新建退货计划单",
                    type: "primary",
                    clickEvent: this.addHandler,
                    code: "",
                    plain: 'false',
                    permission:'returnGoods:list:add'
                },
                {
                    label: "导出",
                    clickEvent: this.exprotHandler,
                    code: "",
                    plain: 'false',
                    permission:'returnGoods:list:export'

                }
            ],
            formData:{
                SubmissionTime: [defaultBeginTime, defaultEndTime], // 提交时间
                orderStatus:"",
                supplyCode:"",
                refundCode:''
            },
            tableColumns: planColumns(),
            tableData:[],
            orderStatusList:[],
            loading:false,
            tablePage: pageConfig(),
            tabKey:0,
            tableKey:0,
            // 时间限制
            pickerOptions: {
                disabledDate(time) {
                return time.getTime() > Date.now()
                }
            },
        }
    },
    mounted(){
        this.$nextTick(()=>{
            // utils.pageActivated()
            this.tabKey++
            this.tableKey++
        })
        this.getStateEnum()
        this.searchList()
    },
    activated(){
      this.getStateEnum()
      this.searchList()
    },

    methods:{
        rowStyle({ row, rowIndex }) {
            let businessOutCodeLength = row.businessOutCode ? row.businessOutCode.split(',').length : 0;
            let supplyCodeLength = row.supplyCode ? row.supplyCode.split(',').length : 0;
            let a = businessOutCodeLength > supplyCodeLength ? businessOutCodeLength : supplyCodeLength;
            if(a===1)return{height: a *55 + 'px !important'}
            return { height: a * 33 + 'px !important' };
        },
      // 获取状态枚举
      getStateEnum(){
        getRefundStatus().then(res=>{
            if(res.code === 0){
              this.orderStatusList = res.result
            }else{
              this.$message.error(res.msg)
            }
        })
      },
      //获取列表
        searchList(flag){
          this.loading = true
          if(flag !== true){
            this.tablePage.pageNum = 1
          }
          const params = {
            pageNum: this.tablePage.pageNum,
            pageSize: this.tablePage.pageSize,
            refundCode: this.formData.refundCode,
            supplyCode: this.formData.supplyCode,
            orderStatus: this.formData.orderStatus,
            startTime: this.formData.SubmissionTime ? this.formData.SubmissionTime[0] + ' 00:00:00' : '',
            endTime: this.formData.SubmissionTime ? this.formData.SubmissionTime[1] + ' 23:59:59' : '',
          }
          getRefundOrderList(params).then(res=>{
            if(res.code === 0){
                this.tableData = res.result.list || []
                this.tablePage.total = res.result.total || 0
            }else{
              this.$message.error(res.msg)
            }
          }).finally(()=>{
            this.loading = false;
            this.$nextTick(()=>{
                this.tableKey++
            })
          })


        },

        reset(){
          this.tablePage.pageSize= 20
          this.$refs.form.resetFields()
          this.searchList()

        },
        // 分页器改变处理
        handlePageChange({currentPage, pageSize}) {
          this.tablePage.pageNum = currentPage;
          this.tablePage.pageSize = pageSize;
            this.searchList(true)
        },
        // 详情
        detailhandler(row){
            this.$router.push({
                path: '/order/returnGoods/detail',
                query: { id: row.refundCode}
            })
        },
        // 新建退货计划单
        addHandler(){
            this.$router.push({
                path: '/order/OrderReturnAdd',
            })
        },

        //导出
        exprotHandler(){
          if (this.tableData.length === 0) {
            this.$message.warning('暂无数据可导出')
            return
          }
          const formInfo = {
            ...this.formData,
            pageNum: this.tablePage.pageNum,
            pageSize: this.tablePage.pageSize,
            startTime: this.formData.SubmissionTime ? `${this.formData.SubmissionTime[0]} 00:00:00 ` : '',
            endTime: this.formData.SubmissionTime ? `${this.formData.SubmissionTime[1]} 23:59:59 ` : '',
          }
          let params = {
              taskBean: 'SUPPLY_REFUNDORDERREMOTE_LISTREFUNDORDER',
              colNameDesc: this.tableColumns.filter(item => item.field != 'operation1').map(item => item.title).join(','),
              colName: this.tableColumns.filter(item => item.field != 'operation1').map(item => item.field).join(','),
              moduleName: 'SUPPLY_ORDER',
              menuDesc: '退货计划单',
              exportParams: JSON.stringify(formInfo)
          }
          this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        }
    }
}
</script>
<style scoped lang="scss">
.btn-status-wrap{
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    font-size: 12px;
}
.btn-status-wrap .btn-status{
    border-color: #ccc;
    background-color: #f5f5f5;
    color: #333;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: default;
}
.btn-status-wrap .btn-status-right{
    margin-left:10px;
}
.btn-status-wrap .status-activity{
    border:1px solid #67c23a;
    background-color: #ffffff;
}
.btn-status-wrap .btn-status:hover{
    border-color: #67c23a;
    color: #333;
}
::v-deep .vxe-body--column{
  max-height: 100px !important;
}
::v-deep .vxe-cell{
  max-height: 100px !important;
}
</style>
