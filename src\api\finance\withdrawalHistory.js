import request from "@/utils/request";

//查询提现记录
export function getWithdrawalHistoryList(params) {
  return request({
    url: "/finance/withdraw/list",
    method: "post",
    data: params,
  });
}

//查询提现汇总
export function getWithdrawalHistorySummary(params) {
  return request({
    url: "/finance/withdraw/amount-summary",
    method: "get",
    data: params,
  });
}

//查询提现记录明细
export function getWithdrawalHistoryDetail(params) {
  return request({
    url: "/finance/withdraw/detail/list",
    method: "post",
    data: params,
  });
}

//提现单按单号查询
export function getWithdrawalHistoryDetailByOrderNo(params) {
  return request({
    url: "/finance/withdraw/one",
    method: "get",
    params: params,
  });
}

//提现申请页发票查询
export function getWithdrawalHistoryInvoice(params) {
  return request({
    url: "/finance/withdraw/invoice/list",
    method: "post",
    data: params,
  });
}

//提现申请提交
export function submitWithdrawalHistory(params) {
  return request({
    url: "/finance/withdraw/submit-withdraw-apply",
    method: "post",
    data: params,
  });
}

//生成提现单号
export function getWithdrawalHistoryNo(params) {
  return request({
    url: "/finance/withdraw/withdraw-no",
    method: "get",
    params: params,
  });
}

//自动核销单详情
export function getAutoWriteOffDetail(params) {
  return request({
    url: "/finance/withdraw/detail/auto-write-off-list",
    method: "post",
    data: params,
  });
}
