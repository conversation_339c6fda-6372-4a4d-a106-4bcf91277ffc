import request from '@/utils/request'

// 控销组主页面查询(商品页面查询控销组也用这个)
export function queryControlledStore(data) {
    return request({
        url: '/merchantGroup/page',
        method: 'post',
        data: data
    })
}

// 商品页面-查询商品控销组
export function queryProductControlledStore(data) {
    return request({
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        url: '/product/selectMerchantGroupBySku',
        method: 'post',
        data: data
    })
}

// 商品页面-更新控销组
export function updateProductControlledStore(data) {
    return request({
        url: '/product/updateMerchantGroupBySku',
        method: 'post',
        data: data
    })
}

// 编辑页面-查询客户
export function queryCustomer(data) {
    return request({
        url: '/merchantGroup/allBuyers',
        method: 'post',
        data: data
    })
}

// 编辑页面-导入
export function importControlledStore(data) {
    return request({
        url: '/merchantGroup/importMerchants',
        method: 'post',
        data: data
    })
}

// 控销组-详情药店/编辑页面查询
export function queryMerchant(data) {
    return request({
        url: '/merchantGroup/merchantPage',
        method: 'post',
        data: data
    })
}

// 详情-商品页查询
export function queryProduct(data) {
    return request({
        url: '/merchantGroup/skuPage',
        method: 'post',
        data: data
    })
}

// 控销组主页面-查看日志
export function queryLog(data) {
    return request({
        url: '/merchantGroup/log',
        method: 'post',
        data: data
    })
}

// 控销主页面-添加控销组
export function addControlledStore(data) {
    return request({
        url: '/merchantGroup/addGroup',
        method: 'post',
        data: data
    })
}

// 控销组主页面-删除
export function deleteControlledStore(data) {
    return request({
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        url: '/merchantGroup/deleteByGroupId',
        method: 'post',
        data: data
    })
}

// 编辑页面-删除
export function deleteMerchant(data) {
    return request({
        url: '/merchantGroup/deleteByMerchantIds',
        method: 'post',
        data: data
    })
}

// 编辑页面-修改控销组名称
export function updateControlledStoreName(data) {
    return request({
        url: '/merchantGroup/updateGroupName',
        method: 'post',
        data: data
    })
}

// 编辑页面-添加店铺
export function addMerchant(data) {
    return request({
        url: '/merchantGroup/addGroupRelation',
        method: 'post',
        data: data
    })
}

// 下载模板
export function downloadTemplate() {
    return request({
        responseType: "blob",
        url: '/merchantGroup/template',
        method: 'get'
    })
}
