<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="closeOnClickModal"
    :title="title"
    :visible="dialogVisible"
    :width="width"
    :height="height"
    append-to-body
    @close="handleClose"
  >
    <slot />
    <span v-if="footerShow" slot="footer" class="dialog-footer">
      <slot name="footer" />
    </span>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog' // 拖拽指令

export default {
  name: 'XyyDialog',
  directives: { elDragDialog },
  props: {
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '648px'
    },
    height: {
      type: String,
      default: 'auto'
    },
    closeOnClickModal: {
      type: Boolean,
      default: false
    },
    footerShow:{
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  mounted() {},
  methods: {
    close() {
      this.dialogVisible = false
    },
    open() {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('on-close')
    }
  }
}
</script>

<style lang="scss">
</style>
