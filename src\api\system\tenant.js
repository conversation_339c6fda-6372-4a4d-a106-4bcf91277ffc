import request from '@/utils/request'

// 查询租户列表
export function listTenant(query) {
  return request({
    url: '/system/tenant/list',
    method: 'get',
    params: query
  })
}

// 查询租户详细
export function getTenant(id) {
  return request({
    url: '/system/tenant/' + id,
    method: 'get'
  })
}

// 新增租户
export function addTenant(data) {
  return request({
    url: '/system/tenant',
    method: 'post',
    data: data
  })
}

// 修改租户
export function updateTenant(data) {
  return request({
    url: '/system/tenant',
    method: 'put',
    data: data
  })
}

// 删除租户
export function delTenant(id) {
  return request({
    url: '/system/tenant/' + id,
    method: 'delete'
  })
}

//查询租户id
export function getTenantIdByName(name){
  return request({
    url: '/system/tenant/name/' + name,
    method: 'get'
  })
}

// 新增子账号
export function addChildAccount(data) {
  return request({
    url: '/system/tenant/openChildUser',
    method: 'post',
    data: data
  })
}

// 查询开通的账号
export function getChildAccountList(query) {
  return request({
    url: '/system/tenant/selectOpenChildUser',
    method: 'get',
    params: query
  })
}

// 商业模糊搜索
export function querySupplier(data) {
  return request({
    url: '/supplierOrganBase/querySupplier',
    method: 'post',
    data: data
  })
}

// 重置子账号密码
export function resetChildAccountPassword(data) {
  return request({
    url: '/system/user/init',
    method: 'post',
    data: data
  })
}
