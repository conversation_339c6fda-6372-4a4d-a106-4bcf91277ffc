<template>
  <div class="app-container">
    <xyy-panel title="基础信息">
      <!-- 按钮组 start-->
      <btn-group slot="tools" :btn-list="btnListTop" />
      <el-descriptions class="margin-top" title="" :column="3" :size="size" border>
        <el-descriptions-item label-class-name="el-descriptions-label" content-class-name="el-descriptions-content">
          <template slot="label"> 创建日期 </template>
          {{ formData.createTimeStr }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="el-descriptions-label" content-class-name="el-descriptions-content">
          <template slot="label"> 单据状态 </template>
          {{ formData.supplyStatusName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="el-descriptions-label" content-class-name="el-descriptions-content">
          <template slot="label"> 仓库联系方式 </template>
          {{ `${formData.wmsLinkman}/${formData.wmsPhone}` }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="el-descriptions-label" content-class-name="el-descriptions-content">
          <template slot="label"> 供货仓库地址 </template>
          {{ formData.wmsAddress }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="el-descriptions-label" content-class-name="el-descriptions-content">
          <template slot="label"> 创建人 </template>
          {{ formData.createUser }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="el-descriptions-label" content-class-name="el-descriptions-content">
          <template slot="label"> 下单人/联系电话 </template>
          {{ `${formData.buyerName}/${formData.buyerPhone}` }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="el-descriptions-label" content-class-name="el-descriptions-content">
          <template slot="label"> 供货商地址 </template>
          {{ formData.supplierAddress }}
        </el-descriptions-item>
      </el-descriptions>
      <vxe-table ref="xTableAmount" :loading="loading" highlight-current-row height="auto" :data="tableAmountData"
        style="margin-top:10px;width: 1000px;height: 95px;font-size:16px;">
        <vxe-table-column title="汇总" width="80" />
        <vxe-table-column v-for="item in detailAmountColumns" :key="item.title" :field="item.field" :title="item.title"
          :width="item.width" />
      </vxe-table>
    </xyy-panel>
    <xyy-panel title="商品列表">
      <div :key="tabKey" class="table-box">
        <vxe-table ref="xTable" :key="tableKey" :loading="loading"  highlight-current-row height="auto" :data="tableData">
          <vxe-table-column type="seq" title="序号" width="80" fixed="left" />
          <template v-for="item in columns">
            <vxe-table-column :key="item.field" :field="item.field" :title="item.title" :width="item.width" :fixed="item.fixed">
              <template #header>
                <div v-if="item.hit">
                  <el-tooltip placement="top">
                    <template slot="content">
                      <div v-for="(it,index) in item.hit" :key="index">
                        <div>{{it}}</div>
                      </div>
                    </template>
                    <div>
                      <span>{{item.title}}</span>
                      <span>
                        <svg-icon  icon-class="prompt-icon"/>
                      </span>
                    </div>
                  </el-tooltip>
                </div>
                
                <div v-else>
                  {{item.title}}
                </div>
              </template>
              <template v-slot:default="slotProps">
                <!-- 本公司商品编码 -->
                <div v-if="item.field === 'taxRate'">
                  {{slotProps.row[item.field]}}%
                </div>
                <div v-else-if="item.field === 'rejectPicCount'">
                    <div class="clickWriting" @click="imgLook(slotProps.row)">{{slotProps.row[item.field]}}</div>
                </div>
                <div v-else>
                  {{ slotProps.row[item.field] }}
                </div>
              </template>
            </vxe-table-column>
          </template>
        </vxe-table>
      </div>
    </xyy-panel>
    <el-image
      style="display: none;"
      :src="url"
      ref="imageRef"
      :preview-src-list="srcList">
    </el-image>
  </div>
</template>
<script>
import utils from '@/utils';
import { getSupplyGoodsDetail } from '@/api/order/supplyGoods'
import { detailColumns, detailAmountColumns } from "./config";
export default {
  name: "orderSupplierdetail",
  data() {
    return {
      btnListTop: [
        {
          label: "返回",
          clickEvent: this.backToPage,
          code: "",
          plain: 'false',
          // icon: 'search-icon',
          permission: 'supplier:detail:back'
        },
      ],
      size: "",
      formData: {
      },
      columns: detailColumns(),
      detailAmountColumns: detailAmountColumns(),
      tableAmountData: [],
      tableData: [],
      loading: false,
      tabKey: 0,
      supplyCode: this.$route.query.supplyCode,
      tableKey:0,
      srcList:[],//图片列表
      url:'',//查看图片
    }
  },
  mounted() {
    console.log(this.$route.query.id);
    this.$nextTick(() => {
      //utils.pageActivated()
      //this.tabKey++
    })
    this.apiGetSupplyGoodsDetail()
  },
  methods: {
    //获取供货单详情
    apiGetSupplyGoodsDetail() {
      getSupplyGoodsDetail({ supplyCode: this.supplyCode }).then(res => {
        const { msg, code, result } = res
        if (code === 0) {
          this.tableKey++
          this.formData = Object.assign({}, result)
          // this.formData.createUserName = this.$store.state.user.name
          delete this.formData.detailList
          this.tableAmountData = [
            {
              productTypeCount: result.productTypeCount,
              supplyCount: result.supplyCount,
              supplyAmount: result.supplyAmount,
              storageCount: result.storageCount,
              storageAmount: result.storageAmount,
              rejectCount: result.rejectCount,
              rejectAmount: result.rejectAmount,
            }
          ]
          this.tableData = result.detailList
        }else {
          this.$message({
            type: 'error',
            message: msg
          })
        }
      })
    },
    backToPage() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.go(-1)
    },
    /**查看图片 */
    imgLook(row){
      if(row.rejectPicCount==0){
        return
      }
      this.srcList = row.rejectPicList;
      this.url = this.srcList[0] || '';
      this.$nextTick(() => {
        this.$refs.imageRef.showViewer = true; // 触发预览
      });
    },
  }
}
</script>
<style scoped>
.clickWriting{
  color:#00B955 !important;
  cursor:pointer;
}
.clickWriting:hover{
  color:#00B955 !important;
  cursor:pointer;
}
</style>