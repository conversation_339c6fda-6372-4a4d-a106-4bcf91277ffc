import Layout from "@/layout";

export default {
  path: "/finance",
  component: Layout,
  alwaysShow: true,
  meta: { title: "财务管理", icon: "el-icon-money" },
  children: [
    {
      path: "/finance/invoices",
      component: () => import("@/views/finance/invoices"),
      name: "Invoices",
      meta: { title: "结算单" },
    },
    {
      path: "/finance/invoicesDetail",
      hidden: true,
      component: () => import("@/views/finance/invoices/detail"),
      name: "InvoicesDetail",
      meta: { title: "结算单详情" },
    },
    {
      path: "/finance/bill",
      component: () => import("@/views/finance/bill"),
      name: "Bill",
      meta: { title: "账单" },
    },
    {
      path: "/finance/InvoiceLedger",
      component: () => import("@/views/finance/InvoiceLedger"),
      name: "InvoiceLedger",
      meta: { title: "发票台账" },
    },
    {
      path: "/finance/invoiceDetails",
      hidden: true,
      component: () => import("@/views/finance/InvoiceLedger/components/invoiceDetails"),
      name: "InvoiceDetails",
      meta: { title: "发票明细" },
    },
    {
      path: "/finance/enterInvoice",
      hidden: true,
      component: () => import("@/views/finance/InvoiceLedger/components/enterInvoice"),
      name: "EnterInvoice",
      meta: { title: "发票录入" },
    },
    {
      path: "/finance/withdrawalHistory",
      component: () => import("@/views/finance/withdrawalHistory"),
      name: "WithdrawalHistory",
      meta: { title: "提现记录" },
    },
    {
      path: "/finance/withdrawalDetails",
      hidden: true,
      component: () => import("@/views/finance/withdrawalHistory/components/withdrawalDetails"),
      name: "WithdrawalDetails",
      meta: { title: "提现单明细" },
    },
    {
      path: "/finance/requestWithdrawal",
      hidden: true,
      component: () => import("@/views/finance/withdrawalHistory/components/requestWithdrawal"),
      name: "RequestWithdrawal",
      meta: { title: "申请提现" },
    },
    {
      path: "/finance/refundOrderDetail",
      hidden: true,
      component: () =>
        import("@/views/finance/InvoiceLedger/components/refundOrderDetail"),
      name: "RefundOrderDetail",
      meta: { title: "退补单详情" },
    },
    {
      path: "/finance/autoWriteOffDetail",
      hidden: true,
      component: () =>
        import(
          "@/views/finance/withdrawalHistory/components/autoWriteOffDetail"
        ),
      name: "AutoWriteOffDetail",
      meta: { title: "自动核销单详情" },
    },
    {
      path: "/finance/paymentVerify",
      component: () => import("@/views/finance/paymentVerify"),
      name: "PaymentVerify",
      meta: { title: "商业付款核销记录" },
    }
  ],
};
