<template>
    <div class="app-container">
        <xyy-panel :titleShow="false">
            <div slot="tools">
                <div>
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; ">
                        <div id="total">
                            <div class="total_content">
                                <div id="total_taxt">待录入发票金额合计
                                    <el-tooltip content="状态为待录入发票的发票金额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${toEnteredInvoiceAmount}` }}</div>
                            </div>
                            <div class="total_content">
                                <div id="total_taxt">发票待核销总金额
                                    <el-tooltip content="统计已录入状态下发票可以核销的总金额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${verificationAmount}` }}</div>
                            </div>
                        </div>
                        <div>
                            <el-button type="primary" @click="exportTable"
                                v-hasPermi="['finance:InvoiceLedger:export']">导出发票明细</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </xyy-panel>
        <xyy-panel title="查询条件">
            <div slot="tools" style="float: right;">
                <el-button type="primary" @click="searchList('click')"
                    v-hasPermi="['finance:InvoiceLedger:query']">查询</el-button>
            </div>
            <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="业务单号">
                            <el-input v-model="formData.sourceNo" placeholder="请输入业务单号" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="供货单单号">
                            <el-input v-model="formData.orderNo" placeholder="请输入供货单单号" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="12" :md="12">
                        <el-form-item label="单据日期">
                            <el-date-picker v-model="formData.SubmissionTime" value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                :picker-options="pickerOptions" :default-time="['00:00:00', '23:59:59']" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="单据类型">
                            <el-select v-model="formData.sourceType" placeholder="请选择单据类型" clearable>
                                <el-option v-for="item in invoiceTypeList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="发票状态">
                            <el-select v-model="formData.invoiceStatus" placeholder="请选择发票状态" clearable>
                                <el-option v-for="item in invoiceStatusList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="12" :md="12">
                        <el-form-item label="审核完成时间">
                            <el-date-picker v-model="formData.auditTimes" value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                :picker-options="pickerOptions" :default-time="['00:00:00', '23:59:59']" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="发票号">
                            <el-input v-model="formData.taxNo" placeholder="请输入发票号" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel title="发票台账列表">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTable" />
            <div class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" empty-text="暂无发票台账数据" @checkbox-all="selectAllEvent"
                    :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }"
                    @checkbox-change="selectChangeEvent" @cell-click="cellClickEvent"
                    :checkbox-config="{ highlight: true }">
                    <vxe-table-column type="checkbox" width="50" />
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'operation'">
                                    <span @click="detailhandler(slotProps.row)"
                                        v-if="slotProps.row.invoiceStatus == 1 || slotProps.row.invoiceStatus == 2"
                                        class="clickWriting">查看明细</span>
                                </div>
                                <div v-else-if="item.field == 'orderNo'">
                                <!-- {{ slotProps.row.sourceType === 3 || 4 ? '' : slotProps.row[item.field] }} -->
                                  <!-- {{ slotProps.row[item.field] }} -->
                                    {{ ((slotProps.row.sourceType === 3 || slotProps.row.sourceType === 4))? '' : slotProps.row[item.field] }}
                              </div>
                                <div v-else-if="item.field == 'invoiceStatusName'">
                                    <el-tag type="info" v-if="slotProps.row.invoiceStatus == -1"
                                        style="font-size: 14px;">{{
                                            slotProps.row.invoiceStatusName }}</el-tag>
                                    <el-tag type="warning" v-else-if="slotProps.row.invoiceStatus == 0"
                                        style="font-size: 14px;">{{ slotProps.row.invoiceStatusName }}</el-tag>
                                    <el-tag type="primary" v-else-if="slotProps.row.invoiceStatus == 1"
                                        style="font-size: 14px;">{{ slotProps.row.invoiceStatusName }}</el-tag>
                                    <el-tag type="success" v-else-if="slotProps.row.invoiceStatus == 2"
                                        style="font-size: 14px;">{{ slotProps.row.invoiceStatusName }}</el-tag>
                                </div>
                                <div v-else-if="item.field == 'sourceNo'">
                                    <span @click="sourceNoDetail(slotProps.row)" class="clickWriting">{{
                                        slotProps.row[item.field]
                                        }}</span>
                                </div>
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
        <enter-real-invoice ref="enterRealInvoice" />
    </div>

</template>

<script>
import { getInvoiceList, getTotalSaleAmount } from "@/api/finance/InvoiceLedger";
import enterRealInvoice from "./components/enterRealInvoice.vue";
import { pageConfig } from "@/utils";
import { tableColumns } from './config'
import XEUtils from 'xe-utils'
import { exportFile } from '@/api/system/exportCenter';
const end = new Date();
const start = XEUtils.getWhatDay(end, -30); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd 00:00:00'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd 23:59:59'); // 将结束日期格式化为字符串
export default {
    name: 'InvoiceLedger',
    components: { enterRealInvoice },
    data() {
        return {
            toEnteredInvoiceAmount: 0,  //待录入发票金额合计
            verificationAmount: 0,  //发票待核销总金额
            btnListTable: [ //表格按钮组
                {
                    label: "录入实体发票",
                    clickEvent: this.enterRealInvoice,
                    type: 'primary',
                    plain: 'false',
                    permission: 'finance:InvoiceLedger:enterRealInvoice'
                },
                {
                    label: "去录入发票",
                    clickEvent: this.enterInvoice,
                    type: 'primary',
                    plain: 'false',
                    permission: 'finance:InvoiceLedger:enterInvoice'
                },
            ],
            formData: { //表单数据
                orderNo: '', //供货单号
                sourceNo: '',    //业务单号
                SubmissionTime: [defaultBeginTime, defaultEndTime], //时间
                sourceType: '',    //单据类型
                invoiceStatus: '',    //发票状态
                taxNo: '',    //发票号
                auditTimes: null //审核通过时间
            },
            invoiceTypeList: [  //单据类型
                {
                    value: '',
                    label: '全部'
                },
                {
                    value: 1,
                    label: '采购入库'
                },
                {
                    value: 2,
                    label: '采购入库退货'
                },
                {
                    value: 3,
                    label: '退补价'
                },
            ],
            invoiceStatusList: [    //发票状态
                {
                    value: '',
                    label: '全部'
                },
                {
                    value: -1,
                    label: '录入失败'
                },
                {
                    value: 0,
                    label: '待录入'
                },
                {
                    value: 1,
                    label: '核对中'
                },
                {
                    value: 2,
                    label: '已录入'
                },
                // {
                //     value: 5,
                //     label: '部分开票'
                // },
            ],
            // 时间限制
            pickerOptions: {
                // disabledDate 用于禁用超过明天零点的日期时间
                disabledDate(time) {
                    // 计算明天的起始时间（第二天 00:00:00）
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(0, 0, 0, 0);
                    // 如果选择的时间大于等于明天 00:00:00，则禁用
                    return time.getTime() >= tomorrow.getTime();
                }
            },
            loading: false, //表格加载动画
            tableData: [],  //表格数据
            tableKey: 0,    //表格刷新
            tableColumns: tableColumns(),  //表格列
            tablePage: pageConfig(),    //表格分页
            checkbox_container: new Map(), // 选中行
        }
    },
    mounted() {

    },
    activated() {
        this.$nextTick(() => {
            this.searchList()
        })
    },
    methods: {
        // 导出表格
        exportTable() {
            if (this.tableData.length == 0) {
                this.$message.warning('暂无数据可导出')
                return
            }
            const formInfo = {
                ...this.formData,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                orderTimeStart: this.formData.SubmissionTime ? this.formData.SubmissionTime[0] : '',
                orderTimeEnd: this.formData.SubmissionTime ? this.formData.SubmissionTime[1] : '',
                auditTimeStart: this.formData.auditTimes ? this.formData.auditTimes[0] : '',
                auditTimeEnd: this.formData.auditTimes ? this.formData.auditTimes[1] : '',
            }
            delete formInfo.SubmissionTime
            delete formInfo.auditTimes;
            const colNameDesc = this.tableColumns.filter(item => item.field != 'operation').map(item => item.title).join(',');
            const colName = this.tableColumns.filter(item => item.field != 'operation').map(item => item.field).join(',');
            const params = {
                taskBean: 'FinanceInvoice_query',
                moduleName: 'FINANCE',
                colNameDesc,
                colName,
                exportParams: JSON.stringify(formInfo),
                menuDesc: '发票台账'
            }
            this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        },
        // 保存checkbox的选中状态
        saveCheckbox() {
            // 获取所有通过checkbox选中的表格行数据
            const rows = this.$refs.xTable.getCheckboxRecords()

            // 遍历每一行选中的数据，并将其存放到checkbox_container中
            // 这样可以保持选中状态的数据，供后续的数据操作使用
            rows?.forEach((v) => {
                this.checkbox_container.set(v.id, v)
            })
            this.checkbox_container = new Map(this.checkbox_container)
        },
        // 获取总额
        apiGetTotalSaleAmount() {
            getTotalSaleAmount().then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.toEnteredInvoiceAmount = result.waitEnterAmount || 0
                    this.verificationAmount = result.waitWriteOffAmount || 0
                } else {
                    this.$message.error(msg)
                }
            })
        },
        // 获取列表
        getList() {
            // 等待DOM更新完成后，保存当前checkbox的选中状态
            this.$nextTick(() => {
                this.saveCheckbox()
            })
            this.loading = true
            const params = {
                ...this.formData,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                orderTimeStart: this.formData.SubmissionTime ? this.formData.SubmissionTime[0] : '',
                orderTimeEnd: this.formData.SubmissionTime ? this.formData.SubmissionTime[1] : '',
                auditTimeStart: this.formData.auditTimes ? this.formData.auditTimes[0] : '',
                auditTimeEnd: this.formData.auditTimes ? this.formData.auditTimes[1] : '',
            }
            delete params.SubmissionTime
            delete params.auditTimes;
            getInvoiceList(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.tableData = result.list
                    this.tablePage.total = result.total
                    this.loading = false
                    this.tableKey++
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            }).finally(() => {
                // 在API请求完成后，无论成功或失败都执行以下操作

                // 根据当前表格数据，找出之前已在checkbox_container中保存的行
                const checkedRows = this.tableData.map(item => {
                    if (this.checkbox_container.has(item.id)) {
                        return item
                    }
                })
                // 重新设置这些行的checkbox为选中状态
                this.$refs.xTable.setCheckboxRow(checkedRows, true)
            })
            this.apiGetTotalSaleAmount()
        },
        // 搜索
        searchList(type) {
            if (type == 'click') {
                this.$nextTick(() => {
                    this.$refs.xTable.setAllCheckboxRow(false)
                    this.checkbox_container.clear() // 清空复选框
                })
            }
            this.tablePage.pageNum = 1
            this.getList()
        },
        // 录入实体发票
        enterRealInvoice() {
            this.$refs.enterRealInvoice.open()
        },
        // 发票录入
        enterInvoice() {
            let banStatusArr = []   // 禁止录入状态
            this.saveCheckbox()
            const checkedArr = Array.from(this.checkbox_container.values())
            if (checkedArr.length === 0) {
                this.$message.warning('请选择需要录入的订单')
                return
            }
            checkedArr.forEach(item => {
                if (item.invoiceStatus != -1 && item.invoiceStatus != 0) {
                    banStatusArr.push(item.invoiceStatus)
                }
                item.auditAmount = item.waitEnterAmount
            })
            if (banStatusArr.length > 0) {
                this.$message.warning('存在发票状态为核对中或已录入的单据，请重新勾选')
                return
            }
            localStorage.setItem('enterInvoice', JSON.stringify(checkedArr))
            this.$router.push({
                path: '/finance/enterInvoice'
            })
            this.$nextTick(() => {
                this.$refs.xTable.setAllCheckboxRow(false)
                this.checkbox_container.clear() // 清空复选框
            })
        },
        // 全选
        selectAllEvent({ checked }) {
            if (!checked) {
                this.tableData.forEach(item => {
                    if (this.checkbox_container.has(item.id)) {
                        this.checkbox_container.delete(item.id)
                    }
                })
            }
        },
        // 选择行
        selectChangeEvent({ row, checked }) {
            this.$refs.xTable.toggleCheckboxRow(row)
            // 如果当前行未被选中，则从容器中删除该行数据，避免残留已选状态的数据
            if (!checked) {
                if (this.checkbox_container.has(row.id)) {
                    this.checkbox_container.delete(row.id)
                }
            }
        },
        //查看详情
        sourceNoDetail(row) {
            if (row.sourceType == 3 || row.sourceType == 4) {
                this.$router.push({
                    path: '/finance/refundOrderDetail',
                    query: { refundAdjustmentCode: row.orderNo }
                })
            } else if (row.sourceType == 2) {
                this.$router.push({
                    path: '/order/returnGoods/detail',
                    query: { id: row.sourceNo }
                })
            } else if (row.sourceType == 1) {
                this.$router.push({
                    path: '/order/OrderStorageDetail',
                    query: { storageCode: row.sourceNo }
                })
            }
        },
        //点击表格行
        cellClickEvent({ row }) {
            this.$refs.xTable.toggleCheckboxRow(row)
            // 如果当前行经过切换后状态为未选中，则从容器中删除该行数据
            if (!this.$refs.xTable.isCheckedByCheckboxRow(row)) {
                if (this.checkbox_container.has(row.id)) {
                    this.checkbox_container.delete(row.id)
                }
            }
        },
        // 详情
        detailhandler(row) {
            this.$router.push({
                path: '/finance/invoiceDetails',
                query: {
                    invoiceNo: row.invoiceNo,
                    invoiceStatus: row.invoiceStatus,
                }
            })
        },
        // 分页
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList();
        },
    }
}
</script>

<style lang="scss" scoped>
#total {
    display: flex;
    flex-direction: row;
    justify-content: start;

    .total_content {
        align-items: center;
        margin: 0 23px 0px 23px;
        display: flex;
        flex-direction: column;

        #total_aomunt {
            font-size: 23px;
            font-weight: bolder;
        }
    }
}
</style>