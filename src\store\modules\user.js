import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    userinfo: {
      orgInfo:{
        orgCode:"",
        merchantId:"",
        warehouseName:""
      },
      tenant:{
        name:""
      }
    },
    roles: [],
    permissions: [],
    warehouses: [],
  },

  mutations: {
    SET_WAREHOUSES: (state, warehouses) => {
      state.warehouses = warehouses
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_USER: (state, userinfo) => {
      state.userinfo = userinfo
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_ORGCODE: (state, orgCode) => {
      state.orgCode = orgCode
    },
    SET_TENANTID: (state, tenantId) => {
      state.tenantId = tenantId
    }
  },

  actions: {
    setOrgInfo({commit}, warehouses){
      commit('SET_WAREHOUSES', warehouses)
    },
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user
          const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.png") : process.env.VUE_APP_BASE_API + user.avatar;
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_ORGCODE',res.orgCode)
          commit('SET_TENANTID',res.user.tenantId)
          commit('SET_NAME', user.userName)
          // 确保 user 包含 tenant 属性，并且 tenant 是一个对象
          const userinfo = {
            orgInfo:{
              orgCode: res.orgInfo.orgCode || "",
              merchantId: res.orgInfo.merchantId || "",
              warehouseName:res.orgInfo.warehouseName || ""
            },
            tenant: {
              name: res.tenant?.name || ""  // 使用可选链操作符
            }
          };
          commit('SET_USER', userinfo)
          commit('SET_AVATAR', avatar)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
