export function shelvedProductsCol(){
    return [
        { field: "erpProductCode", title: "本公司商品编码", width: 150,
            slots: { default: "erpProductCode" },
        },
        { field: "csuid", title: "商品编码", width: 140,},
        { field: "productName", title: "商品名称/规格/生产厂家", width: 240,
            slots: { default: "productName" },
        },
        { field: "dosageForm", title: "商品剂型", width: 100,},
        { field: "splitName", title: "拆零包装/单位", width: 100,hit:['可拆零：按单个售卖','中包装：按中包装X个起售卖,不可拆零','大包装：按大包装X个起售卖,不可拆零'],
            slots: { default: "splitName" },
        },
        { field: "suggestPrice", title: "销售价(元)", width: 150,
            slots: { default: "suggestPrice" },
        },
        { field: "stock", title: "可售库存", width: 80
        },
        { field: "warehouseRank", title: "销售价排名", width: 130,
            slots: { default: "warehouseRank" },
        },
        { field: "latestSupplyPrice", title: "参考价格(元)", width: 130,
            slots: { default: "latestSupplyPrice" },
        },
        { field: "createUser3", title: "环线价(元)", width: 100,
            slots: { default: "createUser3" },
        },
        { field: "activeDesc", title: "活动描述", width: 150,
          slots: { default: "activeDesc" },
      },
        { field: "uniform", title: "控销商圈", width: 100,
            slots: { default: "uniform" },
        },
        { field: "imageUrl", title: "图片", width: 70,
            slots: { default: "imageUrl" },
        },
        {
          field: "options",
          title: "操作",
          width: 120,
          fixed: "right",
          slots: { default: "options" },
        },
      ];
}
export function pendingShelvingCol(){
    return [
        { field: "containerCode", title: "本公司商品编码", width: 200,
            slots: { default: "containerCode" },
        },
        { field: "containerTypeName", title: "商品编码", width: 210,},
        { field: "lockedName", title: "商品名称/规格/生产厂家", width: 210,
            slots: { default: "lockedName" },
        },
        { field: "createTime", title: "拆零包装/单位", width: 210,
            slots: { default: "createTime" },
        },
        { field: "createUser", title: "销售价", width: 210,
            slots: { default: "createUser" },
        },
        { field: "createUser1", title: "销售价排名", width: 160,
            slots: { default: "createUser1" },
        },
        { field: "createUser2", title: "参考价格", width: 160,
            slots: { default: "createUser2" },
        },
        { field: "createUser3", title: "环线价", width: 100,
            slots: { default: "createUser3" },
        },
        { field: "createUser4", title: "控销", width: 210,
            slots: { default: "createUser4" },
        },
        { field: "createUser5", title: "可售库存", width: 120,
            slots: { default: "createUser5" },
        },
        { field: "createUser6", title: "图片", width: 50,
            slots: { default: "createUser6" },
        },
        {
          field: "options",
          title: "操作",
          width: 150,
          fixed: "right",
          slots: { default: "options" },
        },
      ];
}
export function toBePublishedCol(){
    return [
        { field: "containerCode", title: "本公司商品编码", width: 200,
            slots: { default: "containerCode" },
        },
        { field: "containerTypeName", title: "商品编码", width: 210,},
        { field: "lockedName", title: "商品名称/规格/生产厂家", width: 210,
            slots: { default: "lockedName" },
        },
        { field: "createTime", title: "拆零包装/单位", width: 210,
            slots: { default: "createTime" },
        },
        { field: "createUser", title: "销售价", width: 210,
            slots: { default: "createUser" },
        },
        { field: "createUser1", title: "销售价排名", width: 160,
            slots: { default: "createUser1" },
        },
        { field: "createUser2", title: "参考价格", width: 160,
            slots: { default: "createUser2" },
        },
        { field: "createUser3", title: "环线价", width: 100,
            slots: { default: "createUser3" },
        },
        { field: "createUser4", title: "控销", width: 210,
            slots: { default: "createUser4" },
        },
        { field: "createUser5", title: "可售库存", width: 120,
            slots: { default: "createUser5" },
        },
        { field: "createUser6", title: "图片", width: 50,
            slots: { default: "createUser6" },
        },
        {
          field: "options",
          title: "操作",
          width: 150,
          fixed: "right",
          slots: { default: "options" },
        },
      ];
}

export function partRuleTableColumn() {
    return [
        { field: "productCode", title: "商品编码", width: 120,},
        { field: "productName", title: "商品通用名", width: 120,},
        { field: "spec", title: "商品规格", width: 120,},
        { field: "dosageForm", title: "商品剂型", width: 120,},
        { field: "level1", title: "一环加点", width: 120,},
        { field: "level2", title: "二环加点", width: 120,},
        { field: "level3", title: "三环加点", width: 120,},
        { field: "level4", title: "四环加点", width: 120,},
    ]
}

export function chooseCustomerCol() {
    return [
        { field: "id", title: "控销组ID", width: 120,},
        { field: "name", title: "控销组名称", width: 120,},
        { field: "createTimeStr", title: "创建时间", width: 120,},
        { field: "updateTimeStr", title: "更新时间", width: 120,},
    ]
}
