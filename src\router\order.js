import Layout from '@/layout';

export default [
    {
      path: '/order',
      component: Layout,
      alwaysShow: true,
      meta: { title: '订单管理', activeMenu: '/order/supplier',icon: "clipboard"},
      children: [
        {
          path: 'OrderSupplier',
          component: () => import('@/views/order/supplier'),
          name: 'OrderSupplier',
          meta: { title: '供货单管理', activeMenu: '/order/supplier',icon: "clipboard"}
        },
        {
            path: '/order/supplier/detail',
            hidden: true,
            component: () => import('@/views/order/supplier/detail'),
            name: 'orderSupplierdetail',
            meta: { title: '供货单详情'}
        },
        {
            path: '/order/supplier/add',
            hidden: true,
            component: () => import('@/views/order/supplier/add'),
            name: 'OrderSupplierAdd',
            meta: { title: '新建供货单', activeMenu: '/order/supplier/add',}
        },
        {
            path: '/order/supplier/confirm',
            hidden: true,
            component: () => import('@/views/order/supplier/confirm'),
            name: 'OrderSupplierConfirm',
            meta: { title: '供货订单确认发货', activeMenu: '/order/supplier/confirm' }
        },
        {
            path: '/order/returnGoods',
            component: () => import('@/views/order/returnGoods'),
            name: 'OrderReturnGoods',
            meta: { title: '退货单管理', activeMenu: '/order/returnGoods',icon: "clipboard"}
        },
        {
            path: '/order/returnGoods/add',
            component: () => import('@/views/order/returnGoods/add'),
            name: 'OrderReturnAdd',
            hidden: true,
            meta: { title: '新建退货单'}
        },
        {
            path: '/order/returnGoods/detail',
            component: () => import('@/views/order/returnGoods/detail'),
            name: 'OrderReturnDetail',
            hidden: true,
            meta: { title: '退货单详情'}
        },
        {
          path: '/order/storageOrderDetail',
          component: () => import('@/views/order/storageOrderDetail'),
          name: 'OrderStorageDetail',
          meta: { title: '供货入库单明细', activeMenu: '/order/storageOrderDetail',icon: "clipboard"}
      },
      ],
    }
]
