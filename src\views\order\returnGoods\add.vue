<template>
  <div class="app-container">
      <xyy-panel title="查询条件">
          <!-- 按钮组 start-->
        <btn-group slot="tools" :btn-list="btnListTop"/>
        <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
          <el-row :gutter="20">
              <el-col :lg="6" :md="6">
                  <el-form-item label="供货仓" >
                      <el-input v-model="formData.orgName" disabled></el-input>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="退货单号">
                      <el-input v-model="formData.refundCode" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="创建日期">
                      <el-input v-model="formData.createTime" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="创建人">
                      <el-input v-model="formData.creator" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="单据状态">
                      <el-input v-model="formData.status" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="仓库联系方式">
                      <el-input v-model="formData.linkManAndPhone" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="12" :md="12">
                  <el-form-item label="供货仓库地址">
                      <el-input v-model="formData.orgAddress" disabled/>
                  </el-form-item>
              </el-col>

              <el-col :lg="6" :md="6">
                  <el-form-item label="供货商家">
                      <el-input v-model="formData.supplyer" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="下单人/联系电话">
                      <el-input v-model="formData.contactNameAndMobile" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="12" :md="12">
                  <el-form-item label="供货商地址">
                      <el-input v-model="formData.supplierAddress" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="选择配送方式" :required="true">
                    <el-select v-model="formData.deliveryMethod" placeholder="请选择">
                        <el-option v-for="item in deliveryTypeList" :key="item.code" :label="item.name" :value="item.code"/>
                    </el-select>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6" v-if="isSelfDelivery">
                <el-form-item label="自提时间" :required="true">
                  <el-date-picker
                    v-model="formData.deliveryDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>

              </el-col>
          </el-row>
        </el-form>
      </xyy-panel>
      <xyy-panel title="退货商品列表" red-text="多个品种一起下退单，更节省费用">
      <!-- 按钮组 start-->
              <btn-group slot="tools" :btn-list="btnListTabale" style="float:right !important"/>
          <div :key="tabKey" class="table-box">
              <vxe-table ref="xTable" :loading="loading" highlight-current-row height="400" :data="tableData"
                  :key="tableKey"
                   empty-text="未选择退货商品"
                  @checkbox-all="selectAllEvent"
                  @cell-click="cellClickEvent"
                  @checkbox-change="selectChangeEvent"
                  :checkbox-config="{ highlight: true }"
                  >
                  <vxe-table-column type="checkbox" width="80" />
                  <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field" :title="item.title" :width="item.width" >
                    <template #header>
                      <div v-if="item.hit">
                        <el-tooltip placement="top">
                          <template slot="content">
                            <div v-for="(it,index) in item.hit" :key="index">
                              <div>{{it}}</div>
                            </div>
                          </template>
                          <div>
                            <span>{{item.title}}</span>
                            <span>
                              <svg-icon  icon-class="prompt-icon"/>
                            </span>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        {{item.title}}
                      </div>
                    </template>
                      <template v-slot="slotProps">
                          <div v-if="item.field === 'erpProductCode'">
                              <el-input v-model="slotProps.row.erpProductCode" disabled>
                                  <el-button slot="append" icon="el-icon-search" @click.stop="openProductSearch(slotProps.rowIndex)"></el-button>
                              </el-input>
                          </div>
                          <div v-else-if="item.field === 'productCode'">
                              <el-input v-model="slotProps.row.productCode" disabled>
                                  <el-button slot="append" icon="el-icon-search" @click.stop="openProductSearch(slotProps.rowIndex)"></el-button>
                              </el-input>
                          </div>
                          <div v-else-if="item.field === 'csuid'">
                              <el-input v-model="slotProps.row.csuid" disabled>
                                  <el-button slot="append" icon="el-icon-search" @click.stop="openProductSearch(slotProps.rowIndex)"></el-button>
                              </el-input>
                          </div>
                          <div v-else-if="item.field === 'refundCount'">
                              <el-input v-model="slotProps.row[item.field]" @input="handelInput($event,slotProps.row)"></el-input>
                          </div>
                          <div v-else>
                              <span>{{slotProps.row[item.field] }}</span>
                          </div>
                      </template>
                  </vxe-table-column>
              </vxe-table>
          </div>
          <div id="total">
                      <div id="total_content">
                        退货数量：
                        <span>{{ returnSum }}</span>
                      </div>
                      <div id="total_content">
                        退货单金额：
                        <span>{{ returnMoney}}</span>
                      </div>
                      <div id="total_content">

                      </div>
                  </div>
    </xyy-panel>
    <product-search ref="productSearch" @echoData="echoData" ></product-search>
  </div>
  </template>
  <script>
  import productSearch from "./components/productSearch.vue";
  import utils from "@/utils";
  import {getCooperationOrg, getBusiness, getRefundCode, getDeliveryType, saveRefundOrder} from "@/api/order/returnGoods";
  import { returnColumns } from "./config";
  import XEUtils from 'xe-utils'
  const date = new Date()
  const createTime= XEUtils.toDateString(date, 'yyyy-MM-dd HH:mm:ss'); // 将结束日期格式化为字符串
  export default{
      name: "OrderReturnAdd",
      components: {
          productSearch,
      },
      data(){
          return{
              storeList:[],
              deliveryTypeList:[],
              btnListTop: [
                  {
                      label: "提交订单",
                      type: "primary",
                      clickEvent: this.submitOrder,
                      code: "",
                      plain: 'false',
                      permission:'returnGoods:add:submit'

                  },
                  {
                      label: "返回",
                      clickEvent: this.backToPage,
                      code: "",
                      plain: 'false',
                      permission:'returnGoods:add:back'
                  },
              ],
              btnListTabale:[
              {
                      label: "新增",
                      type: "primary",
                      clickEvent: this.addProduct,
                      code: "",
                      plain: 'false',
                      permission:'returnGoods:add:add'
                  },
                  {
                      label: "删除",
                      type: "danger",
                      clickEvent: this.delProduct,
                      code: "",
                      plain: 'false',
                      permission:'returnGoods:add:delete'
                  },
              ],
              formData:{
                  createTime:createTime, //创建日期
                  creator:'', // 创建人
                  status:'待申请',
                  supplyer:'', // 供货商家
                  contactNameAndMobile:'', // 联系人/联系方式
                  supplierAddress:'', // 供货商地址
                  store:'', //供货仓
                  linkManAndPhone:'', // 联系人/联系方式,
                  orgAddress:'', // 机构地址
                  orgCode:'', // 合作机构编码
                  orgName:'', // 合作机构名称
                  refundCode:'', // 退货单号
                  deliveryMethod:'', // 配送方式
                  deliveryDate:null //自提时间
              },
              tableColumns: returnColumns(),
              tableData:[{}],
              initData:{},
              loading:false,
              tabKey:0,
              tableKey:0,
              returnSum:0,
              returnMoney:0,
              currentRowIndex:null
          }
      },
      mounted(){
          this.$nextTick(()=>{
              utils.pageActivated()
              this.tabKey++
              this.tableKey++
          })
          this.getInitData()

      },
      watch:{
        tableData:{
          handler(){
            this.computedCount()
          },
          deep:true,
          immediate:true
        }
      },
      computed:{
        isSelfDelivery:function(){
          const selfDelivery = this.deliveryTypeList.find(item => item.name === '自配送');
          return selfDelivery ? this.formData.deliveryMethod === selfDelivery.code : false;
        },
      },
      methods:{
        // 获取初始信息
        getInitData(){
          //获取供货仓信息
          getCooperationOrg().then(res=>{
                if(res.code === 0){
                    this.storeList = res.result
                    this.formData.orgCode = this.$store.getters.orgCode
                    const storeItem = this.storeList.find(item => item.orgCode === this.formData.orgCode);
                    if (storeItem) {
                      this.formData.orgName = storeItem.orgName;
                      this.formData.linkManAndPhone = `${storeItem.linkMan} / ${storeItem.linkPhone}`;
                      this.formData.orgAddress = storeItem.orgAddress;
                    }
                }else{
                    this.$message.error(res.msg)
                }
            })
            //获取供应商信息
            getBusiness().then(res=>{
                if(res.code === 0){
                    this.formData.supplyer = res.result.name
                    this.formData.contactNameAndMobile = res.result.contactName + '/' + res.result.contactMobile
                    this.formData.supplierAddress = res.result.supplierAddress
                }else{
                    this.$message.error(res.msg)
                }
            })
            // 获取退货单号
            getRefundCode().then(res=>{
                if(res.code === 0){
                    this.formData.refundCode = res.result
                }else{
                    this.$message.error(res.msg)
                }
            })
            // 获取退货单配送方式
            getDeliveryType().then(res=>{
                if(res.code === 0){
                    this.deliveryTypeList = res.result
                    const selfDelivery = this.deliveryTypeList.find(item => item.name === '自配送');
                    this.formData.deliveryMethod = selfDelivery.code
                }else{
                    this.$message.error(res.msg)
                }
            })
            this.formData.creator = this.$store.getters.name
        },
        //校验退货数量
        handelInput(value, row) {
           // 只保留数字，去除非数字字符
           let newValue = value.replace(/[^\d]/g, '');
            // 去除前导零
            newValue = newValue.replace(/^0+/, '');
            // 更新输入值
            // row.supplyCount = newValue;
            this.$set(row, 'refundCount', newValue)
        },
        mergeMethod({ rowIndex, columnIndex }) {
            if (columnIndex === 2) {
                return { rowspan: 1, colspan: 3 };
            } else if (columnIndex > 2 && columnIndex <= 4) {
                return { rowspan: 0, colspan: 0 };
            }
        },
        //计算品种数退货数量退货金额
        computedCount(){
            // 只统计有productCode的商品数量
            this.returnSum = this.tableData.reduce((total, item) => {
              let num = item.refundCount ? Number(item.refundCount) : 0
                return total + num
            }, 0)
            this.tableData.forEach(item=>{
              item.refundAmount = Number((item.refundCount ? item.refundCount : 0) * (item.supplyPrice ? item.supplyPrice : 0)).toFixed(2)
            })
            this.returnMoney = this.tableData.reduce((total, item) => {
                let num = item.refundAmount? Number(item.refundAmount) : 0
                return total + num
            }, 0).toFixed(2)
            // this.returnMoney = this.tableData.reduce((total, item) => {
            //   let num = item.refundCount ? Number(item.refundCount) : 0
            //     return total + num * item.supplyPrice ? item.supplyPrice:0
            // }, 0)

        },
        selectAllEvent(){
        },
         // 选择行
         selectChangeEvent({ row }) {
          this.$refs.xTable.toggleCheckboxRow(row)
            //console.log(this.$refs.xTable.getCheckboxRecords());

        },
        //点击表格行
        cellClickEvent({ row }) {
            this.$refs.xTable.toggleCheckboxRow(row)

        },
        // 新增商品
        addProduct(){
            this.tableData.push(JSON.parse(JSON.stringify(this.initData)))
            this.tableKey++
        },
        ////删除商品
          delProduct(){
            const rows = this.$refs.xTable.getCheckboxRecords()
            if (rows.length === 0) {
                this.$message.warning('请选择需要删除的行')
                return false
            }
            this.$confirm('确定删除此商品吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                rows.forEach(item => {
                    const index = this.tableData.findIndex(row => row._XID === item._XID)
                    this.tableData.splice(index, 1)
                })
            })
          },

          //商品编码数据回调
          echoData(data){
            // this.tableData[this.currentRowIndex] = data
            data.realOutCount === null ? data.realOutCount = 0 : data.realOutCount
            data.realOutAmount === null ? data.realOutAmount = 0 : data.realOutAmount
            if (this.tableData.some(item =>
              item.productCode === data.productCode &&
              item.storageCode === data.storageCode &&
              item.batchCode === data.batchCode
            )) {
              return this.$message.warning('不能选择同一行的退货商品！')
            }
            this.$refs.productSearch.close()
            this.$set(this.tableData, this.currentRowIndex, data)
            this.currentRowIndex = null
          },
          // 提交订单
          submitOrder(){
             if(this.formData.deliveryMethod === 1 && !this.formData.deliveryDate){
              return this.$message.warning('请选择自提时间')
             }
             if(this.tableData.length === 0){
              return this.$message.warning('请选择退货商品')
             }
              // 存储错误信息的行号
                 let productCodeErrors = [];
               // 遍历表格数据并检查商品
                this.tableData.forEach((item, index) => {
                  if (!item.productCode) {
                    productCodeErrors.push(`${index + 1}`);
                  }
                });
                if (productCodeErrors.length > 0) {
                  return this.$message.warning(`第${productCodeErrors.join(',')}行未选择退货商品`);
                }
                // 遍历表格数据并检查是否填写退货数量
                this.tableData.forEach((item, index) => {
                  if (!item.refundCount) {
                    productCodeErrors.push(`${index + 1}`);
                  }
                });
                if (productCodeErrors.length > 0) {
                  return this.$message.warning(`第${productCodeErrors.join(',')}行未填写退货数量`);
                }
                // 遍历表格数据并检查退货数量是否正确
                this.tableData.forEach((item, index) => {
                  if (item.refundCount > item.curRefundCount) {
                    productCodeErrors.push(`${index + 1}`);
                  }
                });
                if (productCodeErrors.length > 0) {
                  return this.$message.warning(`很抱歉，所选第${productCodeErrors.join(',')}行退货库存不满足，辛苦联系对接采购/仓库操作移库！`);
                }
                this.$confirm('请检查退货信息与配送方式，是否确认提交退货单？', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  // 添加全局loading
              const loading = this.$loading({
                lock: true,
                text: '正在提交订单...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });

              const detailList = this.tableData.map(item => ({
                orgCode: item.orgCode,
                supplyCode: item.supplyCode,
                storageCode: item.storageCode,
                productCode:item.productCode,
                erpProductCode: item.erpProductCode,
                standardProductId: item.standardProductId,
                csuid:item.csuid,
                batchCode: item.batchCode,
                refundCount: item.refundCount,
                supplyPrice: item.supplyPrice,
                refundAmount: item.refundAmount,
                remark:'',
                maxRefundCount:item.maxRefundCount,
                refundStorageCount:item.refundStorageCount
              }));
              const params ={
                refundCode: this.formData.refundCode,
                deliveryMethod: this.formData.deliveryMethod,
                carryTime: this.isSelfDelivery ? this.formData.deliveryDate : '',
                detailList
              }
              saveRefundOrder(params).then(res=>{
                if(res.code === 0){
                  this.$message.success('提交成功')
                  // 成功时延迟关闭 loading，与跳转同步
                  setTimeout(() => {
                    loading.close();
                    this.backToPage()
                  }, 500);
                }else{
                  loading.close(); // 失败时直接关闭
                  this.$message.error(res.msg)
                }
              }).finally(() => {
                // 关闭 loading
                loading.close();
              })
            })


          },
          //返回
          backToPage(){
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
          },
          // 打开产品搜索弹窗
          openProductSearch(index){
            this.currentRowIndex = index
            this.$refs.productSearch.open(this.formData.orgName)
          }
      }
  }
  </script>
  <style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper{
    height:400px !important;
}
      #total {
          display: flex;
          flex-direction: row;
          justify-content: space-around;

          #total_content {
              align-items: center;
              font-weight: bolder;
              margin: 10px;
          }
      }
  </style>
