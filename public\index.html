<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= webpackConfig.name %></title>
    <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
	  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #1a1a1a;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 999999;
    }

    .scene {
      position: relative;
      width: 300px;
      height: 100px;
    }

    .capsule {
      position: absolute;
      width: 60px;
      height: 30px;
      background: #4caf50;
      border-radius: 15px;
      left: 0;
      bottom: 30px;
      animation: roll 2.5s linear infinite;
      will-change: transform;
      filter: drop-shadow(0 4px 2px rgba(76, 175, 80, 0.15));
    }

    .capsule::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        #8bc34a 0%, 
        #8bc34a 49%, 
        #4caf50 51%, 
        #4caf50 100%
      );
      border-radius: 15px;
      will-change: transform;
    }

    .ground {
      position: absolute;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, 
        transparent 0%,
        rgba(76, 175, 80, 0.3) 20%,
        rgba(76, 175, 80, 0.5) 50%,
        rgba(76, 175, 80, 0.3) 80%,
        transparent 100%
      );
      bottom: 20px;
    }

    .ground::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg,
        transparent 0%,
        rgba(76, 175, 80, 0.1) 20%,
        rgba(76, 175, 80, 0.2) 50%,
        rgba(76, 175, 80, 0.1) 80%,
        transparent 100%
      );
      bottom: -4px;
    }

    @keyframes roll {
      0% {
        transform: translateX(0) rotate(0deg);
      }
      100% {
        transform: translateX(240px) rotate(720deg);
      }
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      transform: translateY(-100%);
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      font-family: 'Open Sans';
      color: #f1eaea;
      font-size: 19px;
      width: 100%;
      text-align: center;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #eee6e6;
      opacity: 0.5;
    }
  </style>
  </head>
  <body>
    <div id="app">
	    <div id="loader-wrapper">
		    <div class="scene">
			    <div class="capsule"></div>
			    <div class="ground"></div>
		    </div>
		    <div class="load_title">正在加载系统资源，请耐心等待</div>
        </div>
	</div>
  </body>
</html>
