<template>
    <div class="app-container">
        <xyy-panel title="单据信息">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTop" />
            <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="选择供货仓">
                            <el-select v-model="formData.orgCode" placeholder="请选择" disabled>
                                <el-option v-for="item in storeList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="供货单号">
                            <el-input v-model="formData.supplyCode" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="创建日期">
                            <el-input v-model="formData.createTime" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="单据状态">
                            <el-input v-model="formData.supplyStatusStr" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="仓库联系方式">
                            <el-input v-model="formData.wmsContact" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="供货仓库地址">
                            <el-popover trigger="hover" :content="formData.wmsAddress" placement="top">
                                <el-input slot="reference" v-model="formData.wmsAddress" disabled>
                                </el-input>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="创建人">
                            <el-input v-model="formData.createUser" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="下单人/联系电话">
                            <el-input v-model="formData.buyerContact" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="供货商地址">
                            <el-popover trigger="hover" placement="top" :content="formData.supplierAddress">
                                <el-input slot="reference" v-model="formData.supplierAddress" disabled>
                                </el-input>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="供货商家">
                            <el-input v-model="formData.supplierName" disabled />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <!-- <vxe-table ref="xTableAmount" :loading="loading" highlight-current-row height="auto" :data="tableAmountData"
                style="margin-top:10px;width: 1320px;height: 80px;">
                <vxe-table-column title="汇总" width="80" />
                <vxe-table-column v-for="item in addAmountColumns" :key="item.field" :field="item.field"
                    :title="item.title" :width="item.width" />
            </vxe-table> -->
        </xyy-panel>
        <xyy-panel title="供货商品列表">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTabale" style="float:right !important" />
            <div :key="tabKey" class="table-box">
                <vxe-table :row-style="rowStyle" ref="xTable" :loading="loading" highlight-current-row height="440px"
                    :data="tableData" @checkbox-all="selectAllEvent" @checkbox-change="selectChangeEvent"
                    @cell-click="cellClickEvent" :checkbox-config="{ highlight: true }" empty-text="未选择供货商品">
                    <vxe-table-column type="checkbox" fixed="left" width="80" />
                    <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                        :fixed="item.fixed" v-if="!item.hidden" :title="item.title" :width="item.width">
                        <template v-slot="scope">
                            <div v-if="item.field === 'remark'">
                                <el-input v-model="scope.row[item.field]" maxlength="200" placeholder="200字"
                                    show-word-limit></el-input>
                            </div>
                            <div
                                v-else-if="item.field == 'erpProductCode' || item.field == 'csuid' || item.field == 'productCode'">
                                <el-input v-model="scope.row[item.field]" placeholder="商品名称/ERP商品编码/商品编码/产品编码" disabled>
                                    <el-button slot="append" icon="el-icon-search"
                                        @click="openProductSearch(scope.rowIndex)"></el-button>
                                </el-input>
                            </div>
                            <div v-else-if="item.field == 'productInfo'">
                                <el-popover trigger="hover" :content="scope.row[item.field]" placement="top" enterable>
                                    <el-input slot="reference" v-model="scope.row[item.field]"
                                        placeholder="商品名称/ERP商品编码/商品编码/产品编码" disabled>
                                        <el-button slot="append" icon="el-icon-search"
                                            @click="openProductSearch(scope.rowIndex)"></el-button>
                                    </el-input>
                                </el-popover>
                            </div>
                            <div v-else-if="item.field == 'supplyCount'">
                                <el-input :value="scope.row[item.field]" @input="orderNumInput($event, scope.row)"
                                    @change="computeTotalAmount(scope.row)">
                                </el-input>
                            </div>
                            <div v-else-if="item.field == 'supplyPrice'">
                                <el-input :value="scope.row[item.field]" @input="priceInput($event, scope.row)"
                                    @change="computeTotalAmount(scope.row)">
                                </el-input>
                            </div>
                            <div v-else-if="item.field == 'failInfo'">
                                <span style="color: red;">{{ scope.row.failInfo }}</span>
                            </div>
                            <div v-else>
                                <span>{{ scope.row[item.field] }}</span>
                            </div>
                        </template>
                    </vxe-table-column>
                </vxe-table>
            </div>
            <div>
                <div id="total">
                    <div id="total_content">
                        价税合计：
                        <span>{{ `￥${totalTaxValue}` }}</span>
                    </div>
                    <!-- <div id="total_content">
                    金额合计：
                    <span>{{ totalAmount }}</span>
                </div>
                <div id="total_content">
                    税额合计：
                    <span>{{ totalTaxAmount }}</span>
                </div> -->
                </div>
            </div>
        </xyy-panel>
        <product-search ref="productSearch" @echoData="echoData"></product-search>
    </div>
</template>
<script>
import {
    getSupplyGoodsNo,
    getBusiness,
    getAllCooperationOrg,
    saveSupplyGoodsInfo
} from '@/api/order/supplyGoods'
import productSearch from "./components/productSearch.vue";
import utils from "@/utils";
import { addAmountColumns, addColumns } from "./config";
import XEUtils from 'xe-utils'
const currentTime = new Date();
const CreateTime = XEUtils.toDateString(currentTime, 'yyyy-MM-dd HH:mm:ss');
export default {
    name: "OrderSupplierAdd",
    components: {
        productSearch,
    },
    data() {
        return {
            storeList: [],
            btnListTop: [
                {
                    label: "提交订单",
                    type: "primary",
                    clickEvent: this.submitOrder,
                    loading: false,
                    code: "",
                    plain: 'false',
                    // icon: 'search-icon',
                    permission: 'supplier:add:submit'
                },
                {
                    label: "返回",
                    clickEvent: this.backToPage,
                    code: "",
                    plain: 'false',
                    // icon: 'search-icon',
                    permission: 'supplier:add:back'
                },
            ],
            btnListTabale: [
                {
                    label: "新增",
                    type: "primary",
                    clickEvent: this.addRow,
                    code: "",
                    // icon: 'search-icon',
                    permission: 'supplier:add:add'
                },
                {
                    label: "删除",
                    type: "danger",
                    clickEvent: this.deleteRow,
                    code: "",
                    // icon: 'search-icon',
                    permission: 'supplier:add:delete'
                },
            ],
            formData: {
                businessOutCode: "",
                buyerName: "",
                buyerPhone: "",
                buyerContact: '',
                // channelCode: "",
                createTime: CreateTime,
                createUser: this.$store.state.user.name,
                deliveryMethod: "",
                // finishTime: "",
                orgCode: this.$store.getters.orgCode,
                orgName: '',
                supplyCode: '',
                supplyStatusStr: '待申请',
                wmsName: '',
                wmsPhone: '',
                wmsContact: '',
                wmsAddress: '',
                supplierAddress: '',
                supplierName: '',
            },
            tableAmountData: [],
            addAmountColumns: addAmountColumns(),
            tableColumns: addColumns(),
            tableData: [],
            loading: false,
            tabKey: 0,
            currentRow: {},
            totalTaxValue: 0,   //价税合计
            totalAmount: 0, //金额合计
            totalTaxAmount: 0,   //税额合计
            Business: [],   //供应商信息
            CooperationOrg: [],   //合作机构
        }
    },
    watch: {
        tableData: {
            handler(newVal, oldVal) {
                this.computeAmount()
            },
            deep: true
        },
    },
    mounted() {
        this.$nextTick(() => {
            utils.pageActivated()
            this.tabKey++
            this.computeAmount()
        })
        this.apiGetSupplyGoodsNo()
        this.apiGetBusiness()
        this.apiGetAllCooperationOrg()
        this.addRow()
    },
    methods: {
        rowStyle({ row, rowIndex }) {
            if (row.failInfo) {
                return {
                    background: '#FFEDED !important', // 行高根据内容自适应
                };
            }
        },
        //提交订单
        submitOrder() {
            if (this.tableData.length == 0) {
                this.$message.warning('请添加商品')
                return
            }
            for (let i = 0; i < this.tableData.length; i++) {
                if (this.tableData[i].erpProductCode == '' && this.tableData[i].productCode == '' && this.tableData[i].csuid == '') {
                    this.$message.warning(`请选择第${i + 1}行商品`)
                    return
                }
                if (!this.tableData[i].supplyCount || !this.tableData[i].supplyPrice) {
                    this.$message.warning(`请完整填写第${i + 1}行商品${this.tableData[i].productName}的供货数量与供货单价`)
                    return
                }
            }
            this.btnListTop.find(item => item.label == '提交订单').loading = true
            const Loading = this.$loading({
                lock: true,
                text: '正在提交数据……',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
            })
            const params = {
                supplyCode: this.formData.supplyCode,
                orgCode: this.formData.orgCode,
                detailList: this.tableData
            }
            saveSupplyGoodsInfo(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.$message({
                        type: 'success',
                        message: '提交成功'
                    })
                    this.btnListTop.find(item => item.label == '提交订单').loading = false
                    Loading.close()
                    this.backToPage()
                } else {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                    this.btnListTop.find(item => item.label == '提交订单').loading = false
                    Loading.close()
                }
            }).catch(err => {
                this.btnListTop.find(item => item.label == '提交订单').loading = false
                Loading.close()
                console.log(this.tableColumns)
                // err="商品[Y21931031]所属经营范围不再供应商的经营范围之内 [化学药制剂,]"
                let regex = /\[(.*?)\]/;
                let errInfo = err.match(regex)
                this.tableData.forEach(item => {
                    item.failInfo = ''
                })
                if (errInfo) {

                    let result = this.tableData.find(item => {
                        return item.productCode == errInfo[1]
                    })
                    if (result) {
                        result.failInfo = err
                        this.tableColumns.find(item => {
                            return item.field == 'failInfo'
                        }).hidden = false
                    }
                }
            })
        },
        // 获取合作机构
        apiGetAllCooperationOrg() {
            getAllCooperationOrg().then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.CooperationOrg = result
                    this.storeList = result.map(item => {
                        return {
                            label: item.orgName,
                            value: item.orgCode
                        }
                    })
                    const findStore = this.CooperationOrg.find(item => item.orgCode === this.formData.orgCode)
                    this.formData.wmsName = findStore.linkMan
                    this.formData.wmsPhone = findStore.linkPhone
                    this.formData.wmsAddress = findStore.orgAddress
                    this.formData.orgName = findStore.orgName
                    this.formData.wmsContact = findStore.linkMan + '/' + findStore.linkPhone
                } else {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                }
            })
        },
        // 获取供应商信息
        apiGetBusiness() {
            getBusiness().then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.Business = result
                    this.formData.supplierAddress = result.supplierAddress
                    this.formData.supplierName = result.name
                    this.formData.buyerName = result.contactName
                    this.formData.buyerPhone = result.contactMobile
                    this.formData.buyerContact = result.contactName + '/' + result.contactMobile
                } else {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                }
            })
        },
        //获取供货单号
        apiGetSupplyGoodsNo() {
            getSupplyGoodsNo().then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.formData.supplyCode = result
                } else {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                }
            })
        },
        //计算总额
        computeAmount() {
            let sumTotalTaxValue = 0
            // let sumTotalAmount = 0
            // let sumTotalTaxAmount = 0
            this.tableData.forEach(item => {
                if (item.supplyCount && item.supplyPrice) {
                    // sumTotalAmount += Number(item.supplyCount) * Number(item.supplyAmount)
                    // sumTotalTaxAmount += Number((Number(item.supplyCount) * Number(item.supplyAmount) * Number(item.tax)))
                    sumTotalTaxValue += Number(item.supplyPrice) * Number(item.supplyCount)
                }
            })
            // sumTotalTaxValue = (sumTotalAmount ? sumTotalAmount : 0) + (sumTotalTaxAmount ? sumTotalTaxAmount : 0)
            this.totalTaxValue = sumTotalTaxValue.toFixed(2)
            // this.totalAmount = sumTotalAmount.toFixed(2)
            // this.totalTaxAmount = sumTotalTaxAmount.toFixed(2)
        },
        //表格全选方法
        selectAllEvent() { },

        //计算供货金额
        computeTotalAmount(row) {
            if (row.supplyPrice && row.supplyCount) {
                // row.supplyPrice = Number(row.supplyAmount) * Number(row.supplyCount)
                this.$set(row, 'supplyAmount', (Number(row.supplyPrice) * Number(row.supplyCount)).toFixed(2))
            }
        },
        //校验供货单价
        priceInput(value, row) {
            // 使用正则表达式匹配数字和两位小数
            const reg = /^\d*(\.?\d{0,2})$/;
            if (reg.test(value)) {
                // row.supplyAmount = value;
                this.$set(row, 'supplyPrice', value)
            } else {
                // 如果不匹配，去掉最后一个字符
                // row.supplyAmount = value.slice(0, -1);
                this.$set(row, 'supplyPrice', value.slice(0, -1))
            }
        },
        //校验订单数量
        orderNumInput(value, row) {
            // 只保留数字，去除非数字字符
            let newValue = value.replace(/[^\d]/g, '');
            // 去除前导零
            newValue = newValue.replace(/^0+/, '');
            // 更新输入值
            // row.supplyCount = newValue;
            this.$set(row, 'supplyCount', newValue)
        },
        // 选择行
        selectChangeEvent({ row }) {
            this.$refs.xTable.toggleCheckboxRow(row)
        },
        //点击表格行
        cellClickEvent({ row }) {
            this.$refs.xTable.toggleCheckboxRow(row)
            this.currentRow = row
        },
        //删除行
        deleteRow() {
            const rows = this.$refs.xTable.getCheckboxRecords()
            if (rows.length === 0) {
                this.$message.warning('请选择需要删除的行')
                return false
            }
            this.$confirm('确定删除此商品吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                rows.forEach(item => {
                    const index = this.tableData.findIndex(row => row.id === item.id)
                    this.tableData.splice(index, 1)
                })
            })
        },
        //新增行
        addRow() {
            const addRowColumns = JSON.parse(JSON.stringify(addColumns()))
            const addRow = {}
            addRowColumns.forEach(item => {
                addRow[item.field] = ''
            })
            this.tableData.push(addRow)
        },
        // 合并单元格
        // mergeMethod({ rowIndex, columnIndex }) {
        //     if (columnIndex === 2) {
        //         return { rowspan: 1, colspan: 3 };
        //     } else if (columnIndex > 2 && columnIndex <= 4) {
        //         return { rowspan: 0, colspan: 0 };
        //     }
        // },
        //商品编码数据回调
        echoData(data, rowIndex) {
            if (this.tableData.find(item => item.id === data.id)) {
                this.$message.warning('该商品已存在，请重新输入')
                return
            }
            const productInfo = `${data.productCode}, ${data.erpProductCode}, ${data.csuid}`
            this.$set(this.tableData, rowIndex, data)
            this.$set(this.tableData[rowIndex], 'salesRateValueDesc', data.salesRateValue + '%')
            this.$set(this.tableData[rowIndex], 'taxRate', data.salesRateValue)
            this.$set(this.tableData[rowIndex], 'orgName', this.formData.orgName)
            this.$set(this.tableData[rowIndex], 'productInfo', productInfo)
        },
        //返回
        backToPage() {
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
        },
        // 打开产品搜索弹窗
        openProductSearch(row) {
            const wmsData = {
                orgCode: this.formData.orgCode,
                orgName: this.formData.orgName
            }
            this.$refs.productSearch.open(row, wmsData)
        }
    }
}
</script>
<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper {
    height: 400px !important;
}

#total {
    display: flex;
    flex-direction: row;
    justify-content: end;

    #total_content {
        align-items: center;
        font-weight: bolder;
        margin: 10px;
    }
}
</style>