<template>
    <div>
        <xyy-dialog key="dialog1" :footerShow="true" ref="selectInvoice" title="发票列表" width="70%">
            <xyy-panel title="查询条件">
                <!-- 按钮组 start-->
                <btn-group slot="tools" :btn-list="btnListTop" />
                <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
                    <el-row :gutter="20">
                        <el-col :lg="6" :md="6">
                            <el-form-item label="发票号">
                                <el-input v-model="formData.taxNo" placeholder="请输入发票号" clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :lg="12" :md="12">
                            <el-form-item label="时间">
                                <el-date-picker v-model="formData.invoiceDate" type="daterange" range-separator="至"
                                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </xyy-panel>
            <xyy-panel title="发票信息">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row :data="tableData"
                    :checkbox-config="{ highlight: true }" @checkbox-change="selectChangeEvent"
                    :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }"
                    @cell-click="cellClickEvent" @checkbox-all="selectAllEvent">
                    <vxe-table-column type="checkbox" width="60" />
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'operation'">
                                    <span @click="viewInvoicehandler(slotProps.row, slotProps.$rowIndex)"
                                        class="clickWriting">查看发票</span>
                                    <span @click="apiDeleteInvoice(slotProps.row)" class="clickWriting"
                                        style="color: red; margin-left: 12px;">删除</span>
                                </div>
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
                <div class="pager">
                    <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                        :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                            'PrevPage',
                            'JumpNumber',
                            'NextPage',
                            'FullJump',
                            'Sizes',
                            'Total',
                        ]" @page-change="handlePageChange" />
                </div>
            </xyy-panel>
            <span slot='footer' class='dialog-footer'>
                <el-button size='small' @click='close'>返 回</el-button>
                <el-button type='primary' size='small' @click='submit' :loading='submitLoading'>确 认</el-button>
            </span>
        </xyy-dialog>
        <view-invocie ref="viewInvoice" />
    </div>
</template>

<script>
import { pageConfig } from "@/utils";
import { getInvoiceDetail, deleteInvoice } from '@/api/finance/InvoiceLedger'
import viewInvocie from './viewInvocie.vue'
import { selectInvoiceColumns } from '../config'
import XEUtils from 'xe-utils'
const end = new Date();
const start = XEUtils.getWhatDay(end, -30); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd'); // 将结束日期格式化为字符串
export default {
    components: { viewInvocie },
    data() {
        return {
            btnListTop: [
                {
                    label: "查询",
                    clickEvent: this.searchList,
                    type: 'primary',
                    plain: 'false',
                    permission: "finance:enterInvoice:query"
                },
            ],
            formData: { // 查询条件
                taxNo: '',   // 发票号
                invoiceDate: [defaultBeginTime, defaultEndTime],    // 发票日期
            },
            loading: false, // 加载状态
            tableData: [],  // 表格数据
            tableKey: 0,
            tableColumns: selectInvoiceColumns(),
            submitLoading: false,   // 提交加载状态
            tablePage: pageConfig(),    // 表格分页
            checkbox_container: new Map(),  // 存储checkbox的选中状态
        }
    },
    methods: {
        apiDeleteInvoice(row) {
            this.$confirm('删除后不可恢复，确定删除该发票吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteInvoice({ taxNo: row.taxNo }).then(res => {
                    const { code, msg } = res
                    if (code == 0) {
                        this.$message({
                            type: 'success',
                            message: msg
                        })
                        this.getList()
                    } else {
                        this.$message({
                            type: 'error',
                            message: msg
                        })
                    }
                })
            })
        },
        // 保存checkbox的选中状态
        saveCheckbox() {
            // 获取所有通过checkbox选中的表格行数据
            const rows = this.$refs.xTable.getCheckboxRecords()

            // 遍历每一行选中的数据，并将其存放到checkbox_container中
            // 这样可以保持选中状态的数据，供后续的数据操作使用
            rows?.forEach((v) => {
                this.checkbox_container.set(v.id, v)
            })
        },
        // 获取表格数据
        getList() {
            // 等待DOM更新完成后，保存当前checkbox的选中状态
            this.$nextTick(() => {
                this.saveCheckbox()
            })
            this.loading = true
            const params = {
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                taxNo: this.formData.taxNo,
                invoiceTimeStart: this.formData.invoiceDate ? this.formData.invoiceDate[0] : '',
                invoiceTimeEnd: this.formData.invoiceDate ? this.formData.invoiceDate[1] : '',
            }
            getInvoiceDetail(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.tableData = result.list
                    this.tablePage.total = result.total
                    this.loading = false
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            }).finally(() => {
                // 在API请求完成后，无论成功或失败都执行以下操作

                // 根据当前表格数据，找出之前已在checkbox_container中保存的行
                const checkedRows = this.tableData.map(item => {
                    if (this.checkbox_container.has(item.id)) {
                        return item
                    }
                })

                // 重新设置这些行的checkbox为选中状态
                this.$refs.xTable.setCheckboxRow(checkedRows, true)
            })
        },
        // 查询
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
        // 分页
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage
            this.tablePage.pageSize = pageSize
            this.getList()
        },
        // 选择行
        selectChangeEvent({ row, checked }) {
            this.$refs.xTable.toggleCheckboxRow(row)
            // 如果当前行未被选中，则从容器中删除该行数据，避免残留已选状态的数据
            if (!checked) {
                if (this.checkbox_container.has(row.id)) {
                    this.checkbox_container.delete(row.id)
                }
            }
        },
        // 全选
        selectAllEvent({ checked }) {
            if (!checked) {
                this.tableData.forEach(item => {
                    if (this.checkbox_container.has(item.id)) {
                        this.checkbox_container.delete(item.id)
                    }
                })
            }
        },
        //点击表格行
        cellClickEvent({ row }) {
            this.$refs.xTable.toggleCheckboxRow(row)
            // 如果当前行经过切换后状态为未选中，则从容器中删除该行数据
            if (!this.$refs.xTable.isCheckedByCheckboxRow(row)) {
                if (this.checkbox_container.has(row.id)) {
                    this.checkbox_container.delete(row.id)
                }
            }
        },
        // 确认
        submit() {
            this.saveCheckbox()
            const selectRows = Array.from(this.checkbox_container.values())
            if (selectRows.length === 0) {
                this.$message.warning('请选择要发票的数据')
                return
            }
            this.$emit('echo', selectRows)
            this.close()
        },
        // 查看发票
        viewInvoicehandler(row, rowIndex) {
            if (row.imageUrl) {
                if (row.imageUrl.includes('pdf')) {
                    window.open(row.imageUrl)
                } else {
                    this.$refs.viewInvoice.open(row.imageUrl, 'image')
                }
            }
        },
        open(rowIndex) {
            this.$refs.selectInvoice.open()
            this.formData = {
                taxNo: '',
                invoiceDate: [defaultBeginTime, defaultEndTime],
            }
            this.$nextTick(() => {
                this.searchList()
                this.$refs.xTable.setAllCheckboxRow(false)
                this.checkbox_container.clear() // 清空复选框
            })
        },
        close() {
            this.$refs.selectInvoice.close()
        },
    },
}
</script>
<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper {
    height: 230px !important;
}
</style>