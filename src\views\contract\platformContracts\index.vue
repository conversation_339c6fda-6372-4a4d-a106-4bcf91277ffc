<template>
    <div class="app-container">
        <xyy-panel title="查询条件">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTop" />
            <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="任务编号">
                            <el-input v-model="formData.contractNo" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="签署任务主题">
                            <el-input v-model="formData.taskTopic" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="12" :md="12">
                        <el-form-item label="生效/失效时间">
                            <el-date-picker v-model="formData.createTime" value-format="yyyy-MM-dd" type="daterange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :lg="12" :md="12">
                        <el-form-item label="任务发起时间">
                            <el-date-picker v-model="formData.applyTime" value-format="yyyy-MM-dd" type="daterange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel title="签署任务列表">
            <div :key="tabKey" class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }"
                    empty-text="暂无签署任务数据">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            <template slot-scope="scope">
                                <div v-if="item.field === 'operation'">
                                    <span class="clickWriting" style="margin-right: 12px;"
                                        @click="apiGetLogList(scope.row)">查看日志</span>
                                    <span class="clickWriting" style="margin-right: 12px;"
                                        v-if="scope.row.contractStatus == 'PENDING_BUSINESS_SIGN'"
                                        @click="apiGetSignContract(scope.row)">去签署</span>
                                    <span class="clickWriting" v-if="scope.row.contractStatus == 'EXPIRED'"
                                        @click="apiGetDownloadFile(scope.row)">下载文件</span>
                                </div>
                                <div v-else>{{ scope.row[item.field] }}</div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
        <contract-logs ref="contractLogs" />
    </div>
</template>

<script>
import { queryList } from "@/api/contract/platformContracts";
import contractLogs from "./components/contractLogs.vue";
import { pageConfig } from "@/utils";
import { tableColumns } from "./config";
export default {
    name: 'PlatformContracts',
    components: { contractLogs },
    data() {
        return {
            btnListTop: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: this.searchList,
                    code: "",
                    plain: 'false',
                    permission: 'platformContracts:list:query'
                },
                {
                    label: "重置",
                    clickEvent: this.reset,
                    code: "",
                    plain: 'false',
                    permission: 'platformContracts:list:reset'
                },
            ],
            formData: {
                contractNo: '',
                taskTopic: '',
                createTime: null,
                applyTime: null,
            },
            loading: false,
            tableData: [],
            tableKey: 0,
            tableColumns: tableColumns(),
            tablePage: pageConfig(),
            tabKey: 1,
        }
    },
    mounted() {
        this.$nextTick(() => {
        })
    },
    activated() {
        this.$nextTick(() => {
            this.searchList()
        })
    },
    methods: {
        getList() {
            this.loading = true
            const params = {
                contractNo: this.formData.contractNo,
                taskTopic: this.formData.taskTopic,
                effectTimeStartStr: this.formData.createTime ? this.formData.createTime[0] : '',
                effectTimeEndStr: this.formData.createTime ? this.formData.createTime[1] : '',
                createTimeStartStr: this.formData.applyTime ? this.formData.applyTime[0] : '',
                createTimeEndStr: this.formData.applyTime ? this.formData.applyTime[1] : '',
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
            }
            queryList(params).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.tableData = data.list
                    this.tablePage.total = data.total
                    this.loading = false
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            }).finally(() => {
                this.tableKey++
                this.loading = false
            })
        },
        reset() {
            this.formData = {
                contractNo: '',
                taskTopic: '',
                createTime: [],
                applyTime: "",
            }
            this.searchList()
        },
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
        apiGetDownloadFile(row) {
            if (!row.agreementUrl) {
                this.$message.warning('暂无合同文件，请稍后再试！')
                return
            }
            let currentTime = XEUtils.toDateString(new Date(), 'yyyyMMddHHmmss')
            const fileName = `${row.tenantName}_${row.contractTemplateStr}_${currentTime}`
            urlToFile(row.agreementUrl, fileName)
        },
        apiGetSignContract(row) {
            this.$router.push({
                path: '/contract/signContract',
                query: {
                    baseId: row.baseId
                }
            })
        },
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList();
        },
        apiGetLogList(row) {
            this.$refs.contractLogs.open(row)
        }
    },
}
</script>

<style></style>