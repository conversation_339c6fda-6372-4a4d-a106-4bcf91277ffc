<template>
    <div>
        <xyy-dialog key="dialog1" :footerShow="true" ref="viewInvoice" title="查看已上传" width="70%">
            <xyy-panel :titleShow="false">
                <div v-if="type == 'pdf'" class="center-content">
                    <iframe :src="url" width="100%" height="100%" frameborder="0" id="uploadPdf"></iframe>
                </div>
                <div v-else class="center-content">
                    <img :src="url" alt="" id="uploadImg">
                </div>
            </xyy-panel>
        </xyy-dialog>
    </div>
</template>

<script>
export default {
    name: 'viewInvocie',
    data() {
        return {
            url: '',
            srcList: [],
            type: '',
        }
    },
    methods: {
        open(url, type) {
            this.$refs.viewInvoice.open();
            this.url = url ? url : '../../../../assets/images/routerEx.png';
            this.type = type;
        },
        close() {
            this.$refs.viewInvoice.close();
        }
    },
}
</script>

<style lang="scss" scoped>
.center-content {
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
    width: 100%;
    height: 512px;

    #uploadPdf,
    #uploadImg {
        max-width: 100%;
        max-height: 100%;
    }
}
</style>