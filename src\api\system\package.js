import request from '@/utils/request'

// 创建租户套餐
export function addPackage(data) {
  return request({
    url: '/system/package',
    method: 'post',
    data: data
  })
}

// 更新租户套餐
export function updatePackage(data) {
  return request({
    url: '/system/package',
    method: 'put',
    data: data
  })
}

// 删除租户套餐
export function delPackage(id) {
  return request({
    url: '/system/package/' + id,
    method: 'delete'
  })
}

// 获得租户套餐
export function getTenantPackage(id) {
  return request({
    url: '/system/package/' + id,
    method: 'get'
  })
}

// 获得租户套餐分页
export function getTenantPackagePage(query) {
  return request({
    url: '/system/package/list',
    method: 'get',
    params: query
  })
}

// 获取租户套餐精简信息列表
export function getTenantPackageList() {
  return request({
    url: '/system/package/get-simple-list',
    method: 'get'
  })
}
