<template>
  <div class="app-container">
    <xyy-panel title='查询条件'>
      <btn-group slot="tools" :btn-list="btnListTop"/>
      <el-form ref="formData" :model="formData" label-width="120px">
        <el-row :gutter="10">
            <el-col :lg="12" :md="12">
                <el-form-item label="创建时间" prop="date">
                      <el-date-picker
                        v-model.trim="formData.date"
                        type="datetimerange"
                        start-placeholder="开始日期"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        end-placeholder="结束日期"
                        :default-time="['00:00:00','23:59:59']">
                      </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :lg="6" :md="6">
            <el-form-item prop="approvalStatus" label="提现类型">
              <el-select v-model="formData.approvalStatus" style="width:100%" size="small" placeholder="全部" clearable>
                <el-option label="提现单" :value="0"></el-option>
                <el-option label="自动核销单" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item prop="bizNo" label="业务单号">
              <el-input v-model.trim="formData.bizNo" style="width:100%" size="small" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :lg="6" :md="6">
            <el-form-item prop="withdrawNo" label="提现单号">
             <el-input v-model.trim="formData.withdrawNo" style="width:100%" size="small" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </xyy-panel>
    <xyy-panel title='核销记录' >
       <!-- 按钮组 start -->
       <btn-group slot="tools" :btn-list="btnListTable"/>

      <div class="table-box">
        <vxe-table ref="xTable"
        :loading="loading"
        highlight-current-row
        height="auto"
        :data="tableData"
        :key="tableKey"
        >
        <template v-for="item in tableColumns">
          <vxe-table-column :key="item.field" :field="item.field" :title="item.title" :min-width="item.width" :fixed="item.fixed">
            <template #header>
              <div v-if="item.hit">
                <el-tooltip placement="top">
                  <template slot="content">
                    <div v-for="(it,index) in item.hit" :key="index">
                      <div>{{it}}</div>
                    </div>
                  </template>
                  <div>
                    <span>{{item.title}}</span>
                    <span>
                      <svg-icon  icon-class="prompt-icon"/>
                    </span>
                  </div>
                </el-tooltip>
              </div>
              <div v-else>
                {{item.title}}
              </div>
            </template>
            <template v-slot:default="slotProps">
              <div v-if="item.field === 'operation'">
                <span @click="detailhandler(slotProps.row)" class="clickWriting">查看明细</span>
              </div>
              <div v-else>
                {{ slotProps.row[item.field] }}
              </div>
            </template>
          </vxe-table-column>
        </template>
      </vxe-table>
      </div>
      <div class="pager" >
        <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>

    </xyy-panel>
  </div>
</template>
<script>
import { recordColumns } from './config';
import { pageConfig } from '@/utils';
import {paymentVerify} from '@/api/finance/paymentVerify'
import { exportFile } from '@/api/system/exportCenter';
export default {
  name: 'PaymentVerify',
  data() {
    return {
      btnListTop: [
          {
              label: "查询",
              type: "primary",
              clickEvent: () => this.searchList(true),
              code: "",
              plain: 'false',
              permission:'paymentVerify:list:query'
          },
          {
              label: "重置",
              clickEvent: this.reset,
              code: "",
              plain: 'false',
              permission:'paymentVerify:list:reset'
          },
        ],
        btnListTable: [
          {
            label: "导出列表",
            type: "primary",
            clickEvent: this.export,
            permission: "paymentVerify:list:export"
          },
        ],
      formData: {
        date: [],
        approvalStatus: '',
        bizNo: '',
        withdrawNo: '',
        verifyStatus: '',
      },
      loading:false,
      tablePage: pageConfig(),
      tableKey:0,
      tableColumns: recordColumns(),
      tableData: [],
    }
  },
  mounted() {
    this.initDate()
    this.searchList()
  },
  activated() {

      this.searchList()



  },
  methods: {
    // 初始化日期
    initDate() {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - 30)
      this.formData.date = [
        startDate.toISOString().split('T')[0] + ' 00:00:00',
        endDate.toISOString().split('T')[0] + ' 23:59:59'
      ]
    },
    // 查询
    async searchList(flag) {
      if(flag){
        this.tablePage.pageNum = 1
      }
      this.loading = true
      this.params = {
        ...this.formData,
        startApplyDate: this.formData.date ? this.formData.date[0] : '',
        endApplyDate: this.formData.date ? this.formData.date[1] : '',
        pageNum: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
      }
      try{
        const res = await paymentVerify(this.params)
        if (res.code === 200) {
          this.tableData = res.data.list
          this.tablePage.total = res.data.total
        }

      } catch(err) {
        this.$message.error(err.message)
      } finally {
        this.loading = false
        this.tableKey++
      }

    },
    // 重置
    reset() {
      this.$refs.formData.resetFields()
      this.searchList(true)

    },
    // 详情
    detailhandler(row) {
            if (row.withdrawType != 1) {
                this.$router.push({
                    path: '/finance/withdrawalDetails',
                    query: { withdrawNo: row.withdrawNo }
                })
            } else {
                this.$router.push({
                    path: '/finance/autoWriteOffDetail',
                    query: { withdrawNo: row.withdrawNo }
                })
            }},
    // 导出
     export() {
      if (this.tableData.length === 0) {
            this.$message.warning('暂无数据可导出')
            return
          }
          const formInfo = {
            ...this.formData,
            pageNum: this.tablePage.pageNum,
            pageSize: this.tablePage.pageSize,
            startApplyDate: this.formData.date ? `${this.formData.date[0]} `: '',
            endApplyDate: this.formData.date ? `${this.formData.date[1]} ` : '',
          }
          let params = {
              taskBean: 'FinanceWithdrawRemote_findPayReport',
              colNameDesc: this.tableColumns.filter(item => item.field != 'operation').map(item => item.title).join(','),
              colName: this.tableColumns.filter(item => item.field != 'operation').map(item => item.field).join(','),
              moduleName: 'FINANCE',
              menuDesc: '商业付款核销记录',
              exportParams: JSON.stringify(formInfo)
          }
          this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });

    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNum = currentPage
      this.tablePage.pageSize = pageSize
      this.searchList()
    },


  }
}

</script>
<style lang="scss" scoped>
::v-deep .vxe-body--column{
  max-height: 100px !important;
}
::v-deep .vxe-cell{
  max-height: 100px !important;
}

</style>
