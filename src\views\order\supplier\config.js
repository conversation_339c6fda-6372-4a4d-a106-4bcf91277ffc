export function planColumns(){
    return [
        { field: 'orgName', title: '合作仓库', width: 220 },
        { field: 'createTimeStr', title: '创建时间', width: 160 },
        { field: 'supplyCode', title: '供货单号', width: 230 },
        { field: 'businessOutCode', title: '本公司出库单号', width: 230},
        { field: 'supplyStatusName', title: '单据状态', width: 160},
        { field: 'productTypeCount', title: '品种数', width: 160 },
        { field: 'supplyCount', title: '供货数量', width: 160},
        { field: 'supplyAmount', title: '供货金额（元）', width: 160},
        { field: 'confirmTimeStr', title: '确认发货时间', width: 200},
        { field: 'finishTimeStr', title: '完成时间', width: 160},
        { field: 'deliveryMethodName', title: '配送方式', width: 160},
        { field: 'operation1', title: '操作', width: 200,fixed: "right"}
    ]
}
export function productColumns(){
    return [
        { field: 'orgName', title: '合作仓库', width: 220 },
        { field: 'createTimeStr', title: '创建时间', width: 160 },
        { field: 'supplyCode', title: '供货单号', width: 160 },
        { field: 'businessOutCode', title: '本公司出库单号', width: 160},
        { field: 'skuCategory', title: '商品类别', width: 160 },
        { field: 'erpProductCode', title: '本公司商品编码', width: 160},
        { field: 'productCode', title: '产品编码', width: 200},
        { field: 'csuid', title: '商品编码', width: 160},
        { field: 'standardProductId', title: '标准库id', width: 160},
        { field: 'productName', title: '商品名称', width: 160},
        { field: 'spec', title: '规格', width: 160},
        { field: 'productUnit', title: '单位', width: 200},    
        { field: 'manufacturer', title: '生产厂家', width: 200},    
        { field: 'supplyCount', title: '供货数量', width: 200},    
        { field: 'supplyPrice', title: '供货单价（元）', width: 200},
        { field: 'supplyAmount', title: '供货金额（元）', width: 200},   
        { field: 'taxRate', title: '税率', width: 200},        
        { field: 'remark', title: '备注', width: 200},    
        { field: 'storageCount', title: '实际入库数量', width: 200},    
        { field: 'storageAmount', title: '实际入库金额（元）', width: 200},    
        { field: 'operation2', title: '操作', width: 160,fixed: "right"}  
    ]
}

export function detailAmountColumns(){
    return [
        { field: 'productTypeCount', title: '品种数', width: 80 },
        // { field: 'supplyCount', title: '供货数量', width: 160 },
        // { field: 'supplyAmount', title: '订单金额(元)', width: 160 },
        { field: 'supplyCount', title: '实际供货数量', width: 150},
        { field: 'supplyAmount', title: '预计供货金额(元)', width: 150 },
        { field: 'storageCount', title: '实际入库数量', width: 150},
        { field: 'storageAmount', title: '实际入库金额(元)', width: 150},
        { field: 'rejectCount', title: '拒收数量共', width: 120},
        { field: 'rejectAmount', title: '拒收金额(元)'}
    ]
}
export function detailColumns(){
    return [
        { field: 'productCode', title: '产品编码', width: 200,fixed: "left"},
        { field: 'businessOutCode', title: '本公司出库单号', width: 160,fixed: "left"},
        { field: 'csuid', title: '商品编码', width: 160},
        { field: 'orgName', title: '合作仓库', width: 220 },
        { field: 'createTimeStr', title: '创建时间', width: 160 },
        { field: 'supplyCode', title: '供货单号', width: 160 },

        { field: 'skuCategory', title: '商品类别', width: 160 },
        { field: 'erpProductCode', title: '本公司商品编码', width: 160},
        

        { field: 'standardProductId', title: '标准库id', width: 160},
        { field: 'productName', title: '商品名称', width: 160},
        { field: 'spec', title: '规格', width: 230},
        { field: 'productUnit', title: '单位', width: 200},    
        { field: 'manufacturer', title: '生产厂家', width: 200},    
        { field: 'supplyCount', title: '供货数量', width: 200},    
        { field: 'supplyPrice', title: '供货单价（元）', width: 200},  
        { field: 'supplyAmount', title: '供货金额（元）', width: 200},   
        { field: 'taxRate', title: '税率', width: 200},   
        { field: 'remark', title: '备注', width: 200},    
        { field: 'storageCount', title: '实际入库数量', width: 200},    
        { field: 'storageAmount', title: '实际入库金额（元）', width: 200},
        { field: 'rejectCount', title: '拒收数量', width: 200},
        { field: 'rejectReasonDesc', title: '拒收原因', width: 300},
        { field: 'rejectPicCount', title: '查看异常图片', width: 200}
    ]
}
export function addAmountColumns(){
    return [
        { field: 'test', title: '品种数', width: 120 },
        { field: 'test1', title: '供货数量', width: 160 },
        { field: 'test2', title: '订单金额（元）', width: 160 },
        { field: 'test3', title: '实际供货数量', width: 200},
        { field: 'test4', title: '预计供货金额（元）', width: 200 },
        { field: 'test5', title: '实际入库数量', width: 200},
        { field: 'test6', title: '实际入库金额（元）', width: 200}
    ]
}
export function addColumns(){
    return [
        // { field: 'productCode', title: '产品编码', width: 230,fixed: "left"},
        // { field: 'erpProductCode', title: '本公司商品编码', width: 230 ,fixed: "left"},
        // { field: 'csuid', title: '商品编码', width: 230},
        // { field: 'standardProductId', title: '标准库id', width: 160 },
        { field: 'productInfo', title: '商品信息', width: 230, fixed: "right" },
        { field: 'productName', title: '商品名称', width: 160},
        { field: 'spuCategoryName', title: '商品类别', width: 160},
        { field: 'spec', title: '规格', width: 200},
        { field: 'productUnit', title: '单位', width: 160},
        { field: 'manufacturer', title: '生产厂家', width: 160},
        { field: 'code', title: '69码', width: 160},
        { field: 'approvalNumber', title: '批准文号', width: 160},
        { field: 'mediumPackageNum', title: '中包装数', width: 160},
        { field: 'pieceLoading', title: '件包装数', width: 160},
        { field: 'supplyCount', title: '供货数量', width: 160, fixed: "left" },
        { field: 'supplyPrice', title: '供货单价（元）', width: 160, fixed: "left"},
        { field: 'supplyAmount', title: '供货金额（元）', width: 160},
        { field: 'failInfo', title: '失败原因', width: 160,hidden:true},
        { field: 'salesRateValueDesc', title: '税率', width: 160},
        { field: 'remark', title: '备注', width: 160},
        { field: 'orgName', title: '合作仓库', width: 220 },
    ]
}

export function confirmColumns(){
    return [
        { field: 'productCode', title: '产品编码', width: 160,fixed: "left"},
        { field: 'erpProductCode', title: '本公司商品编码', width: 160,fixed: "left"},
        { field: 'csuid', title: '商品编码', width: 160},
        { field: 'orgName', title: '合作仓库', width: 220},
        { field: 'standardProductId', title: '标准库id', width: 160},
        { field: 'productName', title: '商品名称', width: 160},
        { field: 'skuCategory', title: '商品类别', width: 160},
        { field: 'spec', title: '规格', width: 230},
        { field: 'productUnit', title: '单位', width: 160},
        { field: 'manufacturer', title: '生产厂家', width: 160},
        { field: 'code', title: '69码', width: 160},
        { field: 'approvalNumber', title: '批准文号', width: 160},
        { field: 'mediumPackageNum', title: '中包装数', width: 160},
        { field: 'pieceLoading', title: '件包装数', width: 160},
        { field: 'supplyCount', title: '供货数量', width: 160},
        { field: 'supplyPrice', title: '供货单价（元）', width: 160},
        { field: 'supplyAmount', title: '供货金额（元）', width: 160},
        { field: 'taxRateDesc', title: '税率', width: 160},
        // { field: 'rebateRate', title: '佣金率', width: 160},
        { field: 'remark', title: '备注', width: 160},
    ]
}

export function confirmAmountColumns(){
    return [
        { field: 'productTypeCount', title: '品种数', width: 120 },
        // { field: 'supplyCount', title: '供货数量', width: 160 },
        // { field: 'supplyAmount', title: '订单金额（元）', width: 160 },
        { field: 'realSupplyCount', title: '实际供货数量', width: 200},
        { field: 'realSupplyAmount', title: '预计供货金额（元）', width: 200 },
        { field: 'storageCount', title: '实际入库数量', width: 200},
        { field: 'storageAmount', title: '实际入库金额（元）', width: 200}
    ]
}

export function productDataColumns() {
    return [
        { field: 'orgName', title: '下单仓', width: 120 },
        { field: 'erpProductCode', title: '本公司商品编码', width: 120 },
        { field: 'csuid', title: '商品编码', width: 120 },
        { field: 'productCode', title: '产品编码', width: 120 },
        { field: 'productName', title: '商品名称', width: 120 },
        { field: 'spuCategoryName', title: '商品类别', width: 120 },
        { field: 'spec', title: '规格', width: 120 },
        { field: 'productUnit', title: '单位', width: 120 },
        { field: 'manufacturer', title: '生产厂家', width: 120 },
        { field: 'code', title: '69码', width: 120 },
    ]
}
