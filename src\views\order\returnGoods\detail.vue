<template>
  <div class="app-container">
      <xyy-panel title="查询条件">
        <!-- 按钮组 start-->
      <btn-group slot="tools" :btn-list="btnListTop"/>
        <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
          <el-row :gutter="20">
              <el-col :lg="6" :md="6">
                  <el-form-item label="选择供货仓" :required="true">
                    <el-input v-model="formData.orgName" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="退货单号">
                      <el-input v-model="formData.refundCode" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="创建日期">
                      <el-input v-model="formData.createTimeStr" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="创建人">
                      <el-input v-model="formData.createUser" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="单据状态">
                      <el-input v-model="formData.orderStatusName" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="仓库联系方式">
                      <el-input v-model="formData.wmsLinkmanAndPhone" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="12" :md="12">
                  <el-form-item label="供货仓库地址">
                      <el-input v-model="formData.wmsAddress" disabled/>
                  </el-form-item>
              </el-col>

              <el-col :lg="6" :md="6">
                  <el-form-item label="供货商家">
                      <el-input v-model="formData.supplierName" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="下单人/联系电话">
                      <el-input v-model="formData.buyerNameAndPhone" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="12" :md="12">
                  <el-form-item label="供货商地址">
                      <el-input v-model="formData.supplierAddress" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6">
                  <el-form-item label="选择配送方式" :required="true">
                    <el-input v-model="formData.deliveryMethodName" disabled/>
                  </el-form-item>
              </el-col>
              <el-col :lg="6" :md="6" v-if="formData.deliveryMethod === 1">
                <el-form-item label="自提时间" :required="true">
                  <el-input v-model="formData.createTimeStr" disabled/>
                </el-form-item>
              </el-col>
          </el-row>
        </el-form>
      </xyy-panel>
      <xyy-panel title="退货商品列表" red-text="多个品种一起下退单，更节省费用">
          <div :key="tabKey" class="table-box">
              <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData" :key="tableKey"
                   empty-text="未选择退货商品">
                  <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field" :title="item.title" :width="item.width" >
                    <template #header>
                      <div v-if="item.hit">
                        <el-tooltip placement="top">
                          <template slot="content">
                            <div v-for="(it,index) in item.hit" :key="index">
                              <div>{{it}}</div>
                            </div>
                          </template>
                          <div>
                            <span>{{item.title}}</span>
                            <span>
                              <svg-icon icon-class="prompt-icon"/>
                            </span>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        {{item.title}}
                      </div>
                    </template>
                    <template v-slot="{ row }">
                      <div v-if="item.field === 'orgName'">
                        <span>{{ formData.orgName }}</span>
                      </div>
                      <div v-else-if="item.field === 'realOutCount'">
                        {{ row.realOutCount === null ? 0 : row.realOutCount }}
                      </div>
                      <div v-else-if="item.field === 'realOutAmount'">
                        {{ row.realOutAmount === null ? 0 : row.realOutAmount }}
                      </div>
                      <div v-else>
                        <span>{{ row[item.field] }}</span>
                      </div>
                      </template>
                  </vxe-table-column>
              </vxe-table>
          </div>
          <div id="total">
            <div id="total_content">
              退货数量：
              <span>{{ refundCount }}</span>
            </div>
            <div id="total_content">
              退货单金额：
              <span>{{ refundAmount }}</span>
            </div>
            <div id="total_content">

            </div>
          </div>
    </xyy-panel>
  </div>
  </template>
  <script>
  import utils from "@/utils";
  import { returnDetailColumns } from "./config";
  import {viewRefundOrder} from "@/api/order/returnGoods";
  export default{
      name: "OrderReturnDetail",
      data(){
          return{
              btnListTop: [
                  {
                      label: "返回",
                      clickEvent: this.backToPage,
                      code: "",
                      plain: 'false',
                      permission:'returnGoods:detail:back'
                  },
              ],
              formData:{},
              tableColumns: returnDetailColumns(),
              tableData:[],
              loading:false,
              tabKey:0,
              tableKey:0,
              refundCount:0,
              refundAmount:0
          }
      },
      mounted(){
          this.$nextTick(()=>{
              utils.pageActivated()
              this.tabKey++
              this.tableKey++
          })
          this.getDetailInfo()
      },
      methods:{
        getDetailInfo(){
          // 添加全局loading
          const loading = this.$loading({
                lock: true,
                text: '正在获取退货单详情...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
          const id = this.$route.query.id
          viewRefundOrder({refundCode:id}).then(res=>{
              if(res.code === 0){
                this.tableData = res.result.detailList
                this.formData = res.result
                this.formData.wmsLinkmanAndPhone = res.result.wmsLinkman + '/' + res.result.wmsPhone
                this.formData.buyerNameAndPhone = res.result.buyerName + '/' + res.result.buyerPhone
                this.formData.createTimeStr = res.result.createTime.split('T')[0]
                this.refundCount = res.result.detailList.reduce((pre,cur)=>{
                  return pre + cur.refundCount
                },0)
                this.refundAmount = res.result.detailList.reduce((pre,cur)=>{
                  return pre + cur.refundAmount
                },0).toFixed(2)
              } else {
                  this.$message.error(res.msg)
              }
          }).finally(()=>{
              loading.close()
              this.tableKey++
          })
        },

          //返回
          backToPage(){
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
          },

      }
  }
  </script>
  <style lang="scss" scoped>
      #total {
          display: flex;
          flex-direction: row;
          justify-content: space-around;

          #total_content {
              align-items: center;
              font-weight: bolder;
              margin: 10px;
          }
      }
  </style>
