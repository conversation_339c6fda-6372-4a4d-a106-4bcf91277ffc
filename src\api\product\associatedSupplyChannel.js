import request from '@/utils/request'

//获取列表数据
export function queryList(data) {
  return request({
    url: '/product/associate/list',
    method: 'get',
    params: data
  })
}

// 下载模板
export function downloadTemplate() {
  return request({
    responseType: 'blob',
    url: '/product/associate/template',
    method: 'get',
  })
}

// 对码关联
export function associate(data) {
  return request({
    url: '/product/associate/associate',
    method: 'post',
    data
  })
}

// 取消对码关联
export function cancelAssociate(data) {
  return request({
    url: '/product/associate/associateOff',
    method: 'post',
    data
  })
}
