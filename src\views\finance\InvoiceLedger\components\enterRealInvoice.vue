<template>
    <div>
        <xyy-dialog key="dialog1" :footerShow="true" ref="enterRealInvoice" title="录入实体发票" width="70%">
            <xyy-panel :titleShow="false">
                <!-- 按钮组 start-->
                <btn-group slot="tools" :btn-list="btnListTop" />
                <vxe-table ref="xTable" :loading="loading" highlight-current-row :data="tableData">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'operation'">
                                    <span @click="uploadInvoicehandler(slotProps.row, slotProps.$rowIndex)"
                                        class="clickWriting">上传发票</span>
                                    <span @click="deleteOrderhandler(slotProps.row)" class="clickWriting"
                                        style="margin-left:12px; color: red;">删除</span>
                                </div>
                                <div v-else-if="item.field == 'taxNo'">
                                    <el-input v-model="slotProps.row[item.field]" placeholder="请输入内容" />
                                </div>
                                <div v-else-if="item.field == 'invoiceTime'">
                                    <el-date-picker v-model="slotProps.row[item.field]" type="date" placeholder="请选择"
                                        value-format="yyyy-MM-dd" :picker-options="pickerOptions">
                                    </el-date-picker>
                                </div>
                                <div v-else-if="item.field == 'amount'">
                                    <el-input v-model="slotProps.row[item.field]"
                                        @input="amontInput($event, slotProps.row)" placeholder="请输入内容" />
                                </div>
                                <div v-else-if="item.field == 'taxRate'">
                                    <el-input v-model="slotProps.row[item.field]"
                                        @input="taxAmountInput($event, slotProps.row)"
                                        @change="taxAmountChange(slotProps.row)" placeholder="请输入内容" />
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </xyy-panel>
            <span slot='footer' class='dialog-footer'>
                <el-button size='small' @click="close('click')">返 回</el-button>
                <el-button type='primary' size='small' @click='submit' :loading='submitLoading'>保 存</el-button>
            </span>
        </xyy-dialog>
        <upload-invoice ref="uploadInvoice" @imgUrlCallback="imgUrlCallback" />
    </div>
</template>

<script>
import { saveInvoiceDetail } from '@/api/finance/InvoiceLedger'
import uploadInvoice from './uploadInvoice.vue'
import { realInvoiceColumns } from '../config'
export default {
    name: 'EnterRealInvoice',
    components: { uploadInvoice },
    data() {
        return {
            btnListTop: [
                {
                    label: "新增明细",
                    type: "primary",
                    clickEvent: this.addRow,
                    plain: 'false',
                    permission: "finance:InvoiceLedger:addRow"
                },
            ],
            loading: false,
            tableData: [],
            tableColumns: realInvoiceColumns(),
            submitLoading: false,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            }
        }
    },
    methods: {
        imgUrlCallback(url, rowIndex) {
            this.tableData[rowIndex].imageUrl = url
        },
        // 金额修改
        amontInput(value, row) {
            // 规范输入：移除无效字符并限制小数点位数
            let newValue = value.replace(/[^-\d.]/g, ''); // 删除非数字/负号/小数点字符
            const decimalRegex = /^-?\d*(\.\d{0,2})?$/;

            // 如果存在多个小数点或不满足小数位数，进行修正
            if (!decimalRegex.test(newValue)) {
                newValue = newValue
                    .split('.') // 以小数点分割
                    .slice(0, 2) // 保留整数和一个小数部分
                    .join('.'); // 重新组合
                newValue = newValue.replace(/(\.\d{2})\d+/, '$1'); // 限制小数点后2位
            }

            // 更新值
            this.$set(row, 'amount', newValue);
        },
        // 税率修改
        taxAmountChange(row) {
            if (Number(row.taxRate) > 100) {
                this.$set(row, 'taxRate', '')
                this.$message.warning('发票税率不能大于100，请重新输入')
            }
        },
        // 税率修改
        taxAmountInput(value, row) {
            // 只保留数字，去除非数字字符
            let newValue = value.replace(/[^\d]/g, '');
            // 去除前导零，但允许单个0
            newValue = newValue.replace(/^0+(?=\d)/, '');
            // 更新输入值
            this.$set(row, 'taxRate', newValue);
        },
        // 保存
        submit() {
            if (this.tableData.length == 0) {
                this.$message.warning('请添加发票')
                return
            }
            let emptyArr = []
            let emptyUrl = []
            this.tableData.forEach((item, index) => {
                if (!item.taxNo || !item.invoiceTime || !item.amount || !item.taxRate) {
                    emptyArr.push(index + 1)
                }
                if (!item.imageUrl) {
                    emptyUrl.push(index + 1)
                }
            })
            if (emptyArr.length > 0) {
                this.$message.warning(`请填写第${emptyArr.join(',')}行发票信息`)
                return
            }
            if (emptyUrl.length > 0) {
                this.$message.warning(`请上传第${emptyUrl.join(',')}行发票图片`)
                return
            }
            this.submitLoading = true
            saveInvoiceDetail(
                this.tableData
            ).then(res => {
                const { code, msg } = res
                if (code === 0) {
                    this.$message.success(msg)
                    this.submitLoading = false
                    this.close()
                } else {
                    this.$message.error(msg)
                    this.submitLoading = false
                }
            }).finally(() => {
                this.submitLoading = false
            })
        },
        // 上传发票
        uploadInvoicehandler(row, rowIndex) {
            this.$refs.uploadInvoice.open(row, rowIndex)
        },
        // 删除明细
        deleteOrderhandler(row) {
            this.$confirm('确定删除该明细吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableData.splice(this.tableData.indexOf(row), 1)
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                })
            })
        },
        // 新增明细
        addRow() {
            const addRowColumns = JSON.parse(JSON.stringify(realInvoiceColumns()))
            const addRow = {}
            addRowColumns.forEach(item => {
                addRow[item.field] = ''
            })
            this.tableData.push(addRow)
        },
        // 打开弹窗
        open() {
            this.$refs.enterRealInvoice.open()
            this.tableData = []
            this.addRow()
        },
        // 关闭弹窗
        close(type) {
            if (type == 'click') {
                this.$confirm('关闭会丢失当前录入的信息，确认关闭吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$refs.enterRealInvoice.close()
                }).catch(() => {

                })
            } else {
                this.$refs.enterRealInvoice.close()
            }
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper {
    height: 320px !important;
}
</style>