import request from '@/utils/request'

// 发票查询页
export function getInvoiceList(params) {
    return request({
        url: '/finance/invoice/query',
        method: 'post',
        data: params
    })
}

//保存发票
export function saveInvoice(params) {
    return request({
        url: '/finance/invoice/save',
        method: 'post',
        data: params
    })
}

//上传发票图片
export function uploadInvoice(params) {
    return request({
        url: '/common/uploadJPG',
        method: 'post',
        data: params
    })
}

//查询金额
export function getTotalSaleAmount(params) {
    return request({
        url: '/finance/invoice/getAmountView',
        method: 'post',
        data: params
    })
}

//保存发票
export function saveInvoiceDetail(params) {
    return request({
        url: '/finance/invoice/file/saveFile',
        method: 'post',
        data: params
    })
}

//发票查询接口
export function getInvoiceDetail(params) {
    return request({
        url: '/finance/invoice/file/query',
        method: 'post',
        data: params
    })
}

//查看发票详情
export function getInvoiceDetailById(params) {
    return request({
        url: '/finance/invoice/detail',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        method: 'post',
        data: params
    })
}
//发票台账查看商品明细
export function getProductDetail(param) {
  const params = new URLSearchParams()
  params.append('invoiceNo', param.invoiceNo)
  return request({
    url: '/finance/invoice/getProductDetail',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data:params

  })
}

//下载发票文件
export function downloadInvoiceFile(url) {
    return request({
        url: url,
        method: 'get',
        responseType: 'blob'
    })
}

//撤销申请
export function cancelInvoice(params) {
    return request({
        url: '/finance/invoice/auditForRollback',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        method: 'post',
        data: params
    })
}

//审核提交
export function auditInvoice(params) {
    return request({
        url: '/finance/invoice/submit',
        method: 'post',
        data: params
    })
}

//退补价单详情页汇总
export function getInvoicesDetailList(params) {
    return request({
        url: '/finance/refund/adjustment/summary',
        method: 'post',
        data: params
    })
}

  //退补价单详情页明细分页列表
export function getInvoicesList(params) {
    return request({
        url: '/finance/refund/adjustment/pageQuery',
        method: 'post',
        data: params
    })
}

//删除发票
export function deleteInvoice(params) {
    return request({
        url: '/finance/invoice/file/delete',
        method: 'post',
        data: params
    })
}