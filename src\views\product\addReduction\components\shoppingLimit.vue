<template>
  <div>
    <xyy-dialog title="查看购物限制" ref="shoppingLimitDialog" width="60%"  @on-close="onClose" >
      <xyy-panel :titleShow="false" >
        <el-form ref="formData" :model="formData" label-width="120px" :rules="rules" >
          <el-row :gutter="20">
            <el-col :lg="12" :md="12">
              <el-form-item label="客户起购数量" prop="leastPurchaseNum" >
            <el-input
            :disabled="loading"
              v-model="formData.leastPurchaseNum"
              placeholder="请输入正整数"
              @input="handleMinPurchaseInput">
            </el-input>
          </el-form-item>
            </el-col>
            <el-col :lg="12" :md="12">
              <span>起购：药店单次购买的最小采购数量，需为正整数。若商品不可拆零，起购数量需为中包装的倍数</span>
            </el-col>
          </el-row>
         <el-row :gutter="20">
            <el-col :lg="12" :md="12">
              <el-form-item label="是否限购" prop="isLimited">
                <el-switch
                :disabled="loading"
                 :width="50"
                 @change="resetLimit"
                  v-model="formData.isLimited">
                </el-switch>
              </el-form-item>
            </el-col>
            <el-col :lg="12" :md="12">
              <span>限购:药店在设定的周期内可购买的最大采购数量,需为正整数。限购数量需≥起购数量，限购数量需≥中包装数量</span>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="formData.isLimited">
            <el-col :lg="12" :md="12">
              <el-form-item label="客户限购数量" prop="limitedQty" >
                <el-input
                :disabled="loading"
                  v-model="formData.limitedQty"
                  placeholder="请输入正整数"
                  @input="handleMaxPurchaseInput">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="12" :md="12">
              <span>限购:药店在设定的周期内可购买的最大采购数量,需为正整数。限购数量需≥起购数量，限购数量需≥中包装数量</span>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="formData.isLimited">
            <el-col :lg="12" :md="12">
              <el-form-item label="限购类型" prop="limitedRuleType" >
                <el-select v-model="formData.limitedRuleType" placeholder="请选择限购类型" @change="resetTime" :disabled="loading">
                  <el-option label="时间范围" :value="1"></el-option>
                  <el-option label="单笔" :value="2"></el-option>
                  <el-option label="每天(每天00:00到24:00)" :value="3"></el-option>
                  <el-option label="每周(每周一00:00到周日24:00)" :value="4"></el-option>
                  <el-option label="每月(每月一号00:00到每月最后一天24:00)" :value="5"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="formData.limitedRuleType==1 && formData.isLimited">
            <el-col :lg="12" :md="12">
              <el-form-item label="限购生效周期" prop="opPurchaseTime">
                <el-date-picker
                :disabled="loading"
                 :picker-options="pickerOptions"
                  v-model="formData.opPurchaseTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="daterange"
                  :default-time="['00:00:00', '23:59:59']"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </xyy-panel>
      <span slot="footer" class="dialog-footer">
          <el-button @click="onClose">取 消</el-button>
          <el-button type="primary" @click="onSubmit" :loading="loading">提交</el-button>
        </span>

    </xyy-dialog>
  </div>


</template>
<script>
import {getPurchaseLimit,savePurchaseLimit} from '@/api/product/addReduction'
export default ({
  name: 'ShoppingLimit',
  data() {
    return {
      loading: false,
      row:{},
      formData:{
        leastPurchaseNum: '', // 客户起购数量
        limitedQty: '', // 限购数量
        limitedRuleType: '', // 限购类型 0不限购 1时间范围 2单笔 3每天 4每周 5每月
        opPurchaseTime: [], // 限购时间
        isLimited:false,
        mediumPackageNum:null, // 中包装数
      },
      // 时间限制
      pickerOptions: {
          disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
          }
        },
      rules: {
        // leastPurchaseNum: [
        //   { required: true, message: '请填写起购数量', trigger: 'blur' },

        // ],
        limitedQty: [
          { required: true, message: '请填写限购数量', trigger: 'blur' },
          { validator:this.validateNumber, trigger: 'blur' }
        ],
        limitedRuleType: [
          { required: true, message: '请选择限购类型', trigger: 'change' },
        ],
        opPurchaseTime: [
          { required: true, message: '请选择生效周期', trigger: 'change' },
        ],
      }
    }
  },
  methods: {
    // 获取限购信息
    getInfo() {

      getPurchaseLimit({csuId: this.row.csuid}).then(res => {
        if(res.code === 0){
          this.formData.leastPurchaseNum = res.result.leastPurchaseNum
          this.formData.limitedQty = res.result.limitedQty
          this.formData.limitedRuleType = res.result.limitedRuleType
          this.formData.opPurchaseTime = res.result.purchaseTimeStart ? [res.result.purchaseTimeStart, res.result.purchaseTimeEnd] : []
          this.formData.isLimited = res.result.isLimited == 1 ? true : false
          this.formData.mediumPackageNum = res.result.mediumPackageNum

        }else{
          this.$message.error(res.msg)

        }

      })
    },
    // 处理输入，确保只能输入数字
    handleMinPurchaseInput(value) {
      // 只允许输入数字
      value = value.replace(/[^\d]/g, '');

      // 转为数字并去除前导零
      let num = parseInt(value, 10);

      // 如果是数字且大于等于1，则保存值
      if (!isNaN(num) && num >= 1) {
        this.formData.leastPurchaseNum = num;
      } else {
        this.formData.leastPurchaseNum = '';
      }
    },

    // 处理输入，确保只能输入数字
    handleMaxPurchaseInput(value) {
      // 只允许输入数字
      value = value.replace(/[^\d]/g, '');

      // 转为数字并去除前导零
      let num = parseInt(value, 10);

      // 如果是数字且大于等于1，则保存值
      if (!isNaN(num) && num >= 1) {
        this.formData.limitedQty = num;
      } else {
        this.formData.limitedQty = '';
      }
    },

    //校验限购数量比起购大
    validateNumber(rule, value, callback) {
      if (value < this.formData.leastPurchaseNum) {
        callback(new Error('限购数量需大于等于起购数量'));
      } else {
        callback();
      }
    },
    open(val) {
      this.row = val
      this.getInfo()
      this.$refs.shoppingLimitDialog.open()
    },
    onClose() {
      this.$refs.formData.resetFields()
      this.$refs.shoppingLimitDialog.close()
    },
    // 重置限购
    resetLimit(val) {
      if (!val) {
        this.formData.limitedQty = ''
        this.formData.limitedRuleType = ''
        this.formData.opPurchaseTime = []
      }
    },
    // 重置时间
    resetTime(val) {
      if (val != 1) {
        this.formData.opPurchaseTime = []
      }
    },
    // 提交
    onSubmit() {
      this.loading = true
      this.$refs.formData.validate((valid) => {
        if (!valid) return this.loading = false
        else if(this.formData.leastPurchaseNum % this.formData.mediumPackageNum != 0){
          this.$message.warning('起购数量需为中包装的倍数')
          this.loading = false
          return
        }else if(this.formData.limitedQty % this.formData.mediumPackageNum != 0){
          this.$message.warning('限购数量需为中包装的倍数')
          this.loading = false
          return
        }
        else {
          const params = {
            csuId: this.row.csuid,
            leastPurchaseNum: this.formData.leastPurchaseNum,
            limitedQty: this.formData.limitedQty,
            limitedRuleType: this.formData.limitedRuleType,
            purchaseTimeStart: this.formData.opPurchaseTime[0] ? this.formData.opPurchaseTime[0] : '',
            purchaseTimeEnd: this.formData.opPurchaseTime[1] ? this.formData.opPurchaseTime[1] : '',
            isLimited: this.formData.isLimited ? 1 : 0,
            mediumPackageNum: this.formData.mediumPackageNum,
          }
          // console.log(params);

          savePurchaseLimit(params).then(res => {
            if(res.code === 0){
              this.$message.success('设置成功')
              this.onClose()
            }else{
              this.$message.error(res.msg)
            }
          }).finally(() => {
            this.loading = false
          })

        }
      })
    }
  }
})
</script>
<style lang="scss" scoped>
</style>
