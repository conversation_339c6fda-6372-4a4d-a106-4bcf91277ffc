<template>
    <div>
        <xyy-dialog title='价格提醒' ref="calcRankDiaglog" width='500px' @on-close="onClose">
            <xyy-panel :titleShow="false">
                    <div style="font-size: 16px;color: black;">
                    <div class="mg">
                        <span>历史销售价：{{row ? row.oldSuggestPrice:''}}</span>
                    </div>
                    <div class="mg">
                        <span>调整后销售价：{{calcRankRow ? calcRankRow.price : ""}}</span>
                    </div>
                    <div class="mg" v-if="row ? row.oldSuggestPrice : '' != 0">
                        <span style="margin-left: 10px;color: red;">同比增幅{{row ? (((calcRankRow.price-row.oldSuggestPrice)/row.oldSuggestPrice)*100).toFixed(2)  : ""}}%</span>
                    </div>
                    <div class="mg">
                        <span>销售价调整后：</span>
                    </div>
                    <div style="display:flex;height: 70px;">
                        <div class="item" >
                            <div>
                                价格排名下降
                            </div>
                            <div style="display: flex;align-items: center;">
                                <div class="itemColor">{{calcRankRow ? calcRankRow.rankInCurrent - row.warehouseRank : ''}}</div>
                                <div>位</div>
                            </div>
                        </div>
                        <div class="item" style="margin-left:10px;">
                            <div>
                                全仓单上架品种排名：
                            </div>
                            <div style="display: flex;align-items: center;">
                                <div>第</div>
                                <div class="itemColor">{{calcRankRow ? calcRankRow.rankInWhole : ''}}</div>
                            </div>
                        </div>
                    </div>
                    <div style="margin-top:10px;">
                        <span>全仓最低价</span>
                        <span class="redColor">{{calcRankRow ? calcRankRow.lowestPriceInWhole : ''}}</span>
                        <span>再降</span>
                        <span class="redColor">{{calcRankRow ? (calcRankRow.price - calcRankRow.lowestPriceInWhole).toFixed(2) : ""}}元</span>
                        <span>排名可到第一</span>
                        <span style="margin-left:10px;"><el-checkbox v-model="checked">改价到最低价</el-checkbox></span>
                    </div>
                </div>
            </xyy-panel>
            <span slot='footer' class='dialog-footer'>
                <el-button size='small' @click='cancel'>取 消</el-button>
                <el-button type='primary' size='small' @click='submit' :loading='submitLoading'>确 定</el-button>
            </span>
        </xyy-dialog>
    </div>
</template>

<script>
import { adjustPrice } from '@/api/product/addReduction'

export default {
  name: 'calcRankDiaglog',
  components: {
  },
  props: {
    calcRankRow:Object,
    row:Object,
    rowIndex:Number,
  },
  data() {
    return {
      submitLoading: false,
      checked:false,
    }
  },
  created() {
  },
  methods: {
    /**关闭 */
    cancel(){
        this.$refs.calcRankDiaglog.close();
    },
    /**关闭弹窗 */
    onClose() {
        this.$emit('onClose');
        this.row = {};
        this.calcRankRow = {};
        this.rowIndex = null;
        this.checked = false;
    },
    /**打开弹窗 */
    open(){
        this.$refs.calcRankDiaglog.open();
    },
    /**确认 */
    async submit(){
        let params = {
            barcode:this.row.barcode,
            price:this.checked?this.calcRankRow.lowestPriceInWhole:this.calcRankRow.price,
        }
        try{
            const res = await adjustPrice(params)
            if(res.code == 0){
                this.$message({
                    message: res.msg,
                    type: 'success'
                });
                this.$refs.calcRankDiaglog.close();
            }else{
                this.$message.error(res.msg)
            }
        }catch(err){
            
        }
    },
  }
}
</script>

<style scoped>
.item{
    flex: 1;
    display: flex;
    background-color: rgb(242, 242, 242);
    flex-flow: column;
    align-items: center;
    border-radius: 10px; /* 设置圆角半径 */
    padding: 10px;
}
.itemColor{
    color: rgb(71, 165, 234);
    font-size: 24px;
    margin: 0 5px;
}
.mg{
    margin: 5px 0;
}
.redColor{
    margin: 0 5px;
    color: red;
}
</style>
