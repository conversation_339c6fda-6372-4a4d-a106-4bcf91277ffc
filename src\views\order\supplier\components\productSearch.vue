<template>
    <div>
        <xyy-dialog key="dialog1" :footerShow="false" ref="productSearch" title="商品列表" width="70%" >
            <xyy-panel title="查询条件">
                <!-- 按钮组 start-->
                <btn-group slot="tools" :btn-list="btnListTop" />
                <el-form :model="formData" ref="formData" label-width="120px" class="clearfix">
                    <el-row>
                        <el-col :lg="12" :md="12">
                            <el-form-item label="商品信息" prop="productInfo">
                                <el-input v-model="formData.productInfo" placeholder="商品名称/ERP商品编码/商品编码/产品编码"
                                    clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :lg="12" :md="12">
                            <el-form-item label="供货仓库" prop="warehouse">
                                <el-input v-model="formData.warehouse" disabled></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </xyy-panel>
            <xyy-panel title="商品列表">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row  :data="tableData"
                    @cell-dblclick="reuturnInfo"
                    :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :width="item.width" :fixed="item.fixed">
                        </vxe-table-column>
                    </template>
                </vxe-table>
                <vxe-pager border :current-page="tablePage.pageNum" 
                :page-size="tablePage.pageSize"
                :total="tablePage.total" 
                :page-sizes="tablePage.pageSizes" 
                :layouts="[
                    'PrevPage',
                    'JumpNumber',
                    'NextPage',
                    'FullJump',
                    'Sizes',
                    'Total',
                ]" 
                @page-change="handlePageChange" />
            </xyy-panel>
            <span slot='footer' class='dialog-footer'>
            </span>
        </xyy-dialog>
    </div>
</template>

<script>
import { getGoodsInfo, getSupplyOrderProductInterceptCount } from '@/api/order/supplyGoods'
import { productDataColumns } from '../config'
import { pageConfig }  from "@/utils";
export default {
    name: 'productSearch',
    data() {
        return {
            btnListTop: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: this.searchList,
                    code: "",
                    plain: 'false',
                    icon: 'search-icon',
                    permission: 'supplier:productSearch:query'
                },
            ],
            formData: {
                productInfo: '',
                warehouse: '',
                orgCode: ''
            },
            tablePage: pageConfig(),
            tableColumns: productDataColumns(),
            tableData: [],
            loading: false,
            rowIndex: 0,
            scopedProduct: [],
        }
    },
    methods: {
        //获取限制商品列表
        apiGetSupplyOrderProductInterceptCount(){
            const params = {
                orgCode: this.formData.orgCode
            }
            getSupplyOrderProductInterceptCount(params).then(res => {
                const {code, msg, result} = res
                if (code === 0) {
                    this.scopedProduct = result
                }else {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                }
            })
        },
        // 获取列表
        getList() {
            this.loading = true
            const params = {
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                searchLikeCode: this.formData.productInfo,
            }
            getGoodsInfo(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.tableData = result.list || []
                    this.tablePage.total = result.total || 0
                    // this.tablePage.pageNum = result.pageNum || 1
                    this.loading = false
                } else {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                    this.loading = false
                }
            })
        },
        // 查询列表
        searchList() {
            this.tablePage.pageNum = 1;
            this.getList()
        },
        //切页方法
        handlePageChange({currentPage, pageSize}) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList()
        },
        //返回数据
        reuturnInfo({ row }) {
            if(this.scopedProduct.some(item => item === row.productCode)){
                this.$message({
                    type: 'warning',
                    message: '很抱歉，该商品当前不可下单，辛苦您选择其他商品。'
                })
                return
            }
            this.$emit('echoData', row, this.rowIndex)
            this.close()
        },
        // 打开弹窗
        open(rowIndex, wmsData) {
            this.$refs.productSearch.open()
            this.formData = {
                productInfo: '',
                warehouse: '',
                orgCode: '',
            }
            this.scopedProduct = []
            this.formData.warehouse = wmsData.orgName
            this.formData.orgCode = wmsData.orgCode
            this.rowIndex = rowIndex
            this.searchList()
            this.apiGetSupplyOrderProductInterceptCount()
        },
        //关闭弹窗
        close() {
            this.$refs.productSearch.close()
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper{
    height:230px !important;
}
</style>