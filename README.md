## 开发

```bash
# 克隆项目
git clone https://gitee.com/y_project/RuoYi-Vue

# 进入项目目录
cd ruoyi-ui

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

vxe想要自动撑开换行
show-overflow="break-word"

## 路由配置

###### 单页路由页面系统配置

组件路径配置单页component文件地址(除去'@/views/')

路由地址配置为单页名称（需满足大驼峰命名规则）

例：

菜单名称：新建供货单

组件路径：order/supplier/add

路由地址：OrderSupplierAdd

![](./src/assets/images/routerEx.png)

###### 单页路由缓存配置

单页若需缓存，则需在单页文件与其路由文件配置name，name取值需与路由地址保持一致

例：

name：'OrderSupplierAdd'

单页若无需缓存则不需要配置单页name

###### 单页路由跳转规范

router.push中其跳转路径遵循getRouters接口返回的path拼接原则：

父路由拼接子路由

例：

```js
            this.$router.push({
                path: '/order/OrderSupplierAdd',
            })
```