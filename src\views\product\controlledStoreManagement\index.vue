<template>
    <div class="app-container">
        <xyy-panel title='查询条件'>
            <btn-group slot="tools" :btn-list="btnListTop" />
            <el-form ref="formData" :model="formData" label-width="120px">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="商品编码" v-if="isShow">
                            <el-input v-model="formData.skuId" @input="skuIdInput" />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="药店ID" v-if="isShow">
                            <el-input v-model="formData.merchantId" @input="merchantIdInput" />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="控销组ID" v-if="isShow">
                            <el-input v-model="formData.groupId" @input="groupIdInput" />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="控销组名称" v-if="isShow">
                            <el-input v-model="formData.groupName" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel :titleShow='false'>
            <div slot="tools" style="float: right;">
                <el-button type="primary" @click="handleAdd"
                    v-hasPermi="['product:ControlledStoreManagement:handleAdd']">新建控销店铺组</el-button>
            </div>
            <div :key="tabKey" class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            <template slot-scope="scope">
                                <div v-if="item.field === 'operation'">
                                    <span class="clickWriting" style="margin-right: 12px;"
                                        @click="getDetail(scope.row)">详情</span>
                                    <span class="clickWriting" style="margin-right: 12px;"
                                        @click="editInfo(scope.row)">编辑</span>
                                    <span class="clickWriting" style="margin-right: 12px;"
                                        @click="checkChange(scope.row)">查看变更</span>
                                    <span class="clickWriting" style="color: red;"
                                        @click="handleDelete(scope.row)">删除</span>
                                </div>
                                <div v-else-if="item.field === 'merchantNums'">
                                    <span class="clickWriting" @click="getDetail(scope.row, 'controlStore')">{{
                                        scope.row[item.field] }}</span>
                                </div>
                                <div v-else>{{ scope.row[item.field] }}</div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
        <change-log ref="changeLog"></change-log>
        <add-group-alert ref="addGroupAlert" @add-group="addGroup"></add-group-alert>
    </div>
</template>

<script>
import { queryControlledStore, deleteControlledStore } from '@/api/product/controlledStoreManagement';
import addGroupAlert from './components/addGroupAlert.vue';
import changeLog from './components/changeLog.vue';
import { controlTabCol } from './config';
import { pageConfig } from '@/utils';
import XEUtils from 'xe-utils'

export default {
    name: 'ControlledStoreManagement',
    components: {
        changeLog,
        addGroupAlert
    },
    data() {
        return {
            btnListTop: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: this.searchHandler,
                    code: "",
                    plain: 'false',
                    permission: 'product:controlledStoreManagement:search'

                },
                {
                    label: "重置",
                    clickEvent: this.refresh,
                    code: "",
                    plain: 'false',
                    permission: 'product:controlledStoreManagement:refresh'
                },
                {
                    label: "收起",
                    clickEvent: () => { this.isShow = !this.isShow; this.tabKey++; },
                    showForm: true,
                    show: this.isShow,
                },
            ],
            formData: {
                skuId: '', // 商品编码
                merchantId: '', // 药店ID
                groupId: '', // 控销组ID
                groupName: '', // 控销组名称
            },
            tabKey: 0, // 表格key
            isShow: true, // 控销组展开收起
            loading: false, // 表格加载
            tableData: [], // 表格数据
            tableColumns: controlTabCol(), // 表格列配置
            tablePage: pageConfig(), // 表格分页配置
            tableKey: 0, // 表格key
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.searchHandler();
        })
    },
    activated() {
        this.$nextTick(() => {
            this.searchHandler();
        })
    },
    methods: {
        groupIdInput(value) {
            let newVal = value.trim();
            newVal = newVal.replace(/[^\d]/g, '');
            this.formData.groupId = newVal;
        },
        merchantIdInput(value) {
            let newVal = value.trim();
            newVal = newVal.replace(/[^\d]/g, '');
            this.formData.merchantId = newVal;
        },
        skuIdInput(value) {
            let newVal = value.trim();
            newVal = newVal.replace(/[^\d]/g, '');
            this.formData.skuId = newVal;
        },
        // 获取列表
        getList() {
            this.loading = true;
            const params = {
                ...this.formData,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
            }
            queryControlledStore(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    result.list?.forEach(item => {
                        item.createTimeStr = XEUtils.toDateString(item.createTime, 'yyyy-MM-dd HH:mm:ss')
                        item.updateTimeStr = XEUtils.toDateString(item.updateTime, 'yyyy-MM-dd HH:mm:ss')
                    })
                    this.tableData = result.list || []
                    this.tablePage.total = result.total
                } else {
                    this.$message.error(msg)
                }
            }).finally(() => {
                this.tableKey++
                this.loading = false;
            })
        },
        // 重置
        refresh() {
            this.formData = {
                skuId: '', // 商品编码
                merchantId: '', // 药店ID
                groupId: '', // 控销组ID
                groupName: '', // 控销组名称
            }
            this.searchHandler();
        },
        // 查询
        searchHandler() {
            this.tablePage.pageNum = 1
            this.getList();
        },
        // 切页
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList();
        },
        // 删除
        handleDelete(row) {
            this.$confirm('确认删除该控销店铺组吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const params = new URLSearchParams()
                params.append('groupId', row.id)
                deleteControlledStore(params).then(res => {
                    const { code, msg } = res
                    if (code === 0) {
                        this.$message.success(msg)
                        this.getList();
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => { });
        },
        // 查看变更
        checkChange(row) {
            this.$refs.changeLog.open(row);
        },
        // 编辑
        editInfo(row) {
            const editPage = this.$store.state.tagsView.visitedViews.find(
                (item) => item.name === "ControlGroupEdit"
            );
            if (editPage) {
                this.$store.state.tagsView.visitedViews =
                    this.$store.state.tagsView.visitedViews.filter(
                        (item) => item.name !== "ControlGroupEdit"
                    );
            }
            this.$nextTick(() => {
                this.$router.push({
                    path: '/product/controlGroupEdit',
                    query: {
                        type: 'edit',
                        groupName: row.name,
                        groupId: row.id
                    }
                })
            })
        },
        // 详情
        getDetail(row, activeName) {
            this.$router.push({
                path: '/product/controlGroupDetail',
                query: {
                    activeName,
                    groupId: row.id,
                    groupName: row.name,
                },
            })
        },
        // 新建控销店铺管理
        handleAdd() {
            this.$refs.addGroupAlert.open();
        },
        // 新增控销组
        addGroup(data) {
            const editPage = this.$store.state.tagsView.visitedViews.find(
                (item) => item.name === "ControlGroupEdit"
            );
            if (editPage) {
                this.$store.state.tagsView.visitedViews =
                    this.$store.state.tagsView.visitedViews.filter(
                        (item) => item.name !== "ControlGroupEdit"
                    );
            }
            this.$nextTick(() => {
                this.$router.push({
                    path: '/product/controlGroupEdit',
                    query: {
                        type: 'add',
                        groupName: data.name,
                        groupId: data.id
                    }
                })
            })
        }
    },
}
</script>

<style lang="scss" scoped></style>