import Layout from '@/layout';

export default [
    {
        path: '/tenant',
        component: Layout,
        meta: { title: '租户管理', icon: 'user',},
        alwaysShow: true,
        children: [
            {
                path: 'tenantManagement',
                component: () => import('@/views/tenant/tenantManagement/index.vue'),
                name: 'tenantManagement',
                meta: { title: '租户管理',icon: 'user', activeMenu: '/tenant/tenantManagement' }
            },
        ]
    }
]