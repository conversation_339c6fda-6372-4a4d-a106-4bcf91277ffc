<template>
    <div class="responsive-iframe-container" ref="container">
        <iframe
            v-if="SignUrl"
            :src="SignUrl"
            ref="iframe"
            :style="iframeStyles"
            frameborder="0"
            scrolling="auto"
            allowfullscreen
            @load="onIframeLoad"
        ></iframe>
        <div v-else class="loading">
            <i class="el-icon-loading loading-icon"></i>
            <span>加载中...</span>
        </div>
        <!-- 右键菜单 -->
        <div
            v-show="showContextMenu"
            class="context-menu"
            :style="contextMenuStyle"
            @click.stop
        >
            <div class="menu-item" @click="copyUrl">
                <i class="el-icon-document-copy"></i>
                <span>复制地址</span>
            </div>
            <div class="menu-item" @click="refreshIframe">
                <i class="el-icon-refresh"></i>
                <span>刷新页面</span>
            </div>
        </div>
    </div>
</template>

<script>
import { getSignUrl } from '@/api/contract/platformContracts'

export default {
    name: 'SignContract',
    data() {
        return {
            SignUrl: '',
            iframeWidth: 0,
            iframeHeight: 0,
            showContextMenu: false,
            contextMenuStyle: {
                left: '0px',
                top: '0px'
            },
            resizeObserver: null,
            isLoading: true
        }
    },
    computed: {
        iframeStyles() {
            return {
                width: `${this.iframeWidth}px`,
                height: `${this.iframeHeight}px`,
                border: 'none',
                display: 'block',
                backgroundColor: '#fff'
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initializeComponent()
        })
    },
    beforeDestroy() {
        this.cleanup()
    },
    methods: {
        async initializeComponent() {
            try {
                // 计算初始尺寸
                this.calculateIframeDimensions()

                // 设置事件监听器
                this.setupEventListeners()

                // 获取签署URL
                await this.getSignContract()

                // 设置ResizeObserver监听容器尺寸变化
                this.setupResizeObserver()
            } catch (error) {
                console.error('初始化组件失败:', error)
                this.$message.error('页面初始化失败，请刷新重试')
            }
        },

        calculateIframeDimensions() {
            // 获取视口尺寸
            const viewportWidth = window.innerWidth
            const viewportHeight = window.innerHeight

            // 获取侧边栏状态
            const sidebar = this.$store.state.app.sidebar
            const sidebarWidth = sidebar.hide ? 0 : (sidebar.opened ? 200 : 54)

            // 获取是否有TagsView
            const hasTagsView = this.$store.state.settings.tagsView
            const headerHeight = hasTagsView ? 84 : 50

            // 优化后的空间计算：减少重复预留，提高空间利用率
            let horizontalMargin, totalVerticalReserve

            // 根据分辨率计算合理的空间预留
            if (viewportHeight <= 600) {
                // 超低分辨率：最小预留
                horizontalMargin = 16
                totalVerticalReserve = headerHeight + 80 // 导航栏 + 80px缓冲
            } else if (viewportHeight <= 768) {
                // 低分辨率：适中预留
                horizontalMargin = viewportWidth <= 1366 ? 20 : 24
                totalVerticalReserve = headerHeight + 70 // 导航栏 + 70px缓冲
            } else if (viewportHeight <= 900) {
                // 中等分辨率：标准预留
                horizontalMargin = 28
                totalVerticalReserve = headerHeight + 60 // 导航栏 + 60px缓冲
            } else if (viewportHeight <= 1080) {
                // 常见高分辨率：优化空间利用
                horizontalMargin = 32
                totalVerticalReserve = headerHeight + 50 // 导航栏 + 50px缓冲
            } else {
                // 超高分辨率：适当预留
                horizontalMargin = 40
                totalVerticalReserve = headerHeight + 60 // 导航栏 + 60px缓冲
            }

            // 计算可用空间（统一的计算方式，避免重复预留）
            const availableWidth = viewportWidth - sidebarWidth - horizontalMargin
            const availableHeight = viewportHeight - totalVerticalReserve

            // 动态最小尺寸限制
            const minWidth = Math.min(600, availableWidth * 0.8)
            const minHeight = Math.min(400, availableHeight * 0.6)

            this.iframeWidth = Math.max(availableWidth, minWidth)
            this.iframeHeight = Math.max(availableHeight, minHeight)

            // 针对不同分辨率进行精细优化
            this.optimizeForResolution(viewportWidth, viewportHeight)
        },

        optimizeForResolution(width, height) {
            // 精细化分辨率优化：减少过度预留，提高空间利用率

            // 超低分辨率设备（≤600px）：保守处理
            if (height <= 600) {
                this.iframeWidth = Math.min(this.iframeWidth, width - 80)
                // 已在calculateIframeDimensions中预留了足够空间，这里不再额外减少
            }
            // 低分辨率设备（≤768px）：精确优化
            else if (height <= 768) {
                if (width <= 1280) {
                    // 1280x768, 1280x800等：适度限制宽度
                    this.iframeWidth = Math.min(this.iframeWidth, 1150)
                } else if (width <= 1366) {
                    // 1366x768等：标准限制
                    this.iframeWidth = Math.min(this.iframeWidth, 1250)
                } else {
                    // 宽屏低分辨率：充分利用宽度
                    this.iframeWidth = Math.min(this.iframeWidth, 1350)
                }
                // 高度已在calculateIframeDimensions中合理计算，不再额外限制
            }
            // 中等分辨率（769-900px）：平衡优化
            else if (height <= 900) {
                this.iframeWidth = Math.min(this.iframeWidth, 1450)
                // 可以适当增加高度利用率
                this.iframeHeight = Math.min(this.iframeHeight, height - 80)
            }
            // 常见高分辨率（901-1080px）：充分利用空间
            else if (height <= 1080) {
                if (width <= 1920) {
                    // 1920x1080等：优化空间利用，减少底部空白
                    this.iframeWidth = Math.min(this.iframeWidth, 1650)
                    this.iframeHeight = Math.min(this.iframeHeight, height - 70) // 减少预留空间
                } else {
                    // 超宽屏：充分利用
                    this.iframeWidth = Math.min(this.iframeWidth, 1800)
                    this.iframeHeight = Math.min(this.iframeHeight, height - 70)
                }
            }
            // 高分辨率（1081-1440px）：标准优化
            else if (height <= 1440) {
                this.iframeWidth = Math.min(this.iframeWidth, 2000)
                this.iframeHeight = Math.min(this.iframeHeight, height - 80)
            }
            // 超高分辨率（>1440px）：适当限制
            else {
                this.iframeWidth = Math.min(this.iframeWidth, 2200)
                this.iframeHeight = Math.min(this.iframeHeight, 1300)
            }

            // 最终安全检查：确保不超出可视区域（减少安全边距）
            const hasTagsView = this.$store.state.settings.tagsView
            const headerHeight = hasTagsView ? 84 : 50
            const minSafeReserve = headerHeight + 30 // 减少安全预留从150px到30px
            const maxSafeHeight = height - minSafeReserve

            this.iframeHeight = Math.min(this.iframeHeight, maxSafeHeight)

            // 调试信息：帮助分析空间利用情况
            if (process.env.NODE_ENV === 'development') {
                const spaceUtilization = ((this.iframeHeight / height) * 100).toFixed(1)
                console.log(`分辨率: ${width}x${height}, iframe: ${this.iframeWidth}x${this.iframeHeight}, 空间利用率: ${spaceUtilization}%`)
            }
        },

        setupEventListeners() {
            // 窗口大小变化监听
            window.addEventListener('resize', this.handleWindowResize)

            // 右键菜单监听
            document.addEventListener('contextmenu', this.handleContextMenu)
            document.addEventListener('click', this.hideContextMenu)

            // 键盘事件监听
            document.addEventListener('keydown', this.handleKeydown)
        },

        setupResizeObserver() {
            if (window.ResizeObserver && this.$refs.container) {
                this.resizeObserver = new ResizeObserver(entries => {
                    for (let entry of entries) {
                        this.handleContainerResize(entry)
                    }
                })
                this.resizeObserver.observe(this.$refs.container)
            }
        },

        handleWindowResize() {
            // 防抖处理
            clearTimeout(this.resizeTimer)
            this.resizeTimer = setTimeout(() => {
                this.calculateIframeDimensions()
                // 窗口大小变化时也要确保导航栏可见
                this.ensureNavigationVisibility()
                // 如果iframe已经加载，更新其样式
                if (this.$refs.iframe && !this.isLoading) {
                    const iframe = this.$refs.iframe
                    iframe.style.width = `${this.iframeWidth}px`
                    iframe.style.height = `${this.iframeHeight}px`
                    iframe.style.maxHeight = `${this.iframeHeight}px`
                    iframe.style.maxWidth = `${this.iframeWidth}px`
                }
            }, 250)
        },

        handleContainerResize(entry) {
            const { width, height } = entry.contentRect
            if (width > 0 && height > 0) {
                // 容器尺寸变化时重新计算iframe尺寸
                this.calculateIframeDimensions()
            }
        },

        handleContextMenu(event) {
            // 检查是否在iframe容器内右键
            if (this.$refs.container && this.$refs.container.contains(event.target)) {
                event.preventDefault()
                this.showContextMenu = true
                this.contextMenuStyle = {
                    left: `${event.clientX}px`,
                    top: `${event.clientY}px`
                }
            }
        },

        hideContextMenu() {
            this.showContextMenu = false
        },

        handleKeydown(event) {
            // ESC键隐藏右键菜单
            if (event.key === 'Escape') {
                this.hideContextMenu()
            }
            // F5刷新iframe
            if (event.key === 'F5' && this.$refs.iframe) {
                event.preventDefault()
                this.refreshIframe()
            }
        },

        onIframeLoad() {
            this.isLoading = false

            // 确保iframe尺寸固定，防止内容变化影响布局
            if (this.$refs.iframe) {
                const iframe = this.$refs.iframe

                // 低分辨率设备的额外安全检查
                this.ensureNavigationVisibility()

                // 设置固定尺寸属性，防止内容撑开
                iframe.style.width = `${this.iframeWidth}px`
                iframe.style.height = `${this.iframeHeight}px`
                iframe.style.overflow = 'auto'
                iframe.style.border = 'none'
                iframe.style.maxHeight = `${this.iframeHeight}px` // 强制最大高度限制
                iframe.style.maxWidth = `${this.iframeWidth}px` // 强制最大宽度限制

                // 添加加载完成的样式类
                iframe.classList.add('iframe-loaded')
            }

            this.$emit('iframe-loaded')
        },

        // 确保导航栏在所有分辨率下始终可见（优化版本）
        ensureNavigationVisibility() {
            const viewportHeight = window.innerHeight
            const hasTagsView = this.$store.state.settings.tagsView
            const headerHeight = hasTagsView ? 84 : 50

            // 根据分辨率设置合理的最小预留空间
            let minRequiredSpace
            if (viewportHeight <= 768) {
                minRequiredSpace = headerHeight + 60 // 低分辨率：适中缓冲
            } else if (viewportHeight <= 1080) {
                minRequiredSpace = headerHeight + 40 // 常见分辨率：减少缓冲
            } else {
                minRequiredSpace = headerHeight + 50 // 高分辨率：标准缓冲
            }

            // 检查并调整iframe高度
            if (this.iframeHeight > viewportHeight - minRequiredSpace) {
                const newHeight = Math.max(viewportHeight - minRequiredSpace, 300)
                if (newHeight !== this.iframeHeight) {
                    this.iframeHeight = newHeight
                    if (process.env.NODE_ENV === 'development') {
                        console.log(`导航栏可见性检查：调整iframe高度为 ${this.iframeHeight}px (分辨率: ${window.innerWidth}x${viewportHeight})`)
                    }
                }
            }
        },

        async getSignContract() {
            try {
                this.isLoading = true
                const response = await getSignUrl({
                    baseId: this.$route.query.baseId
                })
                this.SignUrl = response.data
            } catch (error) {
                console.error('获取签署URL失败:', error)
                this.$message.error('获取签署链接失败，请重试')
            } finally {
                this.isLoading = false
            }
        },

        copyUrl() {
            if (this.SignUrl) {
                navigator.clipboard.writeText(this.SignUrl).then(() => {
                    this.$message.success('地址已复制到剪贴板')
                }).catch(() => {
                    // 降级方案
                    const textArea = document.createElement('textarea')
                    textArea.value = this.SignUrl
                    document.body.appendChild(textArea)
                    textArea.select()
                    document.execCommand('copy')
                    document.body.removeChild(textArea)
                    this.$message.success('地址已复制到剪贴板')
                })
            }
            this.hideContextMenu()
        },

        refreshIframe() {
            if (this.$refs.iframe) {
                this.isLoading = true
                this.$refs.iframe.src = this.$refs.iframe.src
            }
            this.hideContextMenu()
        },

        cleanup() {
            // 清理事件监听器
            window.removeEventListener('resize', this.handleWindowResize)
            document.removeEventListener('contextmenu', this.handleContextMenu)
            document.removeEventListener('click', this.hideContextMenu)
            document.removeEventListener('keydown', this.handleKeydown)

            // 清理ResizeObserver
            if (this.resizeObserver) {
                this.resizeObserver.disconnect()
                this.resizeObserver = null
            }

            // 清理定时器
            if (this.resizeTimer) {
                clearTimeout(this.resizeTimer)
            }
        }
    }
}
</script>

<style scoped lang="scss">
.responsive-iframe-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 12px; // 减少默认padding从20px到12px，提高空间利用率
    background-color: #f5f5f5;
    overflow: auto;

    // 确保容器不会被iframe内容撑开
    min-height: 0;
    min-width: 0;
}

iframe {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
    transition: box-shadow 0.3s ease;

    // 关键：防止iframe内容影响尺寸
    flex-shrink: 0;
    flex-grow: 0;

    &.iframe-loaded {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    }

    &:hover {
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }
}

.loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 400px;
    font-size: 16px;
    color: #909399;
    gap: 12px;

    .loading-icon {
        font-size: 24px;
        animation: rotating 2s linear infinite;
    }
}

@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.context-menu {
    position: fixed;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    min-width: 120px;
    padding: 4px 0;

    .menu-item {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        cursor: pointer;
        font-size: 14px;
        color: #606266;
        gap: 8px;

        &:hover {
            background-color: #f5f7fa;
            color: #409eff;
        }

        i {
            font-size: 16px;
        }
    }
}

// 响应式设计 - 优化空间利用率，减少不必要的padding
@media screen and (max-height: 600px) {
    .responsive-iframe-container {
        padding: 4px;
        align-items: flex-start;
    }
}

@media screen and (max-height: 768px) {
    .responsive-iframe-container {
        padding: 6px;
        align-items: flex-start;
    }
}

@media screen and (max-height: 900px) {
    .responsive-iframe-container {
        padding: 8px;
    }
}

@media screen and (min-height: 901px) and (max-height: 1080px) {
    .responsive-iframe-container {
        padding: 10px; // 1920x1080等常见分辨率减少padding
    }
}

@media screen and (min-height: 1081px) and (max-height: 1440px) {
    .responsive-iframe-container {
        padding: 15px; // 2K分辨率适中padding
    }
}

@media screen and (min-height: 1441px) {
    .responsive-iframe-container {
        padding: 25px; // 4K等超高分辨率适当padding
    }
}

// 确保在不同主题下的兼容性
.app-wrapper.hideSidebar .responsive-iframe-container,
.app-wrapper.sidebarHide .responsive-iframe-container {
    // 侧边栏隐藏时的样式调整
    padding-left: 30px;
}
</style>