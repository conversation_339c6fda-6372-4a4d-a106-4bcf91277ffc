import request from '@/utils/request'

// 查询上下架列表
export function queryList(data) {
  return request({
    url: '/product/shelf/query',
    method: 'post',
    data: data
  })
}
// 查询平台加价规则
export function basicLoop(data) {
  return request({
    url: '/product/basicLoop',
    method: 'post',
    data: data
  })
}
// 查询加点系数
export function getSpecialRate(data) {
  return request({
    url: '/promotion/special/getSpecialConfig',
    method: 'post',
    data: data
  })
}
// 计算商品环线价
export function calcLoopPrice(data) {
  return request({
    url: '/product/calcLoopPrice',
    method: 'post',
    data: data
  })
}
// 计算特价商品环线价
export function calcLoopPriceSpecial(data) {
  return request({
    url: 'promotion/special/calcLoopPrice',
    method: 'post',
    data: data
  })
}
// 关闭控销
export function closeProductUniform(data) {
  return request({
    url: '/product/shelf/closeProductUniform',
    method: 'post',
    data: data
  })
}
// 下架
export function unShelf(data) {
  return request({
    url: '/product/shelf/unShelf',
    method: 'post',
    data: data
  })
}
// 上架
export function putaway(data) {
  return request({
    url: '/product/shelf/putaway',
    method: 'post',
    data: data
  })
}
// 获取页面展示数量
export function getStatic(data) {
  return request({
    url: '/product/static',
    method: 'post',
    data: data
  })
}
// 修改本公司编码
export function updateErpProductCode(data) {
  return request({
    url: '/product/shelf/updateErpProductCode',
    method: 'post',
    data: data
  })
}
// 修改销售价
export function adjustPrice(data) {
  return request({
    url: '/product/adjustPrice',
    method: 'post',
    data: data
  })
}
// 计算商品排名
export function calcRank(data) {
  return request({
    url: '/product/calcRank',
    method: 'post',
    data: data
  })
}
// 发布-发布商品
export function publish(data) {
  return request({
    url: '/product/publish/publish',
    method: 'post',
    data: data
  })
}
// 发布-商品列表
export function publishList(data) {
  return request({
    url: '/product/publish/list',
    method: 'get',
    params: data
  })
}
// 获取供货对象
export function getUserTypeList(data) {
  return request({
    url: '/product/getUserTypeList',
    method: 'post',
    data: data
  })
}

// 查询限购
export function getPurchaseLimit(data) {
  return request({
    url: '/product/getPurchaseLimit',
    method: 'post',
    data: data
  })
}

// 保存限购
export function savePurchaseLimit(data) {
  return request({
    url: '/product/savePurchaseLimit',
    method: 'post',
    data: data
  })
}

//计算商品环线价
export function calcLoopPricePromotion(data) {
  return request({
    url: '/promotion/pintuan/calcLoopPrice',
    method: 'post',
    data: data
  })
}

//是否有执行中的配置项
export function checkAccess(data) {
  return request({
    url: '/promotion/pintuan/isProgressRunning',
    method: 'post',
    data: data
  })
}

// 查询拼团价格系数
export function getPintuanRate(data) {
  return request({
    url: '/promotion/pintuan/getActivityRate',
    method: 'post',
    data: data
  })
}

//打开自动创建
export function openAutoCreate(data) {
  return request({
    url: '/promotion/pintuan/switchOn',
    method: 'post',
    data: data
  })
}

//仅关闭自动创建
export function closeAutoCreate(data) {
  return request({
    url: '/promotion/pintuan/switchOffOnly',
    method: 'post',
    data: data
  })
}

//关闭且删除自动创建的商品
export function closeAndDelete(data) {
  return request({
    url: '/promotion/pintuan/switchOffWithDelete',
    method: 'post',
    data: data
  })
}

//设置拼团价格系数
export function setPintuanRate(data) {
  return request({
    url: '/promotion/pintuan/setActivityRate',
    method: 'post',
    data: data
  })
}
