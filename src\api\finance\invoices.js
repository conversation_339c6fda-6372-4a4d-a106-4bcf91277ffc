import request from '@/utils/request'

//结算单主表查询
export function getInvoicesList(params) {
  return request({
    url: '/finance/settle/list',
    method: 'post',
    data: params
  })
}

//结算单-明细查询
export function getInvoicesDetailList(params) {
  return request({
    url: '/finance/settle/detailList',
    method: 'post',
    data: params
  })
}

//结算单-获取总销售金额（含税）
export function getTotalSaleAmount(params) {
  return request({
    url: '/finance/settle/getSumBasePay',
    method: 'post',
    data: params
  })
}

// 结算单明细-tab查询
export function getInvoicesDetailTabList(params) {
  return request({
    url: '/finance/settle/detailList',
    method: 'post',
    data: params
  })
}