import { Message } from 'element-ui';
const axios = require('axios');
/**
 * 前端打印类
 * @param {*} options 打印参数
 * title: '页面title' String
 * style: '打印样式' String
 * content: printTmpl 打印模板 可直接传入字符串或者Dom或者refs节点对象
 */
const Print = function({ title, style, content }) {
  if (!(this instanceof Print)) return new Print({ title, style, content })
  if ((typeof content) === 'string') {
    //   如果是字符串不用处理
    // this.dom = document.querySelector(dom)
  } else {
    // this.isDOM(dom)
    content = this.isDOM(content) ? content.outerHTML : content.$el.outerHTML
  }

  this.options = this.extend({
    'noPrint': '.no-print'
  }, { title, style, content })

  this.init()
}
const printNew = function(postData){
  //postData = "[{\"billCode\":\"1716883332764\",\"dsDetail\":[{\"pickingPath\":0,\"picture\":\"https://files.test.ybm100.com/B2B/WMS/xyy-wms-basicdata-man/drug/2024/05/13/5483aade-cdfb-4761-ac7f-cd4e6da74e2f.jpg\",\"quality\":\"合格\"}],\"id\":5702186240123510784,\"isRePrintFlag\":\"\",\"mode\":\"PREVIEW\",\"order\":0,\"orgName\":\"武汉仓\",\"printTime\":\"2024-05-28 16:02:15\",\"printUser\":\"徐震\",\"rePrintTimes\":\"当前第1打印\",\"secondOrder\":0,\"template\":\"DRUG_TEST\",\"version\":\"2405281526\"}]"
  // 发送POST请求
  axios.post('http://127.0.0.1:28886', postData)
  .then(function (response) {
      // 请求成功，处理响应数据
      console.log('Response data:', response.data);
      Message({
        message: '打印成功',
        type: 'success',
      })
  })
  .catch(function (error) {
      // 请求失败，处理错误
      console.error('Error:', error);
      Message({
        message: error,
        type: 'error',
      })
  });
}
Print.prototype = {
  init: function() {
    const content = this.getContent()
    this.writeIframe(content)
  },
  getContent() {
    const title = `<title>${this.options.title}</title>`
    const style = `<style>${this.options.style}</style>`
    return title + style + this.options.content
  },
  extend: function(obj, obj2) {
    for (var k in obj2) {
      obj[k] = obj2[k]
    }
    return obj
  },

  getStyle: function() {
    var str = ''
    var styles = document.querySelectorAll('style,link')
    for (var i = 0; i < styles.length; i++) {
      str += styles[i].outerHTML
    }
    str += '<style>' + (this.options.noPrint ? this.options.noPrint : '.no-print') + '{display:none;}</style>'

    return str
  },

  getHtml: function() {
    var inputs = document.querySelectorAll('input')
    var textareas = document.querySelectorAll('textarea')
    var selects = document.querySelectorAll('select')

    for (var k = 0; k < inputs.length; k++) {
      if (inputs[k].type === 'checkbox' || inputs[k].type === 'radio') {
        if (inputs[k].checked === true) {
          inputs[k].setAttribute('checked', 'checked')
        } else {
          inputs[k].removeAttribute('checked')
        }
      } else if (inputs[k].type === 'text') {
        inputs[k].setAttribute('value', inputs[k].value)
      } else {
        inputs[k].setAttribute('value', inputs[k].value)
      }
    }

    for (var k2 = 0; k2 < textareas.length; k2++) {
      if (textareas[k2].type === 'textarea') {
        textareas[k2].innerHTML = textareas[k2].value
      }
    }

    for (var k3 = 0; k3 < selects.length; k3++) {
      if (selects[k3].type === 'select-one') {
        var child = selects[k3].children
        for (var i in child) {
          if (child[i].tagName === 'OPTION') {
            if (child[i].selected === true) {
              child[i].setAttribute('selected', 'selected')
            } else {
              child[i].removeAttribute('selected')
            }
          }
        }
      }
    }

    return this.dom.outerHTML
  },

  writeIframe: function(content) {
    var w; var doc; var iframe = document.createElement('iframe')
    var f = document.body.appendChild(iframe)
    iframe.id = 'myIframe'
    // iframe.style = "position:absolute;width:0;height:0;top:-10px;left:-10px;";
    iframe.setAttribute('style', 'position:absolute;width:0;height:0;top:-10px;left:-10px;')
    w = f.contentWindow || f.contentDocument
    doc = f.contentDocument || f.contentWindow.document
    doc.open()
    doc.write(content)
    doc.close()
    var _this = this
    iframe.onload = function() {
      _this.toPrint(w)
      setTimeout(function() {
        document.body.removeChild(iframe)
      }, 100)
    }
  },

  toPrint: function(frameWindow) {
    try {
      setTimeout(function() {
        frameWindow.focus()
        try {
          if (!frameWindow.document.execCommand('print', false, null)) {
            frameWindow.print()
          }
        } catch (e) {
          frameWindow.print()
        }
        frameWindow.close()
      }, 10)
    } catch (err) {
      console.log('err', err)
    }
  },
  isDOM: (typeof HTMLElement === 'object')
    ? function(obj) {
      return obj instanceof HTMLElement
    } : function(obj) {
      return obj && typeof obj === 'object' && obj.nodeType === 1 && typeof obj.nodeName === 'string'
    }
}

const printNewAsnyc = async function(postData){
  try {
  // 发送 POST 请求
  const response = await axios.post('http://127.0.0.1:28886', postData);
  // 请求成功，处理响应数据
  console.log('Response data:', response.data);
  if(response.data.code === 0){
    Message({
      message: response.data.msg,
      type: 'success',
      });
  }else {
    Message({
      message: response.data.msg,
      type: 'error',
    })
  }
  } catch (error) {
  // 请求失败，处理错误
  console.error('Error:', error);
  const errorMsg = error.message || '打印失败';
  throw errorMsg; // 抛出错误以便上层捕获
  }
}

export {
  printNew,
  Print,
  printNewAsnyc
}
