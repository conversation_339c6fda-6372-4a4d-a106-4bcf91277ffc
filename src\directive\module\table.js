/**
 *
 * @param {*} el
 */
function calculatTableHeight(el) {
  let pagerHeight = 0
  const pagerEl = el.nextElementSibling
  if (pagerEl && pagerEl.className === 'pager') {
    pagerHeight = el.nextElementSibling.offsetHeight
  }
  const height = window.innerHeight - el.getBoundingClientRect().top - 18 - pagerHeight
  return height
}

export default {
  inserted: function(el) {
    console.log(el);
    
    el.style.height = calculatTableHeight(el) + 'px'
  },
  update: function(el) {
    el.style.height = calculatTableHeight(el) + 'px'
  }
}
