<template>
  <div>
      <xyy-dialog title='查看环线价' ref="platformMarkupRulesDialog" width='60%' :footerShow="false" @on-close="onClose">
        <xyy-panel :titleShow="false">
            <div  class="table-box">
                <vxe-table ref="xTable"
                    :loading="loading"
                    highlight-current-row
                    height="400px"
                    :data="tableConfig.data"
                    >
                    <template>
                    <vxe-table-column v-for="item in tableConfig.col" :key="item.field" :field="item.field" :title="item.title" :width="item.width" :fixed="item.fixed">
                        <template #header>
                        <div v-if="item.hit">
                            <el-tooltip placement="top">
                            <template slot="content">
                                <div v-for="(it,index) in item.hit" :key="index">
                                <div>{{it}}</div>
                                </div>
                            </template>
                            <div>
                                <span>{{item.title}}</span>
                                <span>
                                <svg-icon  icon-class="prompt-icon"/>
                                </span>
                            </div>
                            </el-tooltip>
                        </div>
                        <div v-else>
                            {{item.title}}
                        </div>
                        </template>
                        <template v-slot:default="slotProps">
                        <!-- 本公司商品编码 -->
                        <div v-if="item.field === 'containerCode'">
                            <el-input v-model="slotProps.row[item.field]" class="custom-input">
                            <svg-icon slot="suffix" icon-class="edit-table-icon" style="cursor: pointer;" @click="containerCodeEdit(slotProps.row)"></svg-icon>
                            </el-input>
                        </div>
                        <!--操作按钮-->
                        <div v-else-if="item.field === 'options'">
                            <div>
                            <a href="#" class="clickWriting" v-if="activeName == 'shelvedProducts'" @click="pendingChange(slotProps.row,1)">下架</a>
                            <a href="#" class="clickWriting" v-if="activeName == 'pendingShelving'" @click="shelvedChange(slotProps.row,1)">上架</a>
                            <a href="#" class="clickWriting" style="margin-left:10px;" @click="editbusinessManagement(slotProps.row)">控销商圈设置</a>
                            </div>
                        </div>
                        <div v-else>
                            {{ slotProps.row[item.field] }}
                        </div>
                        </template>
                    </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
        </xyy-panel>
      </xyy-dialog>
  </div>
</template>

<script>
import {
  calcLoopPriceSpecial,calcLoopPrice

} from '@/api/marketing/specialActive'

export default {
name: 'viewRingLinePrice',
components: {
},
props: {
  viewRingLinePriceRow:Object
},
data() {
  return {
    loading: false,
    tableConfig: {
      data: [],
      col: [
        {
          title: '省',
          field: 'areaName',
        },
        {
          title: '公开价',
          field: 'origin',
        },
        {
          title: '单体客户环线后价格',
          field: 'singleRatio'
        }, {
          title: '连锁总店环线后价格',
          field: 'chainRatio'
        },
      ]
    },
  }
},
created() {
},
methods: {
  /**关闭弹窗 */
  onClose() {
      this.$refs.platformMarkupRulesDialog.close()
      this.$emit('on-close')
  },
  /**打开弹窗 */
  open(val){
      this.$refs.platformMarkupRulesDialog.open();
      this.$nextTick(()=>{
          this.queryList(val)
      })
  },
  handleFormSubmit() {
    this.queryList()
  },
  async queryList(flag) {
    this.loading = true
    let params = {
      barcode:this.viewRingLinePriceRow.barcode,
      price:this.viewRingLinePriceRow.activityPrice
    }
    let params1 = {
      barcode:this.viewRingLinePriceRow.barcode,
      price:this.viewRingLinePriceRow.suggestPrice
    }
    try {
      const res = flag === 'false'? await calcLoopPrice([params1]): await calcLoopPriceSpecial([params])
      if (res.code == 0) {
        this.tableConfig.data = res.result[0].items
      } else {
        this.tableConfig.data = []
        this.$message.error(res.msg || '获取列表失败')
      }
    } catch (e) {
      console.log(e)
    }
    this.loading = false
  },
}
}
</script>

<style scoped>
::v-deep .vxe-table--body-wrapper{
    height:500px !important;
}

</style>
