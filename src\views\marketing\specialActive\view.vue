<template>
  <div class="app-container">
    <xyy-panel :titleShow="false">
       <!-- 按钮组 start-->
       <btn-group slot="tools" :btn-list="btnListTop"/>
       <el-form ref="formData" :model="formData" label-width="120px" class="clearfix" :rules="rules">
        <el-row :gutter="20">
          <el-col :lg="8" :md="8">
            <el-form-item label="活动开始时间" prop="startTime">
              <el-date-picker
                          disabled
                        style="width: 100%;"
                          v-model="formData.startTime"
                          :picker-options="pickerOptions"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          type="datetime"
                          placeholder="选择日期时间">
                        </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="8">
            <el-form-item label="活动结束时间" prop="endTime">
              <el-date-picker
               style="width: 100%;"
                disabled
                          v-model="formData.endTime"
                          :picker-options="pickerOptions"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          type="datetime"
                          placeholder="选择日期时间">
                        </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :lg="16" :md="16">
            <el-form-item label="活动名称" prop="title">
              <el-input v-model.trim="formData.title" placeholder="非必填项，50字以内" maxlength="50" show-word-limit  disabled/>
            </el-form-item>
          </el-col>

        </el-row>
       </el-form>
    </xyy-panel>
    <xyy-panel :titleShow="false">
      <div style="text-align: right;">
        <div class="searchProduct">
        <el-checkbox v-model="isUnvalid" style="margin-right: 10px;" @change="handelSearch">已失效</el-checkbox>
        <el-input v-model="searchProduct" placeholder="查询已添加商品" style="width:250px;"/>
        <el-button type="primary" @click="handelSearch" style="margin-left: 10px;">查询</el-button>
        <el-button  @click="handelReset">重置</el-button>
      </div>
      </div>

      <!-- <div class="redTips"><img src="@/assets/images/tips.svg" alt="" style="width: 16px;height: 16px; margin: 12px">修改特价时请仔细核对价格，如果出现价格改错且销售出去的订单，一律由商家自己承担损失！</div> -->
      <div class="table-box">
              <vxe-table ref="xTable" :loading="loading" highlight-current-row height="400" :data="tableData.filter(item => item.isShow)"
                  :key="tableKey"
                  :row-style="rowStyle"
                   empty-text=""
                  @checkbox-all="selectAllEvent"
                  @cell-click="cellClickEvent"
                  @checkbox-change="selectChangeEvent">
                  <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field" :title="item.title" :width="item.width">
                    <template #header>
                      <div v-if="item.hit && checkAccess">
                        <el-tooltip placement="top">
                          <template slot="content">
                            <div v-for="(it,index) in item.hit" :key="index">
                              <div>{{it}}</div>
                            </div>
                          </template>
                          <div>
                            <span>{{item.title}}</span>
                            <span>
                              <svg-icon  icon-class="prompt-icon"/>
                            </span>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        {{item.title}}
                      </div>
                    </template>
                      <template v-slot="slotProps">

                          <div v-if="item.field === 'hx'">
                            <span class="clickWriting" style="margin-right: 10px;" @click.stop="viewRing(slotProps.row)">查看环线价</span>
                          </div>
                          <div v-else-if="item.field === 'productName'" id="productName">
                            <div style="text-align: left;">
                              <div>商品名称:<span>{{slotProps.row.productName}}</span></div>
                              <div>规格:<span >{{slotProps.row.spec}}</span></div>
                              <div>生产厂家:<span >{{slotProps.row.manufacturer}}</span></div>
                            </div>
                          </div>
                          <div v-else-if="item.field === 'url'"  id="imgLook">
                            <img :src="slotProps.row.minImgList[0]" alt="" @click.stop="imgLook(slotProps.row)">
                          </div>
                          <div v-else-if="item.field === 'activityPrice'">
                            <el-input v-model="slotProps.row[item.field]" disabled></el-input>
                            <div v-if=" slotProps.row.valid === 0" style="color: red;font-size: 13px; position: absolute;">高于准入价格活动失效</div>
                          </div>
                          <div v-else>
                              <span>{{slotProps.row[item.field] }}</span>
                          </div>
                      </template>
                  </vxe-table-column>
              </vxe-table>
          </div>

    </xyy-panel>
    <el-image
      style="display: none;"
      :src="url"
      ref="imageRef"
      :preview-src-list="srcList">
    </el-image>
    <!-- 查看环线价 -->
    <viewRingLinePrice ref="viewRingLinePrice" :viewRingLinePriceRow="viewRingLinePriceRow"/>
  </div>
</template>
<script>
import {getSpecialDetail,checkAccess} from '@/api/marketing/specialActive'
import { addTableColumns } from './config'
import viewRingLinePrice from './components/viewRingLinePrice.vue'
export default {
  name: 'SpecialActiveView',
  components: {
    viewRingLinePrice
  },
  data() {
    return {
      checkAccess: false, //是否检查准入价
      activeId: '',
      type: '',
      loading:false,
      formData: {
        title: '',
        activeTime: []
      },
      inUnvalid:false,
      searchProduct: '', //查询已添加商品
      viewRingLinePriceRow:undefined, //查看环线价行数据
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        }
      },
      rules: {
        activeTime: [
          { required: true, message: '请选择活动时间', trigger: 'change' }
        ]
      },
      btnListTop: [
        {
          label: '返回列表',
          clickEvent: this.backToPage,
          code: "",
          plain: 'false',
          permission:'specialActive:view:back'
        }
      ],
      tableData: [],
      tableColumns: addTableColumns(),
      tableKey: 0,
      srcList:[],//图片列表
      isUnvalid:false,
      url:'',//查看图片
    }
  },
  mounted() {
    this.getDetail();
  },
  activated() {
    this.getDetail();
  },
  methods: {
    //是否检查准入价
    async checkAccessPrice(){
      const res = await checkAccess()
      if(res.code === 0){
        this.checkAccess = res.result.checkAccess
      } else {
        this.$message.error(res.msg)
      }
    },
    async getDetail() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      })
      try{
        const res = await getSpecialDetail({promotionId:this.$route.query.promotionId});
        if (res.code === 0) {
          this.formData.title = res.result.title;
          this.formData.startTime = res.result.startTime;
          this.formData.endTime = res.result.endTime;
          this.tableData = res.result.promotionSkuExtendDTOList.map(item => {
            item.csuid = item.skuId;
            item.statusText = item.statusName
            item.isShow = true;
            return item;
          });
        } else{
          this.$message.error(res.msg)
        }
      } catch(err){
        console.log(err);
      } finally {
        loading.close();
      }

    },
    /**查看图片 */
    imgLook(row){
      if (row.bigImgList) {
        this.srcList = row.bigImgList;
        this.url = this.srcList[0] || '';
        this.$nextTick(() => {
          this.$refs.imageRef.showViewer = true; // 触发预览
        });
      } else {
        this.srcList = [];
        this.url = '';
        this.$message.error('没有图片');
      }
    },
    // 动态设置行高
    rowStyle({ row, rowIndex }) {
      return {
        height:'80px !important',
      }; // 统一设置行高为 80px
    },
    backToPage() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push({path: '/marketing/SpecialActive'})
    },
    selectAllEvent(){
        },
     // 选择行
    selectChangeEvent({ row }) {
      this.$refs.xTable.toggleCheckboxRow(row)


    },
     //点击表格行
    cellClickEvent({ row }) {
      this.$refs.xTable.toggleCheckboxRow(row)
    },
   //查询已添加商品
   handelSearch(){
      if (this.isUnvalid) {
        // 只筛选 valid === 0
        this.tableData.forEach(item => {
          if (this.searchProduct) {
            // 有搜索条件时，valid === 0 且满足搜索匹配
            item.isShow = item.valid === 0 && (
              (item.erpProductCode && item.erpProductCode.toLowerCase().includes(this.searchProduct.toLowerCase())) ||
              (item.productCode && item.productCode.toLowerCase().includes(this.searchProduct.toLowerCase())) ||
              (item.productName && item.productName.toLowerCase().includes(this.searchProduct.toLowerCase()))
            );
          } else {
            // 没有搜索条件时，只筛 valid === 0
            item.isShow = item.valid === 0;
          }
        });
      } else {
        // 仅根据搜索条件筛选（不过滤 valid）
        if (this.searchProduct) {
          this.tableData.forEach(item => {
            item.isShow = (item.erpProductCode && item.erpProductCode.toLowerCase().includes(this.searchProduct.toLowerCase())) ||
                        (item.productCode && item.productCode.toLowerCase().includes(this.searchProduct.toLowerCase())) ||
                        (item.productName && item.productName.toLowerCase().includes(this.searchProduct.toLowerCase()));
          });
        } else {
          // 如果没有搜索条件，显示所有数据
          this.tableData.forEach(item => {
            item.isShow = true;
          });
        }
      }
    },
    //重置
    handelReset(){
      this.isUnvalid = false
      this.searchProduct = ''
      // 重置时显示所有数据
      this.tableData.forEach(item => {
        item.isShow = true
      })
    },
    //查看环线价
    viewRing(row){
      this.viewRingLinePriceRow = {...row}
      this.$refs.viewRingLinePrice.open()
    },
  }
}

</script>

<style scoped lang="scss">
::v-deep .vxe-body--column{
  max-height: 100px !important;
}
::v-deep .vxe-cell{
  max-height: 100px !important;
}
.redTips{
  background-color: #FEF0F0;
  height: 38px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;

  img {
    width: 16px;
    height: 16px;
    margin: 0 12px;
  }


  div {
    display: flex;
    align-items: center;

    span {
      display: flex;
      align-items: center;

      img {
        margin:0; // 只保留左边距
      }
    }
  }
}
::v-deep .viewImg{
  position: relative;
}
#imgLook{
  img {
    position: absolute;
    top: 50%;
    left:50%;
    margin-left: -32px;
    margin-top: -32px;
    width: 64px;
    height: 64px;
    cursor: pointer;
  }
}
.searchProduct{
  margin-bottom: 10px;
}
</style>

