<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav"/> -->
    <!-- <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav"/> -->

    <div class="right-menu">
      <!-- <template v-if="device!=='mobile'">
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template> -->
      <div class="store-relation">
        <span style="color: #ffffff;margin-right:10px;">{{ userinfo.tenant.name }}</span>
        <el-dropdown class="avatar-container" trigger="click">
          <img :src="avatar" class="user-avatar" style="margin-right:10px;">
          <el-dropdown-menu slot="dropdown">
            <router-link to="/user/profile">
              <el-dropdown-item  class="avatar-dropdown">
                <svg-icon icon-class="navbar-user-icon"/>
                个人中心</el-dropdown-item>
            </router-link>
            <el-dropdown-item  class="avatar-dropdown" divided @click.native="logout">
              <svg-icon icon-class="login-out-icon"/>
              <span style="margin-left: 3px;;">退出登录</span></el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown class="store-dropdown" trigger="click" @command="handleCommand">
          <div class="avatar-wrapper">
            <span class="org-name" :title="orgCodeName">
            {{ orgCodeName || '未选择机构' }}
            </span>
            <i class="el-icon-caret-bottom" />
         </div>
         <el-dropdown-menu slot="dropdown">
          <el-dropdown-item 
            v-for="warehouse in warehouses"
            :key="warehouse.dictValue"
            :label="warehouse.dictLabel"
            :value="warehouse.dictValue"
            :command="warehouse"
            class="store-item"
            @mouseenter="isHovered = warehouse.dictValue"
            @mouseleave="isHovered = null"
          >
            <svg-icon :icon-class="isHovered === warehouse.dictValue ? 'navbar-store-icon-active' : 'navbar-store-icon'"/>
            {{warehouse.dictLabel}} 
          </el-dropdown-item>
         </el-dropdown-menu>
        </el-dropdown>
      </div>
      
      <!-- <el-dropdown class="avatar-container" trigger="click">
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
         <el-dropdown-item @click.native="openWarehouseDialog">
          <span>切换仓储</span>
        </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
   <!-- TODO 需要前端同学打磨一下 -->
       <!-- <el-dialog
      title="切换仓储"
      :visible.sync="warehouseDialogVisible"
      width="30%"
      :before-close="handleClose">
      <el-select v-model="selectedOrgCode" placeholder="请选择仓储">
        <el-option
          v-for="warehouse in warehouses"
          :key="warehouse.id"
          :label="warehouse.dictLabel"
          :value="warehouse.dictValue">
        </el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="warehouseDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="switchOrg">确 定</el-button>
      </span>
    </el-dialog> -->
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import {queryOrg,switchOrg}   from '@/api/login'

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc
  },
  data() {
    return {
      warehouseDialogVisible: false, // 控制对话框显示
      selectedOrgCode: '', // 选择的仓储
      warehouses: [ // 仓储列表
        { id: '', dictLabel: '' ,dictValue:''},
      ],
      orgCodeName:"",
      tenent: '',
      isHovered: null  // 添加这一行来跟踪悬停状态
    };
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device',
      "userinfo"
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  mounted(){
    this.updateOrgCodeName()
    this.openWarehouseDialog()
  },
  methods: {
    updateOrgCodeName() {
      if (this.userinfo && this.userinfo.orgInfo) {
        this.orgCodeName = this.userinfo.orgInfo.warehouseName || '未选择机构';
      } else {
        this.orgCodeName = '未选择机构';
      }
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
     // 请求方库列表
   async openWarehouseDialog() {
      const response = await queryOrg();
      this.warehouses=response.data;
      this.$store.dispatch('setOrgInfo', response.data)
    },
    // 切换仓储
   async handleCommand(command) {
    try {
      // 调用切换机构的接口
      const response = await switchOrg(command.dictValue);
      if (response.code === 200) { 
        this.$message.success('切换机构成功');
        this.orgCodeName = command.dictLabel;
        
        // 关闭所有标签页
        await this.$store.dispatch('tagsView/delAllViews');
        
        this.$router.push({ path: '/index' }).then(() => {
          window.location.reload(); 
        }).catch((err) => {
          console.error('跳转到 /index 失败44444', err);
        });
      }
    } catch(err) {
      if (err.name !== 'NavigationDuplicated') {
        console.log(err);
      }
    }
  },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(async () => {
        // 关闭所有标签页
          await this.$store.dispatch('tagsView/delAllViews');
          //location.href = '/#/login';
          this.$router.push("/login")
        })
      }).catch(() => {});
    }
  }
}

</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #001529;
  box-shadow: #ffffff;
  padding: 0 25px 0 20px;
  .hamburger-container {
    float: left;
    height: 100%;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;
    justify-content: flex-start;
    align-items: center;
    width: 30px;
    vertical-align: middle;
    display: flex;
    padding: 0 !important;
  }
  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }
  }
}
.store-relation{
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 50px;
  font-size: 16px;
}
.store-dropdown{
  font-size: 16px;
  cursor: pointer;
}
.avatar-wrapper{
  font-size: 16px;
  color: #ffffff;
}
.avatar-container{
  font-size: 16px;
  display: flex;
  align-items: center;
  .user-avatar {
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    top: 18px;
  }
}
::v-deep .avatar-dropdown{
    &:hover, &:focus {
      background-color: #F0F3F6 !important;  // 鼠标悬停时的背景色
      color: #222222 !important;  // 鼠标悬停时的文字颜色
    }
}
::v-deep .store-item{
  &:hover, &:focus {
      background-color: transparent !important;  // 鼠标悬停时的背景色
      color: #00B955 !important;  // 鼠标悬停时的文字颜色
    }
}
::v-deep .el-dropdown-menu {
  .el-dropdown-menu__item {
    &.is-disabled {
      &:hover {
        background-color: transparent;
        color: #222222;
      }
    }
  }
}
::v-deep .store-item {
  .svg-icon {
    margin-right: 8px;
  }
}
</style>
<style>

</style>