<template>
    <div class="app-container">
        <xyy-panel :titleShow='false'>
            <div slot="tools" style="position: relative;">
                <div>
                    <el-tabs v-model="activeName">
                        <el-tab-pane name="edit" v-if="activeName == 'edit'">
                            <div slot="label">
                                <span>编辑控销组</span>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane name="add" v-else>
                            <div slot="label">
                                <span>新增控销组</span>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <!-- 按钮组 start -->
                <btn-group slot="tools" :btn-list="btnListTop" style="position: absolute; right: 0; top: 0; " />
            </div>
            <btn-group slot="tools" :btn-list="btnListForm" />
            <div>
                <el-form ref="formData" :model="formData" label-width="120px">
                    <el-row :gutter="20">
                        <el-col :lg="6" :md="6">
                            <el-form-item label="药店ID">
                                <el-input v-model="formData.merchantId" @input="merchantIdInput" />
                            </el-form-item>
                        </el-col>
                        <el-col :lg="6" :md="6">
                            <el-form-item label="药店名称">
                                <el-input v-model="formData.merchantName" />
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :lg="6" :md="6">
                            <el-form-item label="手机号">
                                <el-input v-model="formData.merchantMobile" />
                            </el-form-item>
                        </el-col> -->
                    </el-row>
                </el-form>
            </div>
        </xyy-panel>
        <xyy-panel :titleShow='false'>
            <btn-group slot="tools" :btn-list="btnListTable" style="float: left;" />
            <div slot="tools" class="submit-container">
                <div class="submit-form">
                    <span class="required">*</span>
                    <span class="submit-label">控销组名称</span>
                    <el-input v-model="submitForm.groupName" placeholder="请输入" />
                </div>
                <div>
                    <el-button type="primary" :loading="submitLoading" @click="submitFormHandle">更改控销组名称</el-button>
                </div>
            </div>
            <div :key="tabKey" class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }"
                    @checkbox-all="handleCheckboxAll" @checkbox-change="handleCheckboxChange"
                    @cell-click="handleCellClick">
                    <vxe-table-column type="checkbox" width="50" />
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
        <file-import ref="fileImport" @on-success="searchList" />
        <add-customers ref="addCustomers" @add-success="searchList" />
    </div>
</template>

<script>
import { queryMerchant, deleteMerchant, updateControlledStoreName } from '@/api/product/controlledStoreManagement';
import { controlStoreCol } from './config';
import { pageConfig } from '@/utils';
import fileImport from './components/fileImport.vue';
import addCustomers from './components/addCustomers.vue';

export default {
    name: 'ControlGroupEdit',
    components: { fileImport, addCustomers },
    data() {
        return {
            activeName: 'edit',
            btnListTop: [
                {
                    label: "返回",
                    type: 'primary',
                    clickEvent: this.backToPage,
                    code: "",
                    plain: 'false',
                    permission: 'product:ControlGroupEdit:backToPage'
                },
            ],
            btnListForm: [
                {
                    label: "查询",
                    type: 'primary',
                    clickEvent: this.searchList,
                    code: "",
                    plain: 'false',
                    permission: 'product:ControlGroupEdit:searchList'
                },
                {
                    label: "重置",
                    type: 'info',
                    clickEvent: this.resetForm,
                    code: "",
                    plain: 'false',
                    permission: 'product:ControlGroupEdit:resetForm'
                },
            ],
            btnListTable: [
                {
                    label: "批量删除",
                    type: 'danger',
                    clickEvent: this.batchDelete,
                    code: "",
                    plain: 'false',
                    permission: 'product:ControlGroupEdit:batchDelete'
                },
                {
                    label: "批量导入",
                    type: 'primary',
                    clickEvent: this.batchImport,
                    code: "",
                    plain: 'false',
                    permission: 'product:ControlGroupEdit:batchImport'
                },
                {
                    label: "添加客户",
                    type: 'primary',
                    clickEvent: this.addCustomer,
                    code: "",
                    plain: 'false',
                    permission: 'product:ControlGroupEdit:addCustomer'
                },
            ],
            formData: {
                merchantId: '', // 药店ID
                merchantName: '', // 药店名称
                // merchantMobile: '', // 手机号
                productCode: '' // 商品编码
            },
            submitForm: {
                groupName: this.$route.query.groupName // 控销组名称
            },
            tableData: [], // 表格数据
            loading: false, // 表格加载状态
            tabKey: 0, // 表格key
            tableKey: 0, // 表格key
            tablePage: pageConfig(), // 表格分页
            tableColumns: controlStoreCol(), // 表格列配置
            submitLoading: false, // 提交加载状态
        }
    },
    created() {
        this.$nextTick(() => {
            if (this.$route.query.type == 'add') {
                this.activeName = 'add'
                // this.$set(this.submitForm, 'groupName', this.$route.query.groupName)
                this.searchList()
            } else {
                this.activeName = 'edit'
                // this.$set(this.submitForm, 'groupName', this.$route.query.groupName)
                this.searchList()
            }
            if (localStorage.getItem('groupName')) {
                if (this.$route.query.groupName != localStorage.getItem('groupName') || this.$route.query.groupId != localStorage.getItem('groupId')) {
                    this.$set(this.submitForm, 'groupName', this.$route.query.groupName)
                    localStorage.setItem('groupName', this.$route.query.groupName)
                    localStorage.setItem('groupId', this.$route.query.groupId)
                }
            } else {
                this.$set(this.submitForm, 'groupName', this.$route.query.groupName)
                localStorage.setItem('groupName', this.$route.query.groupName)
                localStorage.setItem('groupId', this.$route.query.groupId)
            }
        })
    },
    activated() {
        this.$nextTick(() => {
            if (this.$route.query.type == 'add') {
                this.activeName = 'add'
                // this.$set(this.submitForm, 'groupName', this.$route.query.groupName)
                this.searchList()
            } else {
                this.activeName = 'edit'
                // this.$set(this.submitForm, 'groupName', this.$route.query.groupName)
                this.searchList()
            }
            if (localStorage.getItem('groupName')) {
                if (this.$route.query.groupName != localStorage.getItem('groupName') || this.$route.query.groupId != localStorage.getItem('groupId')) {
                    this.$set(this.submitForm, 'groupName', this.$route.query.groupName)
                    localStorage.setItem('groupName', this.$route.query.groupName)
                    localStorage.setItem('groupId', this.$route.query.groupId)
                }
            } else {
                this.$set(this.submitForm, 'groupName', this.$route.query.groupName)
                localStorage.setItem('groupName', this.$route.query.groupName)
                localStorage.setItem('groupId', this.$route.query.groupId)
            }
        })
    },
    beforeDestroy() {
        localStorage.removeItem('groupName')
        localStorage.removeItem('groupId')
    },
    methods: {
        merchantIdInput(value) {
            let newVal = value.trim();
            newVal = newVal.replace(/[^\d]/g, '');
            this.formData.merchantId = newVal;
        },
        handleCellClick({ row }) {
            this.$refs.xTable.toggleCheckboxRow(row)
        },
        handleCheckboxChange({ row }) {
            this.$refs.xTable.toggleCheckboxRow(row)
        },
        handleCheckboxAll() { },
        getList() {
            this.loading = true
            const params = {
                ...this.formData,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                groupId: Number(this.$route.query.groupId),
            }
            queryMerchant(params).then((res) => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.tableData = result.list
                    this.tablePage.total = result.total
                } else {
                    this.$message.error(msg)
                }
            }).finally(() => {
                this.loading = false
                this.tableKey++
            })
        },
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage
            this.tablePage.pageSize = pageSize
            this.getList()
        },
        resetForm() {
            this.formData = {
                merchantId: '', // 药店ID
                merchantName: '', // 药店名称
                // merchantMobile: '', // 手机号
                productCode: '' // 商品编码
            }
            this.searchList()
        },
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
        batchDelete() {
            const selectRows = this.$refs.xTable.getCheckboxRecords()
            if (selectRows.length === 0) {
                this.$message.warning('请勾选需要删除的行')
                return
            }
            this.$confirm('确认删除选中的行？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const params = {
                    groupId: Number(this.$route.query.groupId),
                    merchantIds: selectRows.map((item) => item.merchantId)
                }
                deleteMerchant(params).then(res => {
                    const { code, msg } = res
                    if (code === 0) {
                        this.$message.success(msg)
                        this.searchList()
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => { })
        },
        submitFormHandle() {
            this.$refs.formData.validate((valid) => {
                if (valid) {
                    this.submitLoading = true
                    const params = {
                        groupId: Number(this.$route.query.groupId),
                        name: this.submitForm.groupName,
                    }
                    updateControlledStoreName(params).then(res => {
                        if (res.code === 0) {
                            this.$message.success(res.msg)
                            this.$route.query.groupName = this.submitForm.groupName
                        } else {
                            this.$message.error(res.msg)
                        }
                    }).finally(() => {
                        this.submitLoading = false
                    })
                }
            })
        },
        addCustomer() {
            this.$refs.addCustomers.open(this.$route.query.groupId)
        },
        batchImport() {
            this.$refs.fileImport.open(this.$route.query.groupId)
        },
        backToPage() {
            this.$confirm('返回后续操作将不保存数据，是否返回？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$router.go(-1)
                this.$store.state.tagsView.visitedViews =
                    this.$store.state.tagsView.visitedViews.filter(
                        (item) => item.name !== "ControlGroupEdit"
                    );
            }).catch(() => {

            })
        },
    },
}
</script>

<style lang="scss" scoped>
.submit-container {
    display: flex;
    justify-content: end;

    .submit-form {
        display: flex;
        justify-content: start;
        align-items: center;
        margin-right: 23px;

        .submit-label {
            margin-right: 10px;
            width: 120px;
            font-size: 14px;
            color: #666;
            font-weight: bold;
        }
    }
}

.required {
    color: red;
}
</style>