import request from '@/utils/request'
//查询数量
export function getSpecialCount(data) {
  return request({
    url: '/promotion/special/getStatusCountInfo',
    method: 'post',
    data: data
  })
}
//获取活动列表
export function getSpecialList(data) {
  return request({
    url: '/promotion/special/list',
    method: 'post',
    data: data
  })
}
//下线活动
export function closeSpecial(data) {
  return request({
    url: '/promotion/special/offline',
    method: 'post',
    data: data
  })
}
// 获取活动详情
export function getSpecialDetail(data) {
  return request({
    url: '/promotion/special/promotionDetail',
    method: 'post',
    data: data
  })
}
// 获取活动变更日志
export function getSpecialLog(data) {
  return request({
    url: '/promotion/special/logs',
    method: 'post',
    data: data
  })
}
// 计算特价商品环线价
export function calcLoopPriceSpecial(data) {
  return request({
    url: 'promotion/special/calcLoopPrice',
    method: 'post',
    data: data
  })
}
// 计算商品环线价
export function calcLoopPrice(data) {
  return request({
    url: '/product/calcLoopPrice',
    method: 'post',
    data: data
  })
}
// 提交活动
export function saveSpecial(data) {
  return request({
    url: '/promotion/special/doAddOrEdit',
    method: 'post',
    data: data
  })
}
// 是否校验
export function checkAccess(data) {
  return request({
    url: '/promotion/special/getSpecialConfig',
    method: 'post',
    data: data
  })
}
// 导入
export function importProduct(data) {
  return request({
    url: '/promotion/special/importProduct',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// 获取商品列表
export function queryList(data) {
  return request({
    url: '/product/shelf/query',
    method: 'post',
    data: data
  })
}
