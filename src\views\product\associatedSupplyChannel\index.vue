<template>
<div class="app-container">
  <xyy-panel title="查询条件">
    <!-- 按钮组 start-->
    <btn-group slot="tools" :btn-list="btnListTop"/>
    <el-form :model="formData" ref="formData" label-width="120px" class="clearfix">
      <el-row :gutter="20">
        <el-col :lg="6" :md="6">
          <el-form-item label="批准文号" prop="approvalNumber">
            <el-input v-model.trim="formData.approvalNumber" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="商品名称" prop="productName">
            <el-input v-model.trim="formData.productName" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12">
          <el-form-item label="生产厂家" prop="manufacturer">
            <el-input v-model.trim="formData.manufacturer" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </xyy-panel>
  <xyy-panel :titleShow="false" :bottom="10">
    <xyy-panel :titleShow="false" :bottom="10">
      <div>通过此模块您可以快速进行商品供货渠道的新增设置： (也可以线下联系对接的人员,平台内部人员新增)</div>
      <div>1.根据批准文号，商品名称，生产厂家搜索平台的商品库信息</div>
      <div>2.根据搜索出的商品信息，确认后维护供货价和您公司的商品编码进行对码生成供货渠道</div>
      <div>3.待平台质管审核通过后，商家确认后即可下供货订单</div>
    </xyy-panel>
    <xyy-panel :titleShow="false" :bottom="10">
      <div style="display: flex; justify-content: space-between;">
        <div style="color: red;">备注：若没有查到想要的产品信息，请联系采购助理进行产品信息的维护，维护后才能进行渠道关联！</div>
        <el-button type="primary" v-hasPermi="['supplyChannel:template:download']" @click="_downloadTemplate" :loading="buttonLoading">下载模板</el-button>
      </div>
    </xyy-panel>
    <div class="table-box">
      <vxe-table ref="xTable" :loading="loading"  highlight-current-row height="auto" :row-style="rowStyle" :data="tableData" :key="tableKey" empty-text="暂未查询到匹配的记录"
      :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize,}">
         <vxe-table-column type="seq" title="序号" width="80" />
         <template>
          <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
          :title="item.title" :min-width="item.width" :fixed="item.fixed">
          <template v-slot:default="slotProps">
            <!-- 图片 -->
            <div v-if="item.field === 'isPublish'">
                <span>{{ slotProps.row.isPublish === 0 ? '否': '是' }}</span>
              </div>
              <!--操作按钮-->
              <div v-else-if="item.field === 'operation1'">
                <div v-if="slotProps.row.isPublish === 0">
                  <a href="#" class="clickWriting" v-hasPermi="['supplyChannel:list:addConnection']"  @click="addConnection(slotProps.row)">新增对码关联</a>
                </div>
                <div v-else>
                  <span class="clickWriting" v-hasPermi="['supplyChannel:list:CancelConnection']"  @click="cancelConnection(slotProps.row)">取消对码关联</span>
                </div>
              </div>
            <div v-else>
            {{ slotProps.row[item.field] }}
            </div>
          </template>
        </vxe-table-column>
         </template>
      </vxe-table>
    </div>
    <div class="pager">
      <vxe-pager
        border
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :page-sizes="tablePage.pageSizes"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total',
        ]"
        @page-change="handlePageChange"
      />
    </div>
  </xyy-panel>
  <add-connection ref="addConnection" @success="searchList"></add-connection>
</div>
</template>

<script>
import { pageConfig }  from "@/utils";
import{ tableColumns } from "./config";
import addConnection from './components/addConnection.vue'
import { queryList,downloadTemplate,cancelAssociate} from '@/api/product/associatedSupplyChannel';
export default {
  name: 'AssociatedSupplyChannel',
  components: {
    addConnection
  },
  data() {
    return {
      btnListTop: [
        {
            label: "查询",
            type: "primary",
            clickEvent: this.searchList,
            code: "",
            plain: 'false',
            permission:'supplyChannel:list:query'
         },
              {
             label: "重置",
             clickEvent: this.reset,
             code: "",
             plain: 'false',
            permission:'supplyChannel:list:reset'
        },
      ],
      tableKey: 0,
      formData: {
        approvalNumber:'', //批准文号
        productName:'', //商品名称
        manufacturer:'', //生产厂家
      },
      loading: false,
      tableData:[{productCode:'11'}],
      tablePage:pageConfig(),
      tableColumns:tableColumns(),
      buttonLoading:false,
    }
  },
  mounted() {
    this.tableKey++
    this.searchList();
  },
  methods: {
    // 动态设置行高
    rowStyle({ row, rowIndex }) {
      return {};
    },
    searchList(flag) {
      this.loading = true;
      if(flag !== true){
        this.tablePage.pageNum = 1;
      }
      const params = {
        pageNum: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
        ...this.formData,
      }

      queryList(params).then(res => {
        if(res.code === 0){
          this.tablePage.total = res.result.total;
          this.tableData = res.result.list;
        }else {
          this.$message.error(res.msg);
        }

      }).finally(() => {
        this.loading = false;
        this.tableKey++;
      })
    },
    //打开新增对码关联
    addConnection(row){
      if(row.isPublish === 1){
        return this.$message.warning('该商品已发布，无法进行对码关联！');
      }
      this.$refs.addConnection.open(row);
    },

    //取消对码关联
    cancelConnection(row){
      this.$confirm('是否取消对码关联？', '提示', {
        type: 'warning',
        showCancelButton: true,
        cancelButtonText: '取消',
        confirmButtonText: '确定',
      }).then(() => {
        const params = {
          productCode: row.productCode ? row.productCode : '',
          erpProductCode: row.erpProductCode ? row.erpProductCode : '',
        }
        cancelAssociate(params).then(res => {
          if(res.code === 0){
            this.$message.success(res.msg);
            this.searchList();
          }else {
            this.$message.error(res.msg);
          }
        })
      }).catch(() => {})
    },

    reset() {
      this.tablePage.pageSize = 20
      this.$refs.formData.resetFields();
      this.searchList();
    },
    // 分页器改变处理
    handlePageChange({currentPage, pageSize}){
      this.tablePage.pageNum = currentPage;
      this.tablePage.pageSize = pageSize;
      this.searchList(true);
    },

    //下载模板
    _downloadTemplate() {
      this.buttonLoading = true;
      downloadTemplate().then(res => {
       // 确定文件类型，这里假设是 Excel 文件
       const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
       const blob = new Blob([res], { type: fileType });
       // 创建一个 URL 对象
       const url = window.URL.createObjectURL(blob);
       // 创建一个 <a> 标签
       const link = document.createElement('a');
       link.download = `商品首营资料模板.xlsx`;
       link.href = url;
       //link.download = `${row.menuDesc}.xlsx`; // 设置下载的文件名，假设是 .xlsx 文件
       document.body.appendChild(link);
       link.click();
       // 释放 URL 对象
       window.URL.revokeObjectURL(url);
       document.body.removeChild(link);
      }).finally(() => {
        this.buttonLoading = false;
      })

    }
  }
}
</script>
<style lang="scss" scoped>
</style>
