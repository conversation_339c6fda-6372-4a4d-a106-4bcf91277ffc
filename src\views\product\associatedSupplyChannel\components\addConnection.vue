<template>
  <div>
    <xyy-dialog title="新增对码商品" width="20%" ref="addConnectionDialog">
      <el-form label-width="120px" class="clearfix">
        <el-row :gutter="20">
          <el-col>
            <el-form-item label="通用名:">
              {{ row.commonName }}
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="规格:">
              {{ row.spec }}
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="生产企业:">
              {{ row.manufacturer }}
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="单位:">
              {{ row.productUnit }}
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="税率:">
              <el-input v-model="row.salesRateValue" disabled style="width: 80%;"></el-input>
            </el-form-item>
          </el-col>
          <el-col style="margin-top: 12px;">
            <el-form-item label="本公司商品编码:">
              <el-input v-model="row.erpProductCode" style="width: 80%;" @input="erpProductCodeChange"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <span slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='submitLoading'>确 定</el-button>
      </span>
    </xyy-dialog>
  </div>
</template>
<script>
import { associate } from '@/api/product/associatedSupplyChannel'
export default {
  name: "addConnection",
  props: {
  },
  data() {
    return {
      submitLoading: false,
      row: {},
    };
  },

  methods: {
    erpProductCodeChange(value) {
      // 规范输入：移除无效字符，只允许输入字母和数字
      let newValue = value.replace(/[^A-Za-z0-9]/g, '');
      // 更新值
      this.$set(this.row, 'erpProductCode', newValue);
  },
  open(row) {
    this.submitLoading = false
    this.$refs.addConnectionDialog.open();
    this.row = row;
  },
  close() {
    this.$refs.addConnectionDialog.close();
  },
  submit() {
    this.submitLoading = true;
    associate({
      productCode: this.row.productCode,
      erpProductCode: this.row.erpProductCode
    }).then(res => {
      if (res.code === 0) {
        this.$message.success('操作成功');
        this.$emit('success');
        setTimeout(() => {
          this.$refs.addConnectionDialog.close();
        }, 500);
      } else {
        this.$message.error(res.msg);
      }
    }).finally(() => {
      this.submitLoading = false;
    });
  }

}
}
</script>
<style scoped lang="scss">
::v-deep.el-form-item {
  margin-bottom: 0;
}
</style>
