import Layout from '@/layout';

export default [
    {
        path: '/product',
        component: Layout,
        meta: { title: '商品管理', icon: 'category-icon',},
        alwaysShow: true,
        children: [
            {
                path: 'addReduction',
                component: () => import('@/views/product/addReduction/index.vue'),
                name: 'addReduction',
                meta: { title: '上下架管理', activeMenu: '/product/addReduction' }
            },
            {
                path: 'inventory',
                component: () => import('@/views/product/inventory/index.vue'),
                name: 'inventoryList',
                meta: { title: '库存管理', activeMenu: '/product/inventory' }
            },
            {
                path: 'businessManagement',
                hidden: true,
                component: () => import('@/views/product/businessManagement/index.vue'),
                name: 'businessManagement',
                meta: { title: '控销商圈管理', activeMenu: '/product/businessManagement' }
            },
            {
                path:'associatedSupplyChannel',
                component:()=>import('@/views/product/associatedSupplyChannel/index.vue'),
                name:'associatedSupplyChannel',
                meta:{title:'关联供货渠道',activeMenu:'/product/associatedSupplyChannel'}
            },
            {
                path: 'activitySettings',
                component: () => import('@/views/product/activitySettings/index.vue'),
                name: 'activitySettings',
                meta: { title: '活动商品上下架设置', activeMenu: '/product/activitySettings' }
            },
            {
                path: 'controlledStoreManagement',
                component: () => import('@/views/product/controlledStoreManagement/index.vue'),
                name: 'ControlledStoreManagement',
                meta: { title: '控销店铺管理', activeMenu: '/product/controlledStoreManagement' }
            },
            {
                path: 'controlGroupDetail',
                component: () => import('@/views/product/controlledStoreManagement/contolGroupDetail.vue'),
                name: 'ControlGroupDetail',
                meta: { title: '控销组详情', activeMenu: '/product/controlledStoreManagement' }
            },
            {
                path: 'controlGroupEdit',
                component: () => import('@/views/product/controlledStoreManagement/contolGroupEdit.vue'),
                name: 'ControlGroupEdit',
                meta: { title: '控销组管理', activeMenu: '/product/controlledStoreManagement' }
            },
            {
                path: 'productSalesRecord',
                component: () => import('@/views/product/productSalesRecord/index.vue'),
                name: 'ProductSalesRecord',
                meta: { title: '商品销售记录', activeMenu: '/product/productSalesRecord' }
            },
        ]
    }
]
