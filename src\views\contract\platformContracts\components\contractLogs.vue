<template>
    <div>
        <xyy-dialog key="dialog1" :footerShow="false" ref="contractLogs" title="查看日志" width="70%">
            <xyy-panel :titleShow="false">
                <div>
                    <div style="margin: 23px;">
                        <span style="margin-right: 23px;">合同模板：{{ contractName }}</span>
                        <span>任务ID：{{ flowNo }}</span>
                    </div>
                    <div :key="tabKey" class="table-box">
                        <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                            :key="tableKey" empty-text="暂无日志数据">
                            <vxe-table-column type="seq" title="序号" width="80" />
                            <template>
                                <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                                    :title="item.title" :min-width="item.width" :fixed="item.fixed">
                                </vxe-table-column>
                            </template>
                        </vxe-table>
                    </div>
                    <div class="pager">
                        <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                            :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                                'PrevPage',
                                'JumpNumber',
                                'NextPage',
                                'FullJump',
                                'Sizes',
                                'Total',
                            ]" @page-change="handlePageChange" />
                    </div>
                </div>
            </xyy-panel>
        </xyy-dialog>
    </div>
</template>

<script>
import { queryLogList } from '@/api/contract/platformContracts';
import { pageConfig } from '@/utils';
import { logTableColumns } from '../config';
export default {
    data() {
        return {
            contractName: '',
            flowNo: '',
            loading: false,
            tableData: [],
            tableColumns: logTableColumns(),
            tableKey: 0,
            tabKey: 0,
            tablePage: pageConfig(),
            rowData: {},
        }
    },
    methods: {
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getLogInfo();
        },
        getLogInfo() {
            this.loading = true;
            const params = {
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                baseId: this.rowData.baseId,
            };
            queryLogList(params).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.tableData = data.list
                    this.tablePage.total = data.total
                    this.loading = false
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            }).catch(err => {
                this.loading = false
            });
        },
        open(row) {
            this.$refs.contractLogs.open();
            this.rowData = row
            this.flowNo = row.baseId
            this.contractName = row.contractTemplateStr
            this.getLogInfo()
        },
        close() {
            this.$refs.contractLogs.close();
        },
    }
}
</script>
<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper {
    height: 230px !important;
}
</style>