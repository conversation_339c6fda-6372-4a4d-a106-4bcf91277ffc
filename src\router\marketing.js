import Layout from '@/layout';

export default [
    {
        path: '/marketing',
        component: Layout,
        meta: { title: '营销管理', icon: 'clipboard',},
        alwaysShow: true,
        children: [
            {
                path: 'specialActive',
                component: () => import('@/views/marketing/specialActive/index.vue'),
                name: 'SpecialActive',
                meta: { title: '特惠活动', activeMenu: '/marketing/specialActive' }
            },
            {
                path: 'specialActiveAdd',
                component: () => import('@/views/marketing/specialActive/add.vue'),
                name: 'SpecialActiveAdd',
                meta: { title: '新增特惠活动', activeMenu: '/marketing/specialActive/add' }
            },
            {
                path: 'specialActiveEdit',
                component: () => import('@/views/marketing/specialActive/edit.vue'),
                name: 'SpecialActiveEdit',
                meta: { title: '编辑特惠活动', activeMenu: '/marketing/specialActive/edit' }
            },
            {
                path: 'specialActiveView',
                component: () => import('@/views/marketing/specialActive/view.vue'),
                name: 'SpecialActiveView',
                meta: { title: '查看特惠活动', activeMenu: '/marketing/specialActive/view' }
            }
        ]
    }
]
