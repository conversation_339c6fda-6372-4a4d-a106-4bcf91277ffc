<template>
    <div class="app-container">
        <xyy-panel title="发票录入">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTable" />
            <div class="table-box">
                <vxe-table ref="xTable" :loading="xloading" highlight-current-row height="auto" :data="invoiceTableData"
                    :key="xtableKey" empty-text="暂无发票数据" :footer-method="footerMethod" show-footer
                    :row-style="invoiceRowStyle">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in invoiceColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            <!-- 表头插槽，根据 required 属性添加红色星号 -->
                            <template v-slot:header="params">
                                <span>
                                    <span v-if="item.required" style="color: red;">*</span>
                                    {{ item.title }}
                                </span>
                            </template>
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'operation'">
                                    <span @click="deleteInvoicehandler(slotProps.row, slotProps.$rowIndex)"
                                        class="clickWriting" style="margin-left:12px; color: red;">删除</span>
                                </div>
                                <div v-else-if="item.field == 'taxNo'">
                                    <el-input v-model="slotProps.row[item.field]" placeholder="发票号" disabled>
                                        <el-button slot="append" icon="el-icon-search"
                                            @click="openInvoiceSearch(slotProps.rowIndex)"></el-button>
                                    </el-input>
                                </div>
                                <div v-else-if="item.field === 'invoiceTime'">
                                    <el-date-picker v-model="slotProps.row[item.field]" type="date" placeholder="请选择"
                                        value-format="yyyy-MM-dd" disabled>
                                    </el-date-picker>
                                </div>
                                <div v-else-if="item.field === 'noTaxAmount'">
                                    <el-input v-model="slotProps.row[item.field]" placeholder="请输入"
                                        suffix-icon="el-icon-edit-outline"
                                        @input="noTaxAmountInput($event, slotProps.row)"
                                        @change="noTaxAmountChange(slotProps.row)"></el-input>
                                </div>
                                <div v-else-if="item.field === 'invoiceTax'">
                                    <el-input v-model="slotProps.row[item.field]" placeholder="请输入"
                                        suffix-icon="el-icon-edit-outline"
                                        @input="taxAmountInput($event, slotProps.row)"
                                        @change="taxAmountChange(slotProps.row)"></el-input>
                                </div>
                                <div v-else-if="item.field == 'taxRate'">
                                    <span v-if="slotProps.row.taxRate">{{ slotProps.row.taxRate }}%</span>
                                </div>
                                <!-- <div v-else-if="item.field == 'auditTax'">
                                  <span>{{ slotProps.row.invoiceTax - slotProps.row.noTaxAmount }}</span>
                                </div> -->
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
        </xyy-panel>
        <xyy-panel title="订单核对">
            <div class="table-box">
                <vxe-table ref="yTable" :loading="yloading" highlight-current-row height="auto" :data="ordersTableData"
                    :key="ytableKey" empty-text="暂无订单数据" :row-style="ordersRowStyle">
                    <template>
                        <vxe-table-column v-for="item in ordersColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'operation'">
                                    <span @click="deleteOrderhandler(slotProps.row, slotProps.$rowIndex)"
                                        class="clickWriting" style="margin-left:12px; color: red;">删除</span>
                                </div>
                                <div v-else-if="item.field == 'auditAmount'">
                                    <el-input v-model="slotProps.row[item.field]" placeholder="请输入"
                                        suffix-icon="el-icon-edit-outline" @change="orderAmountChange(slotProps.row)"
                                        @input="orderAmountInput($event, slotProps.row)" disabled></el-input>
                                </div>
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
        </xyy-panel>
        <xyy-panel :titleShow="false">
            <div id="footer_box">
                <div id="form_box">
                    <div id="form_content">
                        汇总发票含税金额:
                        <span id="form_amount">{{ `￥${formData.invoiceAmount}` }}</span>
                    </div>
                    <div id="form_content">
                        汇总发票不含税金额:
                        <span id="form_amount">{{ `￥${formData.invoiceAmountNoTax}` }}</span>
                    </div>
                    <div id="form_content">
                        含税金额差异:
                        <span id="form_amount">{{ `￥${formData.invoiceAmountDifference}` }}</span>
                    </div>
                    <div id="form_content">
                        税额总计:
                        <span id="form_amount">{{ `￥${formData.fileTotalTax}` }}</span>
                    </div>
                </div>
                <div id="btn_box">
                    <!-- 按钮组 start-->
                    <btn-group slot="tools" :btn-list="btnListForm" />
                </div>
            </div>
        </xyy-panel>
        <select-invoice ref="selectInvoice" @echo="echo" />
    </div>
</template>

<script>
import { auditInvoice } from '@/api/finance/InvoiceLedger'
import selectInvoice from './selectInvoice.vue'
import { addInvoiceColumns, confirmOrderColumns } from '../config'
export default {
    name: 'EnterInvoice',
    components: { selectInvoice },
    data() {
        return {
            xloading: false,    //表格加载
            invoiceTableData: [],   //发票表格数据
            xtableKey: 0,   //发票表格刷新
            invoiceColumns: addInvoiceColumns(),   //发票表格列
            yloading: false,    //货单表格加载
            ordersTableData: [],   //货单表格数据
            ytableKey: 0,   //货单表格刷新
            ordersColumns: confirmOrderColumns(),  //货单表格列
            btnListForm: [
                {
                    label: "提交",
                    clickEvent: this.submitOrder,
                    type: 'success',
                    plain: 'false',
                    permission: "finance:enterInvoice:submitOrder"
                },
                {
                    label: "返回",
                    clickEvent: this.backToPage,
                    plain: 'false',
                    permission: "finance:enterInvoice:back"
                },
            ],
            btnListTable: [
                {
                    label: "新增行",
                    clickEvent: this.addRow,
                    type: 'success',
                    plain: 'false',
                    permission: "finance:enterInvoice:addRow"
                },
            ],
            formData: {
                invoiceAmount: 0,  //发票含税金额
                invoiceAmountDifference: 0,  //含税金额差异
                invoiceAmountNoTax: 0,  //发票不含税金额
                fileTotalTax:0,  //税额总计
            },
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.addRow()
            this.ordersTableData = localStorage.getItem('enterInvoice') ? JSON.parse(localStorage.getItem('enterInvoice')) : []
        })
    },
    activated() {
        this.$nextTick(() => {
            this.computedAmount()
            if(localStorage.getItem('enterInvoice')){
                const orders =  JSON.parse(localStorage.getItem('enterInvoice'))
                if(this.ordersTableData.length != orders.length){
                    this.invoiceTableData = []
                    this.addRow()
                    this.ytableKey = this.ytableKey + 1
                }else {
                    let diffArr = []
                    this.ordersTableData.forEach((item, index) => {
                        if(item.id != orders[index].id){
                            diffArr.push(item)
                        }
                    })
                    if(diffArr.length > 0){
                        this.invoiceTableData = []
                        this.addRow()
                        this.ytableKey = this.ytableKey + 1
                    }
                }
            }
            this.ordersTableData = localStorage.getItem('enterInvoice') ? JSON.parse(localStorage.getItem('enterInvoice')) : []
        })
    },
    watch: {
        invoiceTableData: {
            handler(newValue, oldValue) {
                this.computedAmount()
            },
            deep: true
        },

        ordersTableData: {
            handler(newValue, oldValue) {
                this.computedAmount()
            },
            deep: true
        }
    },
    methods: {
        // 提交
        submit() {
            const Loading = this.$loading({
                lock: true,
                text: '正在提交数据……',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
            })
            const invoiceFileList = this.invoiceTableData.map(item => {
                return {
                    taxNo: item.taxNo,
                    auditAmount: Number(item.invoiceTax),
                    auditNoTaxAmount: Number(item.noTaxAmount),
                    auditTax: Number(item.auditTax)
                }
            })
            const invoiceList = this.ordersTableData.map(item => {
                return {
                    invoiceNo: item.invoiceNo,
                    waitEnterAmount: Number(item.waitEnterAmount),
                    auditAmount: Number(item.auditAmount)
                }
            })
            const params = {
                invoiceFileList,
                invoiceList
            }
            auditInvoice(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.$message({
                        type: 'success',
                        message: msg
                    })
                    Loading.close()
                    this.backToPage()
                } else {
                    this.$message({
                        type: 'error',
                        message: msg
                    })
                    Loading.close()
                }
            }).finally(() => {
                Loading.close()
            })
        },
        // 订单行样式
        ordersRowStyle({ row, rowIndex }) {
            if (!row.auditAmount) {
                return { backgroundColor: '#EDFFEF' }
            } else {
                return {}
            }
        },
        // 发票行样式
        invoiceRowStyle({ row, rowIndex }) {
            if (!row.taxNo || !row.invoiceTime || !row.noTaxAmount || !row.invoiceTax) {
                return { backgroundColor: '#EDFFEF' }
            } else {
                return {}
            }
        },
        // 提交
        submitOrder() {
            let emptyInvoice = []
            let emptyInvoiceTime = []
            let emptyInvoiceTax = []
            let emptyInvoiceNoTax = []
            let sameInvoice = []
            let sameInvoiceSeen = new Map()
            let emptyOrder = []
            if(this.invoiceTableData.length == 0){
                this.$message({
                    type: 'warning',
                    message: '请选择发票'
                })
                return
            }
            this.invoiceTableData.forEach((item, index) => {
                if (!item.taxNo) {  // 未选择发票
                    emptyInvoice.push(index)
                }
                if (!item.invoiceTime) {
                    emptyInvoiceTime.push(index + 1)
                }
                if (!item.invoiceTax) {
                    emptyInvoiceTax.push(index + 1)
                }
                if (!item.noTaxAmount) {
                    emptyInvoiceNoTax.push(index + 1)
                }
                // 发票号重复
                const invoiceNo = item.taxNo
                if (sameInvoiceSeen.has(invoiceNo)) {
                    if (!sameInvoice.includes(invoiceNo)) {
                        sameInvoice.push(invoiceNo)
                    }
                } else {
                    sameInvoiceSeen.set(invoiceNo, true)
                }
            })
            this.ordersTableData.forEach((item, index) => {
                if (!item.auditAmount) {  // 订单内容未填写
                    emptyOrder.push(index)
                }
            })
            if (emptyInvoice.length > 0) {
                this.$message.warning(`请输入发票号`)
                return
            }
            if (emptyInvoiceTime.length > 0) {
                this.$message.warning(`请输入发票时间`)
                return
            }
            if (emptyInvoiceNoTax.length > 0) {
                this.$message.warning(`请输入发票不含税金额`)
                return
            }
            if (emptyInvoiceTax.length > 0) {
                this.$message.warning(`请输入发票含税金额，且校验输入发票含税金额不得小于发票不含税金额`)
                return
            }
            if (emptyOrder.length > 0) {
                this.$message.warning(`第${emptyOrder.join(',')}行订单内容未填写完整`)
                return
            }
            if (Number(this.formData.invoiceAmountDifference) != 0) {
                this.$message.warning('发票含税金额汇总与本次开票金额汇总不一致，请检查')
                return
            }
            if (sameInvoice.length > 0) {
                this.$confirm(`供应商发票号[${sameInvoice.join(',')}],是否继续`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.submit()
                }).catch(() => {
                    return
                })
            } else {
                this.submit()
            }
        },
        //发票反显
        echo(data) {
            this.invoiceTableData = this.invoiceTableData.filter(item => item.taxNo !== null && item.taxNo !== undefined && item.taxNo !== '')
            data.forEach(item => {
                if (this.invoiceTableData.some(item1 => item1.taxNo == item.taxNo)) {
                    return
                }
                this.$set(item, 'noTaxAmount', '')
                this.$set(item, 'invoiceTax', '')
                this.invoiceTableData.push(item)
            })
            this.xtableKey = this.xtableKey + 1
        },
        // 发票搜索
        openInvoiceSearch(row, rowIndex) {
            this.$refs.selectInvoice.open(rowIndex)
        },
        // 新增行
        addRow() {
            const addRowColumns = JSON.parse(JSON.stringify(addInvoiceColumns()))
            const addRow = {}
            addRowColumns.forEach(item => {
                addRow[item.field] = ''
            })
            this.invoiceTableData.push(addRow)
        },
        // 计算金额
        computedAmount() {
            let sumNoTaxValue = 0
            let sumTaxValue = 0
            let sumOrderValue = 0
            let sumFileTotalTax = 0
            this.invoiceTableData?.forEach(item => {
                if (item.noTaxAmount) {
                    sumNoTaxValue += Number.isNaN(Number(item.noTaxAmount)) ? 0 : Number(item.noTaxAmount)
                }
                if (item.invoiceTax) {
                    sumTaxValue += Number.isNaN(Number(item.invoiceTax)) ? 0 : Number(item.invoiceTax)
                }
                if (item.auditTax) {
                  sumFileTotalTax += Number.isNaN(Number(item.auditTax)) ? 0 : Number(item.auditTax)
                }
            })
            this.ordersTableData?.forEach(item => {
                if (item.auditAmount) {
                    sumOrderValue += Number.isNaN(Number(item.auditAmount)) ? 0 : Number(item.auditAmount)
                }
            })
            this.formData.invoiceAmount = Number.isNaN(sumTaxValue) ? 0 : sumTaxValue.toFixed(2)
            this.formData.invoiceAmountNoTax = Number.isNaN(sumNoTaxValue) ? 0 : sumNoTaxValue.toFixed(2)
            this.formData.invoiceAmountDifference = Number.isNaN((Number(sumOrderValue) - Number(sumTaxValue))) ? 0 : (Number(sumOrderValue) - Number(sumTaxValue)).toFixed(2)
            this.formData.fileTotalTax = Number.isNaN(sumFileTotalTax) ? 0 : sumFileTotalTax.toFixed(2)
        },
        // 订单金额
        orderAmountInput(value, row) {
            // 规范输入：移除无效字符并限制小数点位数
            let newValue = value.replace(/[^-\d.]/g, ''); // 删除非数字/负号/小数点字符
            const decimalRegex = /^-?\d*(\.\d{0,2})?$/;

            // 如果存在多个小数点或不满足小数位数，进行修正
            if (!decimalRegex.test(newValue)) {
                newValue = newValue
                    .split('.') // 以小数点分割
                    .slice(0, 2) // 保留整数和一个小数部分
                    .join('.'); // 重新组合
                newValue = newValue.replace(/(\.\d{2})\d+/, '$1'); // 限制小数点后2位
            }

            // 更新值
            this.$set(row, 'auditAmount', newValue);
        },
        // 订单金额
        orderAmountChange(row) {
            if (row.auditAmount > row.realAmount) {
                this.$message({
                    message: '本次开票金额不能大于实际金额',
                    type: 'warning'
                })
                row.auditAmount = row.waitEnterAmount
            }
            localStorage.setItem('enterInvoice', JSON.stringify(this.ordersTableData))
        },
        // 发票含税金额修改
        taxAmountChange(row) {
          let invoiceTax = Number.isNaN(Number(row.invoiceTax)) ? 0:Number(row.invoiceTax)
          let noTaxAmount = Number.isNaN(Number(row.noTaxAmount)) ? 0:Number(row.noTaxAmount)
          if (invoiceTax && noTaxAmount) {
            if(Math.abs(noTaxAmount) > Math.abs(invoiceTax) ){
              this.$set(row, 'noTaxAmount', '')
              this.xtableKey = this.xtableKey + 1
              return this.$message({
                message: '发票不含税金额绝对值不能大于发票含税金额绝对值',
                type: 'warning'
              })
            }
            row.auditTax = (invoiceTax - noTaxAmount).toFixed(2)
          } else(
            row.auditTax = ''
          )
            this.xtableKey = this.xtableKey + 1
        },
        // 发票含税金额修改
        taxAmountInput(value, row) {
            // 规范输入：移除无效字符并限制小数点位数
            let newValue = value.replace(/[^-\d.]/g, ''); // 删除非数字/负号/小数点字符
            const decimalRegex = /^-?\d*(\.\d{0,2})?$/;

            // 如果存在多个小数点或不满足小数位数，进行修正
            if (!decimalRegex.test(newValue)) {
                newValue = newValue
                    .split('.') // 以小数点分割
                    .slice(0, 2) // 保留整数和一个小数部分
                    .join('.'); // 重新组合
                newValue = newValue.replace(/(\.\d{2})\d+/, '$1'); // 限制小数点后2位
            }

            // 更新值
            this.$set(row, 'invoiceTax', newValue);
        },
        // 不含税金额修改
        noTaxAmountChange(row) {
          let invoiceTax = Number.isNaN(Number(row.invoiceTax)) ? 0:Number(row.invoiceTax)
          let noTaxAmount = Number.isNaN(Number(row.noTaxAmount)) ? 0:Number(row.noTaxAmount)
          if (invoiceTax && noTaxAmount) {
            if(Math.abs(noTaxAmount) > Math.abs(invoiceTax) ){
              this.$set(row, 'noTaxAmount', '')
              this.xtableKey = this.xtableKey + 1
              return this.$message({
                message: '发票不含税金额绝对值不能大于发票含税金额绝对值',
                type: 'warning'
              })
            }
            row.auditTax = (invoiceTax - noTaxAmount).toFixed(2)
          }else{
            row.auditTax = ''
          }
            this.xtableKey = this.xtableKey + 1
        },
        // 不含税金额修改
        noTaxAmountInput(value, row) {
            // 规范输入：移除无效字符并限制小数点位数
            let newValue = value.replace(/[^-\d.]/g, ''); // 删除非数字/负号/小数点字符
            const decimalRegex = /^-?\d*(\.\d{0,2})?$/;

            // 如果存在多个小数点或不满足小数位数，进行修正
            if (!decimalRegex.test(newValue)) {
                newValue = newValue
                    .split('.') // 以小数点分割
                    .slice(0, 2) // 保留整数和一个小数部分
                    .join('.'); // 重新组合
                newValue = newValue.replace(/(\.\d{2})\d+/, '$1'); // 限制小数点后2位
            }

            // 更新值
            this.$set(row, 'noTaxAmount', newValue);
        },
        // 删除订单
        deleteOrderhandler(row, rowIndex) {
            if (this.ordersTableData.length == 1) {
                this.$message({
                    message: '请至少保留一个订单',
                    type: 'warning'
                })
                return
            }
            this.$confirm('确认删除该订单吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.ordersTableData.splice(rowIndex, 1)
                localStorage.setItem('enterInvoice', JSON.stringify(this.ordersTableData))
                this.ytableKey = this.ytableKey + 1
            }).catch(() => {
            })
        },
        // 删除发票
        deleteInvoicehandler(row, rowIndex) {
            this.$confirm('确认删除该发票吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.invoiceTableData.splice(rowIndex, 1)
                this.xtableKey = this.xtableKey + 1
            }).catch(() => {
            })
        },
        // 返回
        backToPage() {
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
        },
        /**
        * 生成表格底部汇总行的方法。
        * @param {Array} param0.columns - 当前表格的列信息数组。
        * @param {Array} param0.data - 当前表格的数据数组。
        * @returns {Array} 返回包含汇总信息的数组。
        */
        footerMethod({ columns, data }) {
            // 存储汇总信息的数组
            let all = [];
            // 需要汇总的属性列表
            let propertys = [
                'noTaxAmount',
                'invoiceTax',
                'auditTax'
            ];
            // 调用自定义函数 blusFunction 来计算属性的汇总值
            let numObj = this.blusFunction(data, propertys);

            // 遍历表格的每一列
            columns.forEach((column, columnIndex) => {
                // 如果是第一列，添加汇总标题
                if (columnIndex === 0) {
                    all.push("汇总");
                } else {
                    // 获取对应属性的汇总值，如果没有则用 "-" 表示
                    let value = numObj[column.property] ? numObj[column.property].toFixed(2) : "-";
                    all.push(value);
                }
            });

            // 返回包含汇总信息的数组
            return [all];
        },

        /**
         * 计算给定数据数组中指定属性的汇总值。
         * @param {Array} data - 包含要计算的数据的数组。
         * @param {Array} propertys - 需要汇总的属性列表。
         * @returns {Object} 返回包含各属性汇总值的对象。
         */
        blusFunction(data, propertys) {
            let all = {}; // 存储汇总值的对象

            // 遍历数据数组中的每一项
            data.forEach((item) => {
                // 遍历属性列表中的每一个属性
                propertys.forEach((key) => {
                    // 如果汇总对象中不存在该属性，则初始化为0
                    if (!all[key]) {
                        all[key] = 0;
                    }
                    // 将当前数据项的属性值累加到汇总对象中
                    all[key] += Number.isNaN(item[key] * 1) ? 0 : item[key] * 1; // 乘以1确保属性值为数字
                });
            });

            return all; // 返回包含各属性汇总值的对象
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper {
    height: auto !important;
}

.detail ::v-deep .vxe-table--footer-wrapper.body--wrapper {
    overflow-x: hidden !important;
}

#footer_box {
    position: relative;

    #form_box {
        display: flex;
        justify-content: start;
        margin: 23px;
        align-items: center;
        font-weight: bolder;
        font-size: 23px;

        #form_amount {
            margin-right: 23px;
        }
    }

    #btn_box {
        position: absolute;
        right: 0;
        bottom: 0;
    }
}
</style>