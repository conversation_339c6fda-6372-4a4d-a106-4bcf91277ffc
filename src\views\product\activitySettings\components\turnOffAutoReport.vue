<template>
    <div>
        <xyy-dialog key="dialog1" :footerShow="true" ref="turnOffAutoReport" title="提示" width="30%">
            <xyy-panel :titleShow="false">
                <div class="dialog-body">开关关闭，已生成的拼团品是否一并删除？</div>
            </xyy-panel>
            <span slot="footer" class="dialog-footer">
                <el-button type="danger" @click="closeAndShelve(1)">关闭且删除</el-button>
                <el-button type="warning" @click="closeAndShelve(0)">仅关闭不删除</el-button>
            </span>
        </xyy-dialog>
    </div>
</template>

<script>

export default {

    data() {
        return {

        };
    },
    methods: {
        // 关闭弹窗
        close() {
            this.$refs.turnOffAutoReport.close();
        },
        // 打开弹窗
        open() {
            this.$refs.turnOffAutoReport.open();
        },
        // 是否关闭并下架
        closeAndShelve(flag) {
            this.$emit('closeAndShelve', flag);
            this.close();
        },
    }
}
</script>

<style></style>