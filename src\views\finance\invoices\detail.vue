<template>
    <div class="app-container">
        <xyy-panel title="结算单明细列表">
            <btn-group slot="tools" :btn-list="btnListTop" />
            <div class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" empty-text="暂无结算单明细数据">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :width="item.width" :fixed="item.fixed">
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div>
        </xyy-panel>
    </div>
</template>

<script>
import { getInvoicesDetailList } from "@/api/finance/invoices";
import { pageConfig } from "@/utils";
import { detailColumns } from './config'
import { exportFile } from '@/api/system/exportCenter';
export default {
    data() {
        return {
            btnListTop: [
                {
                    label: "导出明细",
                    type: "warning",
                    clickEvent: this.exportTable,
                    plain: 'false',
                    permission: "finance:invoicesDetail:export"
                },
                {
                    label: "返回",
                    type: "info",
                    clickEvent: this.backToPage,
                    plain: 'false',
                    permission: "finance:invoicesDetail:back"
                },
            ],
            tableColumns: detailColumns(),
            loading: false,
            tableData: [],
            tableKey: 0,
            tablePage: pageConfig(),
        }
    },
    mounted() {
        this.searchList()
    },
    methods: {
        // 导出
        exportTable() {
            if (this.tableData.length === 0) {
                this.$message.warning('暂无数据可导出')
                return
            }
            const colName = this.tableColumns.map(item => item.field).join(',');
            const colNameDesc = this.tableColumns.map(item => item.title).join(',');
            const formInfo = {
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                settleNo: this.$route.query.settleNo
            }
            const params = {
                taskBean: 'FinanceSettleService_pageQueryDetail',
                colName,
                colNameDesc,
                moduleName: 'FINANCE',
                menuDesc: '结算单明细',
                exportParams: JSON.stringify(formInfo)
            }
            this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        },
        // 获取列表数据
        getList() {
            this.loading = true
            const params = {
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
                settleNo: this.$route.query.settleNo
            }
            getInvoicesDetailList(params).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.tableData = data.list
                    this.tablePage.pageNum = data.pageNum
                    this.tablePage.total = data.total
                    this.loading = false
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            })
        },
        // 获取列表数据
        searchList() {
            this.tablePage.pageNum = 1
            this.getList()
        },
        // 分页
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage
            this.tablePage.pageSize = pageSize
            this.getList()
        },
        //返回
        backToPage() {
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
        },
    }
}
</script>

<style></style>