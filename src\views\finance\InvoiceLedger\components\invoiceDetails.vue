<template>
    <div class="app-container">
        <xyy-panel title="发票录入">
            <div class="table-box">
                <vxe-table ref="xTable" :loading="xloading" highlight-current-row height="auto" :data="invoiceTableData"
                    :key="xtableKey" empty-text="暂无发票数据" :footer-method="footerMethod" show-footer
                    footer-cell-class-name="footer-cell-class-name">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in invoiceColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            <!-- 表头插槽，根据 required 属性添加红色星号 -->
                            <template v-slot:header="params">
                                <span>
                                    <span v-if="item.required" style="color: red;">*</span>
                                    {{ item.title }}
                                </span>
                            </template>
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'operation'">
                                    <span @click="downloadhandler(slotProps.row)" class="clickWriting">下载</span>
                                </div>
                                <div v-else-if="item.field == 'taxRate'">
                                    <span>{{ slotProps.row[item.field] }}%</span>
                                </div>
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
        </xyy-panel>
        <xyy-panel title="货单核对">
            <div class="table-box">
                <vxe-table ref="yTable" :loading="yloading" highlight-current-row height="auto" :data="ordersTableData" @cell-dblclick="cellDblclickEvent"
                    :key="ytableKey" empty-text="暂无货单数据" cell-class-name="yTable-cell">
                    <!-- <vxe-table-column type="checkbox" width="50" /> -->
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in ordersColumns" :key="item.field" :field="item.field"
                            :title="item.title" :min-width="item.width" :fixed="item.fixed">

                        <template v-slot:default="slotProps">
                                <div v-if="item.field == 'operation'">
                                    <span @click="detailhandler(slotProps.row)"
                                        v-if="slotProps.row.invoiceStatus == 1 || slotProps.row.invoiceStatus == 2"
                                        class="clickWriting">查看明细</span>
                                </div>
                                <div v-else-if="item.field == 'orderNo'">
                                <!-- {{ slotProps.row.sourceType === 3 || 4 ? '' : slotProps.row[item.field] }} -->
                                  <!-- {{ slotProps.row[item.field] }} -->
                                    {{ ((slotProps.row.sourceType === 3 || slotProps.row.sourceType === 4))? '' : slotProps.row[item.field] }}
                              </div>
                              <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                        </template>
                      </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
        </xyy-panel>
        <xyy-panel title="货单明细" >
          <div :key="tabKey" class="table-box">
              <vxe-table ref="xTable"
                :loading="detailLoading"
                highlight-current-row height="auto"
                :data="tableDataTwo"
                :key="tableKey1"
                empty-text="双击想查询的货单获取货单明细">
                  <vxe-table-column v-for="item in tableColumnsTwo" :key="item.field" :field="item.field" :title="item.title" :width="item.width" >
                    <template #header>
                      <div v-if="item.hit">
                        <el-tooltip placement="top">
                          <template slot="content">
                            <div v-for="(it,index) in item.hit" :key="index">
                              <div>{{it}}</div>
                            </div>
                          </template>
                          <div>
                            <span>{{item.title}}</span>
                            <span>
                              <svg-icon icon-class="prompt-icon"/>
                            </span>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        {{item.title}}
                      </div>
                    </template>
                    <template v-slot="{ row }">
                      <div v-if="item.field === 'orderTime'">
                        <!-- {{formatDate(row[item.field])}} -->
                      </div>
                      <div v-else-if="item.field == 'invoiceStatusName'">
                        <!-- <el-tag :type="invoiceStatusType(row)" style="cursor: pointer;">
                          {{row[item.field]}}
                        </el-tag> -->
                      </div>
                      <div v-else>
                        <span>{{ row[item.field] }}</span>
                      </div>
                      </template>
                  </vxe-table-column>
              </vxe-table>
          </div>
      </xyy-panel>
        <xyy-panel :titleShow="false">
            <div id="footer_box">
                <div id="form_box">
                    <div id="form_content">
                        汇总发票含税金额:
                        <span id="form_amount">{{ `￥${formData.invoiceAmount}` }}</span>
                    </div>
                    <div id="form_content">
                        含税金额差异:
                        <span id="form_amount">{{ `￥${formData.invoiceAmountDifference}` }}</span>
                    </div>
                    <div id="form_content">
                        汇总发票不含税金额:
                        <span id="form_amount">{{ `￥${formData.invoiceAmountNoTax}` }}</span>
                    </div>
                    <div id="form_content">
                        税额总计:
                        <span id="form_amount">{{ `￥${formData.fileTotalTax}` }}</span>
                    </div>
                </div>
                <div id="btn_box">
                    <!-- 按钮组 start-->
                    <!-- <btn-group slot="tools" :btn-list="btnListForm" /> -->
                    <div slot="tools">
                        <el-button type="success" v-hasPermi="['finance:invoiceDetails:cancelOrder']"
                            @click="cancelOrder" v-if="$route.query.invoiceStatus != 2">撤销申请</el-button>
                        <el-button v-hasPermi="['finance:invoiceDetails:back']" @click="backToPage">返回</el-button>
                    </div>
                </div>
            </div>
        </xyy-panel>
    </div>
</template>

<script>
import { getInvoiceDetailById, downloadInvoiceFile, cancelInvoice , getProductDetail} from '@/api/finance/InvoiceLedger'
import { invoiceColumns, ordersColumns,detailTwoColumns } from '../config'
export default {
    data() {
        return {
            detailLoading:false, //货单明细loading
            tableKey1:0,
            xloading: false,    //表格加载
            invoiceTableData: [],   //发票表格数据
            xtableKey: 0,   //发票表格刷新
            invoiceColumns: invoiceColumns(),   //发票表格列
            yloading: false,    //货单表格加载
            ordersTableData: [],   //货单表格数据
            ytableKey: 0,   //货单表格刷新
            ordersColumns: ordersColumns(),  //货单表格列
            tableColumnsTwo:detailTwoColumns() ,//货单详情
            // btnListForm: [
            //     {
            //         label: "撤销申请",
            //         clickEvent: this.cancelOrder,
            //         type: 'success',
            //         plain: 'false',
            //         permission: "finance:invoiceDetails:cancelOrder"
            //     },
            //     {
            //         label: "返回",
            //         clickEvent: this.backToPage,
            //         plain: 'false',
            //         permission: "finance:invoiceDetails:back"
            //     },
            // ],
            formData: {
                invoiceAmount: 0,  //发票含税金额
                invoiceAmountDifference: 0,  //含税金额差异
                invoiceAmountNoTax: 0,  //发票不含税金额
                fileTotalTax:0,  //税额总计
            },
            auditId: '',    //货单id
        }
    },
    mounted() {
        this.getInvoiceDetail()
    },
    methods: {
      //查询货单明细
    async  cellDblclickEvent ({ row, column }) {
      this.detailLoading = true
     try{
      const res = await getProductDetail({
        invoiceNo:row.invoiceNo,
      })
      if(res.code === 0){
        this.tableDataTwo = res.result
      }

     } catch(err){
       this.$message.error(err.message)
     } finally{
        this.tableKey1 ++
       this.detailLoading = false
     }

    },
        // 撤销申请
        cancelOrder() {
            let passArr = []
            let errArr = []
            let waitArr = []
            if (this.ordersTableData.length == 0) {
                this.$message.warning('订单明细为空，不能撤销申请')
                return
            }
            this.ordersTableData.forEach(item => {
                if (item.invoiceStatus == 2) {
                    passArr.push(item)
                }
                if (item.invoiceStatus == -1) {
                    errArr.push(item)
                }
                if (item.invoiceStatus == 0) {
                    waitArr.push(item)
                }
            })
            if (passArr.length > 0) {
                this.$message.warning('已经有审核通过的发票，不能撤销申请')
                return
            }
            if (waitArr.length > 0) {
                this.$message.warning('存在待录入的订单，不能撤销申请')
                return
            }
            if (errArr.length > 0) {
                this.$message.warning('存在录入失败的订单，不能撤销申请')
                return
            }
            const Loading = this.$loading({
                lock: true,
                text: '正在提交数据……',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
            })
            const params = new URLSearchParams()
            params.append('auditId', this.auditId)
            cancelInvoice(params).then(res => {
                const { msg, code } = res
                if (code === 0) {
                    this.$message.success(msg)
                    Loading.close()
                    this.backToPage()
                } else {
                    this.$message.error(msg)
                    Loading.close()
                }
            }).finally(() => {
                Loading.close()
            })
        },
        // 下载
        downloadhandler(row) {
            downloadInvoiceFile(row.imageUrl).then(res => {
                let blob = new Blob()
                if (row.imageUrl.includes('pdf')) {
                    blob = new Blob([res], { type: 'application/pdf' })
                } else {
                    blob = new Blob([res], { type: 'image/jpeg' })
                }
                const link = document.createElement('a')
                link.href = window.URL.createObjectURL(blob)
                link.download = `${row.taxNo}.${row.imageUrl.includes('pdf') ? 'pdf' : 'jpg'}`
                document.body.appendChild(link)
                link.click()
                window.URL.revokeObjectURL(link.href)
                document.body.removeChild(link)
            })
        },
        // 获取发票详情
        getInvoiceDetail() {
            this.xloading = true
            this.yloading = true
            const params = new URLSearchParams()
            params.append('invoiceNo', this.$route.query.invoiceNo)
            getInvoiceDetailById(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    if (result) {
                        this.formData.invoiceAmount = result.fileTotalAmount || 0
                        this.formData.invoiceAmountDifference = result.diff || 0
                        this.formData.invoiceAmountNoTax = result.fileTotalNoTax || 0
                        this.formData.fileTotalTax = result.fileTotalTax || 0
                        this.invoiceTableData = result.invoiceFileList || []
                        this.ordersTableData = result.invoiceList || []
                        this.auditId = result.auditId
                        this.xloading = false
                        this.yloading = false
                    } else {
                        this.xloading = false
                        this.yloading = false
                    }
                } else {
                    this.$message.error(msg)
                    this.xloading = false
                    this.yloading = false
                }
            }).finally(() => {
                this.xloading = false
                this.yloading = false
            })
        },
        // 返回
        backToPage() {
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
        },
        /**
        * 生成表格底部汇总行的方法。
        * @param {Array} param0.columns - 当前表格的列信息数组。
        * @param {Array} param0.data - 当前表格的数据数组。
        * @returns {Array} 返回包含汇总信息的数组。
        */
        footerMethod({ columns, data }) {
            // 存储汇总信息的数组
            let all = [];
            // 需要汇总的属性列表
            let propertys = [
                'auditNoTaxAmount',
                'auditAmount',
                'auditTax'
            ];
            // 调用自定义函数 blusFunction 来计算属性的汇总值
            let numObj = this.blusFunction(data, propertys);

            // 遍历表格的每一列
            columns.forEach((column, columnIndex) => {
                // 如果是第一列，添加汇总标题
                if (columnIndex === 0) {
                    all.push("汇总");
                } else {
                    // 获取对应属性的汇总值，如果没有则用 "-" 表示
                    let value = numObj[column.property] ? Number(numObj[column.property]).toFixed(2) : "-";
                    all.push(value);
                }
            });

            // 返回包含汇总信息的数组
            return [all];
        },

        /**
         * 计算给定数据数组中指定属性的汇总值。
         * @param {Array} data - 包含要计算的数据的数组。
         * @param {Array} propertys - 需要汇总的属性列表。
         * @returns {Object} 返回包含各属性汇总值的对象。
         */
        blusFunction(data, propertys) {
            let all = {}; // 存储汇总值的对象

            // 遍历数据数组中的每一项
            data.forEach((item) => {
                // 遍历属性列表中的每一个属性
                propertys.forEach((key) => {
                    // 如果汇总对象中不存在该属性，则初始化为0
                    if (!all[key]) {
                        all[key] = 0;
                    }
                    // 将当前数据项的属性值累加到汇总对象中
                    all[key] += item[key] * 1; // 乘以1确保属性值为数字
                });
            });

            return all; // 返回包含各属性汇总值的对象
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper {
    height: auto !important;
}

.detail ::v-deep .vxe-table--footer-wrapper.body--wrapper {
    overflow-x: hidden !important;
}

::v-deep .footer-cell-class-name,
::v-deep .yTable-cell {
    font-size: 14px !important;
    font-weight: 400;
}

#footer_box {
    position: relative;

    #form_box {
        display: flex;
        justify-content: start;
        margin: 23px;
        align-items: center;
        font-weight: bolder;
        font-size: 23px;

        #form_amount {
            margin-right: 23px;
        }
    }

    #btn_box {
        position: absolute;
        right: 0;
        bottom: 0;
    }
}
</style>