
<template>
  <xyy-dialog ref="xyyDialog" :title="title" width="30%" @close="close">
    <xyy-panel :titleShow="false">
      <el-upload
          action="xxx"
          ref="excludeImport"
          :http-request="uploadFile"
          :before-remove="removeImportData"

          :show-file-list="true"
          :limit="1"
          :before-upload="beforeImportData"
          accept=".xls, .xlsx, .XLS, .XLSX"
        >
          <el-button type="primary" size="small" style="margin-right: 10px">
            导入Excel文件
          </el-button>
          <el-button size="small" @click.stop="downloadTemplate" >下载模板</el-button>
        </el-upload>

      <div>
        <p>温馨提示：</p>
        <p>1、请按照模版要求录入信息后导入</p>
        <p>2、相同时间同一商品仅支持创建一个特价活动；</p>
        <p>3、商品状态必须为：“已上架”、“待上架”</p>
        <p>4、单个文件不可超过3M，最多可导入<span style="color: red;">200</span>个商品</p>
      </div>
    </xyy-panel>
    <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="handleBatchAudit">确 定</el-button>
      </span>
  </xyy-dialog>
</template>
<script>
import { importProduct } from '@/api/marketing/specialActive'
export default {
  name: 'BatchInput',
  data() {
    return {
      title: '批量导入',
      width: '500px',
      fileFormData: {},
      erpCodes:''
    }
  },
  methods: {
    open(erpCodes) {
      this.$refs.xyyDialog.open()
      this.erpCodes = erpCodes
    },
    close() {
      this.$refs.excludeImport.clearFiles()
      this.$refs.xyyDialog.close()
    },
    //批量导入
    uploadFile(params) {

      const { file } = params
      if (file) {
        this.fileFormData.file = file
      } else {
        this.$message.warning('请选择上传文件!')
      }
    },
    //清空上传列表
    removeImportData() {
      this.$refs.excludeImport.clearFiles()
      this.fileFormData = {}
    },
    beforeImportData(uploadInfo) {
      const isLt3M = uploadInfo.size / 1024 / 1024 < 3
      if (!isLt3M) {
        this.$message.warning('文件过大，最大支持3M!')
        return false
      }
      return true
    },
    downloadTemplate() {
      const a = document.createElement('a')
      a.href = 'https://oss-ec.ybm100.com/qdd/template/QDD特价活动导入模版.xlsx'
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
    handleBatchAudit() {
      if (!this.fileFormData.file) {
        this.$message.warning('请选择要上传的文件')
        return
      }
      const forms = new FormData()
      forms.append('file', this.fileFormData.file)
      forms.append('exists', this.erpCodes)
      const loading = this.$loading({
        lock: true,
        text: '正在导入文件',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      })
      importProduct(forms).then(res => {
        if(res.code === 0){
         if(res.result.failureNum === 0){
           this.$message({
            type: 'success',
            message: '导入成功'
          })
         }
        this.$emit('success',res.result)

        }else {
          this.$message({
            type: 'error',
            message: res.msg
          })
        }
      }).finally(() => {
        loading.close()
        this.close()
      })



    },
  }
}
</script>
<style scoped lang="scss">
</style>
