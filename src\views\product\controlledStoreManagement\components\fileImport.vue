<template>
    <xyy-dialog ref="dialogTableVisible" :title="title" width="500" height="200">
        <div style="display: flex; justify-content: space-around; width: 100%">
            <div style="display: flex; flex-direction: column">
                <div><span style="color: red">*</span><span>模版下载</span></div>
                <span @click="download">
                    <div class="xiazai">
                        <i class="el-icon-download" style="font-size: 100px"></i>
                        <div>下载模版</div>
                    </div>
                </span>
            </div>
            <div style="display: flex; flex-direction: column">
                <div><span style="color: red">*</span><span>上传文件</span></div>
                <el-upload class="upload-demo" drag ref="upload" action="#" :file-list="fileList"
                    :before-upload="beforeUpload" :on-change="onChange" accept=".xls,.xlsx" :auto-upload="false"
                    :http-request="uploadFile">
                    <i class="el-icon-upload2" style="font-size: 100px"></i>
                    <div class="el-upload__text">上传</div>
                    <div class="el-upload__tip" slot="tip">仅支持xls，xlsx，小于2M</div>
                </el-upload>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="submitUpload()" :loading="submitLoading">提 交</el-button>
        </span>
    </xyy-dialog>
</template>

<script>
import { downloadTemplate, importControlledStore } from "@/api/product/controlledStoreManagement";
import utils from "@/utils";
export default {
    name: "FileDialog",
    data() {
        return {
            title: "",
            show: false,
            listType: ["xls", "xlsx"],
            fileList: [],
            groupId: "",
            submitLoading: false,
        };
    },
    methods: {
        // 文件上传成功处理
        handleFileSuccess(response, file, fileList) {
            const { code, msg, result } = response;
            if (code == 1) {
                this.$message({
                    message: msg,
                    type: "error",
                });
            } else if (code == 0) {
                this.$message({
                    message: msg,
                    type: "success",
                });
            }
            // console.log(response, "mybaby1");
        },
        // 文件上传错误
        handleFileError(response, file, fileList) {
            this.$message({
                message: "上传文件失败",
                type: "error",
            });
        },
        download() {
            //下载模版
            downloadTemplate().then(res => {
                console.log(res);

                // 确定文件类型，这里假设是 Excel 文件
                const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                const blob = new Blob([res]);
                // 创建一个 URL 对象
                const url = window.URL.createObjectURL(blob);
                // 创建一个 <a> 标签
                const link = document.createElement('a');
                link.download = `批量导入药店模板.xlsx`;
                link.href = url;
                document.body.appendChild(link);
                link.click();
                // 释放 URL 对象
                window.URL.revokeObjectURL(url);
                document.body.removeChild(link);
            })
        },
        submitUpload() {
            //点击上传
            this.$refs.upload.submit();
        },
        uploadFile(param) {
            console.log("param", param);
            const formData = new FormData();
            formData.append("file", param.file);
            formData.append('groupId', this.groupId)
            if (formData.get("file") == null) {
                this.$message.error("请选择文件");
                return;
            }
            this.submitLoading = true;
            importControlledStore(formData).then((res) => {
                const { code, msg, result } = res;
                if (code === 0) {
                    if (result.error > 0) {
                        const h = this.$createElement;
                        this.$msgbox({
                            title: '提示',
                            message: h('div', null, [
                                h('div', null, `${result.success}个导入成功，${result.error}个导入失败,失败原因请下载错误文件：`),
                                h('div', {
                                    style: 'margin-top: 10px; color: red; cursor: pointer;', on: {
                                        click: () => {
                                            window.open(result.errorFileUrl, '_self');
                                        }
                                    }
                                }, `${result.errorFileName}`)
                            ]),
                            showCancelButton: true,
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                        }).then(() => {
                            // window.open(result.errorFileUrl, '_self');
                            this.close();
                            this.$emit("on-success");
                        }).catch(() => {
                        })
                    } else {
                        this.$confirm(`${result.success}个导入成功，${result.error}个导入失败。`, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        }).then(() => {
                            this.close();
                            this.$emit("on-success");
                        }).catch(() => {
                        })
                    }
                } else {
                    this.$message.error(res.msg || '提交失败')
                }
            }).finally(() => {
                this.submitLoading = false;
            });
        },
        onChange(file, fileList) {
            // 通过这个判断截取长度
            console.log(fileList);
            if (fileList.length > 1) {
                fileList.splice(0, 1);
            }
        },
        beforeUpload(file) {
            let testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
            let flag = this.listType.includes(testmsg);
            if (!flag) {
                this.$message({
                    message: "上传文件只能是 xls、xlsx格式!",
                    type: "warning",
                });
                return false;
            }
            if (file.size > 2 * 1024 * 1024) {
                this.$message({
                    message: "上传文件不能超过2M",
                    type: "warning",
                });
                return false;
            }
            return true;
        },
        //弹窗打开
        open(data) {
            this.title = "批量导入";
            this.show = true;
            this.groupId = data
            this.$refs.dialogTableVisible.open();
        },
        // 弹窗关闭
        close() {
            this.$refs.dialogTableVisible.close();
            this.fileList = [];
            this.show = false;
        },
        handleClose() {
            this.$emit("on-before-close");
        },
    },
};
</script>

<style scoped>
.xiazai {
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 300px;
    height: 180px;
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
</style>
