<template>
  <div class="clearfix">
    <el-row>
      <template v-for="item in btnList">
        <button v-if="item.shortkey && item.permission" :key="item.label"
          v-hasPermi="[item.permission]"
          :disabled="item.disabled || item.loading" :loading="item.loading" :icon="item.icon" type="button"
          :class="{ btn: true, 'is-loading': item.loading }" @click="item.clickEvent(item)">
          <span class="sMag">{{ item.label }}</span>
          <span class="sMag">{{ item.shortkey }}</span>
          <i v-if="item.loading" class="btn-loading el-icon-loading" />
        </button>
        <button v-else-if="item.shortkey" :key="item.label"
          v-hasPermi="[item.permission]"
          :disabled="item.disabled || item.loading" :loading="item.loading" :icon="item.icon" type="button"
          :class="{ btn: true, 'is-loading': item.loading }" @click="item.clickEvent(item)">
          <span class="sMag">{{ item.label }}</span>
          <span class="sMag">{{ item.shortkey }}</span>
          <i v-if="item.loading" class="btn-loading el-icon-loading" />
        </button>
        <div v-else-if="item.showForm" :key="item.showForm" style="display: inline;" @click="()=>{item.clickEvent(item);item.show=!item.show}">
          <span class="trigger clickWriting"  style="font-size:14px;" >
            {{ item.show ? ' 展开' : '收起' }}
          </span>
          <i :class="item.show ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" class="clickWriting"></i>
        </div>
        <el-button v-else-if="item.permission" :key="item.label" :type="item.type" size="small"
          v-hasPermi="[item.permission]"
          :disabled="item.disabled" :loading="item.loading" 
          :plain="item.plain=='false' ? false : true"  
          class="el-btn" @click="item.clickEvent(item)">
          <svg-icon v-if="item.icon" :icon-class="item.icon"></svg-icon>
          {{ item.label }}</el-button>
        <el-button v-else :key="item.label" :type="item.type" size="small"
          :disabled="item.disabled" :loading="item.loading" 
          :plain="item.plain=='false' ? false : true"  
          class="el-btn" @click="item.clickEvent(item)">
          <svg-icon v-if="item.icon" :icon-class="item.icon"></svg-icon>
          {{ item.label }}</el-button>
      </template>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "BtnGroup",
  props: {
    btnList: {
      // 按钮列表配置
      type: Array,
      default: () => [],
    },
    useEvent: {
      // 是否启用按钮组快捷键
      type: Boolean,
      default: false,
    },
    excuseEvent: {
      // 是否执行快捷键事件
      type: Boolean,
      default: true,
    },
  },
  mounted() {
    // console.log(this.btnList,'btnList');
    this.replaceIcon()
    if (!this.useEvent) return false;
    // const hotkeys = this.getHotkeys().toLowerCase();
    // this.$hotkeys(hotkeys, this.handlerKeys);
  },
  beforeDestroy() {
    // 销毁快捷键
    // const hotkeys = this.getHotkeys().toLowerCase();
    // this.$hotkeys.unbind(hotkeys);
  },
  methods: {
    getBtnPermissionFunc(code) {
      // code value is test will complete allow
      if(code=="test"){
        return true
      }
      if (!this.$route.meta.buttonList) {
        return false;
      }
      const permissions = this.$route.meta.buttonList.map(item => {
        return item.code;
      });
      const index = permissions.indexOf(code);
      return index !== -1;
    },
    handlerKeys(event, handler) {
      event.preventDefault();
      const key = handler.key;
      this.btnList.forEach((el) => {
        if (el.shortkey.toLowerCase() === key && this.excuseEvent) {
          el.clickEvent(el, event);
        }
      });
    },
    getHotkeys() {
      const hotkeys = [];
      this.btnList.forEach((el) => {
        if (el.shortkey) {
          hotkeys.push(el.shortkey);
        }
      });
      return hotkeys.join(",");
    },
    replaceIcon() {
      this.btnList.forEach(item => {
        item = this.matchIcon(item)
      })
    },
    matchIcon(item) {
      switch (item.icon) {
        case 'el-icon-search':
          item.icon = 'headchaxun1'
          break;
        case 'el-icon-download':
          item.icon = 'headxiazai'
          break;
        case 'el-icon-plus':
          item.icon = 'headxinzeng1'
          break;
        case 'el-icon-delete':
          item.icon = 'headshanchu'
          break;
        case 'el-icon-setting':
          item.icon = 'headshezhi'
          break;
        case 'el-icon-circle-plus-outline':
          item.icon = 'headxinzeng'
          break;
        case 'el-icon-sort':
          item.icon = 'headpaixu1'
          break;
        case 'el-icon-check':
          item.icon = 'headqueren'
          break;
        case 'el-icon-close':
          item.icon = 'headquxiao1'
          break;
        case 'el-icon-view':
          item.icon = 'headchakan'
          break;
        case 'el-icon-printer':
          item.icon = 'headdayinji_o'
          break;
        case 'el-icon-refresh-left':
          item.icon = 'headshuaxin'
          break;
        case 'el-icon-refresh-right':
          item.icon = 'headshuaxin'
          break;
        case 'el-icon-copy-document':
          item.icon = 'headfuzhi_o'
          break;
        case 'el-icon-s-promotion':
          item.icon = 'headwodetijiao'
          break;
        case 'el-icon-s-grid':
          item.icon = 'headcaidan'
          break;
        case 'el-icon-edit-outline':
          item.icon = 'headxiugai'
          break;
        case 'el-icon-share':
          item.icon = 'headfenxiang'
          break;
        case 'el-icon-finished':
          item.icon = 'headicon-test'
          break;
        case 'el-icon-edit':
          item.icon = 'headbianji'
          break;
        case 'el-icon-set-up':
          item.icon = 'headshezhi1'
          break;
        case 'el-icon-bottom-right':
          item.icon = 'headdownrightvector'
          break;
        case 'el-icon-s-opportunity':
          item.icon = 'headremind-s'
          break;
        case 'el-icon-s-claim':
          item.icon = 'headgongdanqueren'
          break;
        case 'el-icon-upload2':
          item.icon = 'headshangchuan'
          break;
        case 'el-icon-arrow-left':
          item.icon = 'headfanhui'
          break;
        case 'el-icon-d-caret':
          item.icon = 'headpaixu3'
          break;
        case 'el-icon-back':
          item.icon = 'headfanhui1'
          break;
        case 'el-icon-refresh':
          item.icon = 'headshuaxin'
          break;

        default:
          break;
      }
      return item
    },
  },
};
</script>

<style lang="scss" scoped>
.clearfix {
  float: right;

  .btn {
    position: relative;
    height: 32px;
    color: #fff;
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    vertical-align: top;
    touch-action: manipulation;
    cursor: pointer;
    border: 1px solid transparent;
    white-space: nowrap;
    background-color: #2db7f5;
    border-color: #2db7f5;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.42857143;
    border-radius: 4px;
    font-size: 14px;
    min-width: 54px;
    padding: 0 12px;
    margin-right: 10px;

    .btn-loading {
      position: relative;
      top: -18px;
      color: #2db7f5;
    }

    &.is-loading::before {
      pointer-events: none;
      content: "";
      position: absolute;
      left: -1px;
      top: -1px;
      right: -1px;
      bottom: -1px;
      border-radius: inherit;
      background-color: rgba(0, 0, 0, 0.6);
    }

    &:disabled {
      cursor: not-allowed;
      background-color: #a0cfff;
      border-color: #a0cfff;
    }

    &:hover {
      background-color: #34a4d7;
    }

    &:last-child {
      margin-right: 0px;
    }

    .sMag {
      display: inline-block;
      height: 15px;
      line-height: 15px;
      font-size: 12px;
    }
  }

  .el-button {
    margin-right: 10px;
  }
}
</style>
