<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商户名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入商户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in dict.type.sys_normal_disable"  :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="联系人" prop="contactName">
        <el-input
          v-model="queryParams.contactName"
          placeholder="请输入联系人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="过期时间" prop="expireTime">
        <el-date-picker clearable
                        v-model="queryParams.expireTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择过期时间">
        </el-date-picker>
      </el-form-item>
       <el-form-item label="渠道编码" prop="channelCodes">
        <el-input
          v-model="queryParams.channelCodes"
          placeholder="请输入渠道编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:tenant:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:tenant:edit']"
        >修改</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:tenant:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
        <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="el-icon-upload2"
              size="mini"
              @click="handleImport"
              v-hasPermi="['system:user:import']"
            >导入</el-button>
          </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:tenant:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tenantList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" alig  n="center" prop="id" />
      <el-table-column label="商户名称" align="center" prop="name" width="220"/>
      <el-table-column label="渠道编码" align="center" prop="channelCodes" width="150"/>
      <el-table-column label="入仓机构" align="center" prop="orgCodes" width="150"/>
      <el-table-column label="供应商编码" align="center" prop="supplierCode" width="150"/>
      <el-table-column label="供应商地址" align="center" prop="supplierAddress" width="150"/>
      <el-table-column label="联系人" align="center" prop="contactName" />
      <el-table-column label="联系手机" align="center" prop="contactMobile" width="150"/>
      <el-table-column label="租户状态" align="center" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="绑定域名" align="center" prop="domain" /> -->
      <el-table-column label="套餐编号" align="center" prop="packageId" >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.packageId === 0" type="danger">系统租户</el-tag>
          <el-tag v-else> {{getPackageName(scope.row.packageId)}} </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="过期时间" align="center" prop="expireTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号数量" align="center" prop="accountCount" />
      <!-- <el-table-column label="创建者" align="center" prop="creator" /> -->
      <!-- <el-table-column label="更新者" align="center" prop="updater" /> -->
      <!--<el-table-column label="是否删除" align="center" prop="deleted" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleAddChild(scope.row)"
            v-hasPermi="['system:tenant:addChild']"
            v-if="scope.row.id >1"
          >+子账号</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:tenant:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:tenant:remove']"
            v-if="scope.row.id >1"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改租户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商户名称" prop="name">
          <!-- <el-input v-model="form.name" placeholder="请输入商户名称" /> -->
          <el-autocomplete
            clearable
            class="inline-input"
            v-model.trim="form.name"
            :fetch-suggestions="querySearch"
            placeholder="请输入商户名称"
            @select="handleSelect"
            style="width: 100%;"
          ></el-autocomplete>
        </el-form-item>
        <!-- <el-form-item label="渠道编码" prop="channelCodes">
          <el-input v-model="form.channelCodes" placeholder="请输入渠道编码" />
        </el-form-item> -->
        <el-form-item label="供应商编码" prop="supplierCode">
          <el-input v-model="form.supplierCode" placeholder="请输入供应商编码" disabled/>
        </el-form-item>
        <el-form-item label="供应商地址" prop="supplierAddress">
          <el-input v-model="form.supplierAddress" placeholder="请输入供应商地址" disabled/>
        </el-form-item>
        <el-form-item label="联系人" prop="contactName">
          <el-input v-model="form.contactName" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系手机" prop="contactMobile">
          <el-input v-model="form.contactMobile"  placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="入仓机构" prop="domain">
          <!-- <el-input v-model="form.orgCodes" placeholder="入仓机构，多个逗号分隔" /> -->
          <el-select v-model="form.orgCodesArr" placeholder="请选择入仓机构" clearable multiple size="small">
            <el-option v-for="item in orgList" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
        </el-form-item>
        <!-- <el-form-item label="租户套餐" prop="packageId">
          <el-select v-model="form.packageId" placeholder="请选择租户套餐" clearable size="small">
            <el-option v-for="item in packageList" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="过期时间" prop="expireTime">
          <el-date-picker clearable
                          v-model="form.expireTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择过期时间">
          </el-date-picker>
        </el-form-item> -->
        <!-- <el-form-item label="账号数量" prop="accountCount">
          <el-input-number v-model="form.accountCount" placeholder="请输入账号数量" />
        </el-form-item> -->
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

     <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <!-- <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div> -->
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 新增子账号 -->
    <addChild ref="addChild" @success="handleAddChildSuccess"></addChild>
    <!-- 添加成功 -->
    <addSuccess ref="addSuccess" ></addSuccess>
  </div>
</template>

<script>
import { listTenant, getTenant, delTenant, addTenant, updateTenant,querySupplier } from "@/api/system/tenant";
import { getTenantPackageList } from "@/api/system/package";
import { getToken } from "@/utils/auth";
import addChild from "./components/addChild.vue";
import addSuccess from "./components/addSuccess.vue";
import axios from 'axios'
export default {
  name: "Tenant",
  dicts: ['sys_normal_disable'],
  components: {
    addChild,
    addSuccess
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 租户表格数据
      tenantList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //租户套餐
      packageList:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        contactUserId: undefined,
        contactName: undefined,
        contactMobile: undefined,
        status: undefined,
        domain: undefined,
        packageId: undefined,
        expireTime: undefined,
        accountCount: undefined,
        supplierAddress:undefined,
        supplierCode:undefined
      },
        // 租户导入参数
      upload: {
        // 是否显示弹出层（租户导入）
        open: false,
        // 弹出层标题（租户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/tenant/importData"
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "商户名称不能为空", trigger: "blur" }
        ],
        contactName: [
          { required: true, message: "联系人不能为空", trigger: "blur" }
        ],
        contactMobile: [
          { required: true, message: "联系手机号不能为空", trigger: "blur" }
        ],
        // channelCodes: [
        //   { required: true, message: "渠道编码不能为空", trigger: "blur" }
        // ],
        orgCodes: [
          { required: true, message: "入仓机构不能为空", trigger: "blur" }
        ],
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "租户状态不能为空", trigger: "blur" }
        ],
        expireTime: [
          { required: true, message: "过期时间不能为空", trigger: "blur" }
        ],
        creator: [
          { required: true, message: "创建者不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ],
        deleted: [
          { required: true, message: "是否删除不能为空", trigger: "blur" }
        ],
         supplierCode: [
          { required: true, message: "供应商编码不能为空", trigger: "blur" }
        ],
         supplierAddress: [
          { required: true, message: "供应商地址不能为空", trigger: "blur" }
        ],
        orgCodesArr: [
          { type: "array", required: true, message: "入仓机构不能为空", trigger: "change" }
        ],
      },

      //模糊搜索list
      supplierList:[],

      //模糊搜索词
      searchText: null,

      // 模糊搜索选择的供应商名称
      tenantName: null,
      //入仓机构列表
      orgList: [
        {
          label: '武汉仓',
          value: '002'
        },
        {
          label: '重庆仓',
          value: '003',
        },
        {
          label: '杭州仓',
          value: '005'
        },
        {
          label: '长沙仓',
          value: '007'
        },
        {
          label: '济南仓',
          value: '008'
        },
      ],
    };
  },
  created() {
    this.getList();

    // 获得租户套餐列表
    getTenantPackageList().then(response => {
      this.packageList = response.data;
    })
  },
  methods: {
    // 输入商户名称自动补全
    querySearch(str,cb){
      // 未输入不发请求
      if(!str) {
        return cb([]);
      }
      // 输入相同的值不发请求
      if(this.searchText === str) {
        return cb(this.supplierList);
      }else{
        this.searchText = str;
        // 重新输入后清空供应商编码和地址
        this.tenantName = null;
        this.$set(this.form, 'supplierCode', null);
        this.$set(this.form, 'supplierAddress', null);

      }

      const params = {
        supplierName: str,
        auditStatus:2
      }
      querySupplier(params).then(res => {
        if(res.code === 200) {
          const data = res.data.list.map(item => {
            return {
              value: item.supplierName,
              label: item.supplierName,
              supplierCode: item.supplierCode,
              repertoryAddress: item.repertoryAddress,
              id: item.id,
            }
          })
          this.supplierList = data;
          cb(data)
        }else{
          cb([])
        }
      })


    },
    // 选择商户
    handleSelect(item){
      // console.log(item);

      this.tenantName = item.value;
      this.searchText = item.value;
      this.$set(this.form, 'supplierCode', item.supplierCode);
      this.$set(this.form, 'supplierAddress', item.repertoryAddress);
    },
    /** 查询租户列表 */
    getList() {
      this.loading = true;
      listTenant(this.queryParams).then(response => {
        this.tenantList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        contactUserId: null,
        contactName: null,
        contactMobile: null,
        status: undefined,
        domain: null,
        packageId: null,
        expireTime: null,
        accountCount: null,
        creator: null,
        createTime: null,
        updater: null,
        updateTime: null,
        channelCodes: null,
        orgCodes: null,
        deleted: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加租户";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTenant(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改租户";
        this.$set(this.form, 'orgCodesArr', this.form.orgCodes.split(","))
      });
    },
    // 添加子账号
    handleAddChild(row) {
     this.$refs.addChild.open(row);

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            this.form.orgCodes = this.form.orgCodesArr.join(",");
            updateTenant(this.form).then(response => {
              if (response.code == 500) {
                this.$message.error(response.msg);
                return;
              }
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();

            });
          } else {
            this.form.orgCodes = this.form.orgCodesArr.join(",");
            addTenant(this.form).then(response => {
              if (response.msg === 'openChildUser') {
                this.$confirm('请确认是否需要为该供应商创建子账号', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                }).then(() => {
                  this.open = false;
                  this.$refs.addChild.open({id:response.data.id, name:this.tenantName},this.form);
                }).catch(() => {});
              }else{
                if (response.code == 500) {
                  this.$message.error(response.msg);
                  return;
                }else{
                  this.open = false;
                  const unOpenOrg = response.data.unOpenOrgCodeNames ? response.data.unOpenOrgCodeNames : [];
                  const data ={
                    title: `添加成功`,
                    data: [response.data],
                    unOpenOrg,
                  }
                  console.log(data);

                  this.$refs.addSuccess.open(data)

                  this.getList();
                }
              }

            });
          }
        }
      });
    },
    // 成功新增子账号
    handleAddChildSuccess(res){
      this.getList()
      const data = res.data.map(item => {
        return {
          name : item.tenantName,
          channelCodes : item.channelCode,
          orgCodes : item.orgCodes,
          contactMobile: item.contactMobile,
        }
      })

      const dataInfo ={
        title: `添加成功`,
        data,
        unOpenOrg: [],
      }
      // console.log(dataInfo);

      this.$refs.addSuccess.open(dataInfo)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除租户编号为"' + ids + '"的数据项？').then(function() {
        return delTenant(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
        /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "租户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/tenant/importTemplate', {
      }, `tenant_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      if(response.msg !== '操作成功'){
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg.replace(/\r\n/g, "<br>") + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      } else{
        const unOpenOrg = response.data
          .filter(item => Array.isArray(item.unOpenOrgCodeNames) && item.unOpenOrgCodeNames.length > 0)
          .map(item => ({
            name: item.name,
            unOpenOrgCodeNames: item.unOpenOrgCodeNames.join()
          }));
          console.log(unOpenOrg);

        const data ={
          title: `导入成功${response.data.length}条`,
          data: response.data,
          unOpenOrg,
        }
        this.$refs.addSuccess.open(data)
      }

      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/tenant/export', {
        ...this.queryParams
      }, `tenant_${new Date().getTime()}.xlsx`)
    },
    /** 套餐名格式化 */
    getPackageName(packageId) {
      for (const item of this.packageList) {
        if (item.id === packageId) {
          return item.name;
        }
      }
      return '未知套餐';
    }
  }
};
</script>
