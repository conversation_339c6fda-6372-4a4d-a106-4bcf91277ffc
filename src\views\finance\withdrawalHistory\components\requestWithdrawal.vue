<template>
    <div class="app-container">
        <xyy-panel :titleShow="false">
            <div slot="tools">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; ">
                    <div>
                        <div id="total">
                            <div id="total_content">
                                <div id="total_taxt">账户余额
                                    <el-tooltip content="结算单金额减已提现金额的剩余账号余额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${balanceAccount}` }}</div>
                            </div>
                            <div id="total_content">
                                <div id="total_taxt">发票待核销总金额
                                    <el-tooltip content="发票状态为已录入下的待核销发票金额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${verificationAmount}` }}</div>
                            </div>
                            <div id="total_content">
                                <div id="total_taxt">可提现金额
                                    <el-tooltip content="在账户余额和发票待核销总金额之间取最小金额为可提现金额" placement="top">
                                        <span class="el-icon-question"></span>
                                    </el-tooltip>
                                </div>
                                <div id="total_aomunt">{{ `￥${WithdrawableAmount}` }}</div>
                            </div>
                            <!-- <div id="total_content">
                        <div id="total_taxt">本月提现</div>
                        <div id="total_aomunt">{{ `￥${monthPayouts}` }}</div>
                    </div> -->
                            <!-- <div id="total_content">
                        <div id="total_taxt">已提现金额</div>
                        <div id="total_aomunt">{{ `￥${withdrawnAmount}` }}</div>
                    </div> -->
                        </div>
                    </div>
                    <div>
                        <el-button type="primary" v-hasPermi="['finance:requestWithdrawal:submitWithdrawal']"
                            @click="submitWithdrawal">提交申请</el-button>
                        <el-button v-hasPermi="['finance:requestWithdrawal:back']" @click="backToPage">返回</el-button>
                    </div>
                </div>
            </div>
        </xyy-panel>
        <xyy-panel title="核对信息">
            <div id="warning_tips">
                <span id="warning_tips_icon" class="el-icon-warning"></span>
                请确认银行信息是否有误，如果不正确请及时联系对接人员做修改
            </div>
            <div id="bank_info">
                <el-form ref="formInfo" :model="formInfo" :rules="infoRules" label-width="120px" class="clearfix">
                    <el-row :gutter="20">
                        <el-col :lg="6" :md="6">
                            <el-form-item label="提现申请单号" prop="withdrawNo">
                                <el-input v-model="formInfo.withdrawNo" placeholder="请输入提现申请单号" disabled></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :lg="6" :md="6">
                            <el-form-item label="申请时间" prop="applyDate">
                                <el-input v-model="formInfo.applyDate" placeholder="请输入申请时间" disabled></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :lg="6" :md="6">
                            <el-form-item label="开户行" prop="openBank">
                                <el-select v-model="formInfo.tenantBankId" placeholder="请选择开户行" @change="bankChange"
                                    clearable>
                                    <el-option v-for="item in bankList" :key="item.id" :label="item.label"
                                        :value="item.id"> </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :lg="6" :md="6">
                            <el-form-item label="收款人" prop="receiveName">
                                <el-input v-model="formInfo.receiveName" placeholder="请输入收款人" disabled></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :lg="6" :md="6">
                            <el-form-item label="银行账号" prop="bankAccount">
                                <el-input v-model="formInfo.bankAccount" placeholder="请输入银行账号" disabled></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </xyy-panel>
        <xyy-panel title="填写提现金额">
            <div id="withdrawal_amount">
                <div id="withdrawal_amount_content">
                    <div id="withdrawal_amount_content_left">
                        <div id="withdrawal_amount_content_left_taxt">本次申请提现金额</div>
                        <div id="withdrawal_amount_content_left_aomunt">
                            <el-input v-model="amountForm.withdrawAmountNow" placeholder="请输入提现金额"
                                @input="withdrawAmountNowInput($event)" @change="withdrawAmountNowChange">
                                <template slot="prepend">￥</template>
                            </el-input>
                        </div>
                        <div id="withdrawal_amount_content_left_button">
                            <el-button type="primary" @click="submitAmount">全部提现</el-button>
                        </div>
                    </div>
                    <div id="withdrawal_amount_content_right">
                        <el-card shadow="never" style="border: none;">
                            <div id="withdrawal_amount_content_right_taxt">
                                本次申请核销金：{{ `￥${writeOffAmount}` }}
                                <span id="withdrawal_amount_content_right_button"
                                    class="el-icon-refresh-right clickWriting" @click="recoveryData">一键恢复</span>
                            </div>
                            <div>
                                <span id="withdrawal_amount_content_right_warning_tips"
                                    class="el-icon-warning-outline">本次申请核销金额需等于本次申请提现金额才可以提现</span>
                            </div>
                        </el-card>
                    </div>
                </div>
            </div>
        </xyy-panel>
        <xyy-panel title="核对发票">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTable" />
            <div id="table_form">
                <el-form ref="tableFormRef" :model="tableForm" label-width="120px" class="clearfix">
                    <el-row :gutter="20">
                        <el-col :lg="6" :md="6">
                            <el-form-item label="业务单号">
                                <el-input v-model="tableForm.sourceNo" placeholder="请输入入库单/退货单/退补价单号"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :lg="12" :md="12">
                            <el-form-item label="单据日期">
                                <el-date-picker v-model="tableForm.orderDate" type="daterange" range-separator="至"
                                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :lg="6" :md="6">
                            <el-form-item label="单据类型">
                                <el-select v-model="tableForm.sourceType" placeholder="请选择单据类型" clearable>
                                    <el-option v-for="item in orderTypeList" :key="item.value" :label="item.label"
                                        :value="item.value"> </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" empty-text="暂无核对发票数据" @checkbox-change="selectChangeEvent"
                    @checkbox-all="selectAllEvent" :checkbox-config="{ highlight: true }">
                    <vxe-table-column type="checkbox" width="60" />
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'amount'">
                                    <el-input v-model="slotProps.row[item.field]" placeholder="请输入核销金额"
                                        @input="writeOffAmountInput($event, slotProps.row)"
                                        suffix-icon="el-icon-edit-outline"
                                        @change="writeOffAmountChange(slotProps.row)"> </el-input>
                                </div>
                                <div v-else-if="item.field == 'invoiceStatusName'">
                                    <el-tag type="info" v-if="slotProps.row.invoiceStatus == -1"
                                        style="font-size: 14px;">{{
                                            slotProps.row.invoiceStatusName }}</el-tag>
                                    <el-tag type="warning" v-else-if="slotProps.row.invoiceStatus == 0"
                                        style="font-size: 14px;">{{ slotProps.row.invoiceStatusName }}</el-tag>
                                    <el-tag type="primary" v-else-if="slotProps.row.invoiceStatus == 1"
                                        style="font-size: 14px;">{{ slotProps.row.invoiceStatusName }}</el-tag>
                                    <el-tag type="success" v-else-if="slotProps.row.invoiceStatus == 2"
                                        style="font-size: 14px;">{{ slotProps.row.invoiceStatusName }}</el-tag>
                                </div>
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
        </xyy-panel>
    </div>

</template>

<script>
import { getBankAccount } from "@/api/system/user";
import { getWithdrawalHistorySummary, getWithdrawalHistoryInvoice, getWithdrawalHistoryNo, submitWithdrawalHistory } from "@/api/finance/withdrawalHistory";
import { requestWithdrawalColumns } from '../config'
import XEUtils from 'xe-utils'
const end = new Date();
const start = XEUtils.getWhatDay(end, -30); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd'); // 将结束日期格式化为字符串
export default {
    name: 'RequestWithdrawal',
    data() {
        return {
            btnListTable: [
                {
                    label: "查询",
                    clickEvent: this.searchList,
                    type: 'primary',
                    plain: 'false',
                    permission: "finance:requestWithdrawal:query"
                },
            ],
            balanceAccount: 0,  //账户余额
            verificationAmount: 0,  //发票待核销总金额
            WithdrawableAmount: 0,  //可提现金额
            formInfo: { // 提现申请单信息
                withdrawNo: '', // 提现单号
                applyDate: XEUtils.toDateString(new Date(), 'yyyy-MM-dd HH:mm:ss'),  // 申请日期
                openBank: '',   // 开户行
                receiveName: '',    // 收款人
                bankAccount: '',    // 银行卡号
                tenantBankId: ''    // 开户行id
            },
            tableForm: {    // 表单数据
                sourceNo: '',    // 业务单号
                orderDate: [defaultBeginTime, defaultEndTime],  // 供货单日期（自动核销上线后恢复）
                sourceType: '',  // 单据类型
            },
            orderTypeList: [
                {
                    value: '',
                    label: '全部'
                },
                {
                    value: 1,
                    label: '采购入库'
                },
                {
                    value: 2,
                    label: '采购入库退货'
                },
                {
                    value: 3,
                    label: '退补价入'
                },
                {
                    value: 4,
                    label: '退补价出'
                },
            ],  //单据类型列表
            amountForm: {
                withdrawAmountNow: '',
            },
            bankList: [],   // 开户行列表
            infoRules: {
                openBank: [
                    { required: true, message: '请选择开户行', trigger: 'change' },
                ],
            },
            writeOffAmount: '0.00',  //本次申请核销金
            loading: false, // 加载中
            tableData: [],  // 表格数据
            tableKey: 0,    // 表格刷新
            tableColumns: requestWithdrawalColumns(),   // 表格列
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.searchList()
            this.apiGetWithdrawalHistorySummary()
            this.apiGetBankAccount()
            this.apiGetWithdrawalHistoryNo()
        })
    },
    activated() {
        this.$nextTick(() => {
            this.apiGetBankAccount()
        })
    },
    methods: {
        // 获取提现申请单
        apiGetWithdrawalHistoryNo() {
            getWithdrawalHistoryNo().then(response => {
                const { code, msg, data } = response;
                if (code === 200) {
                    this.formInfo.withdrawNo = data
                } else {
                    this.$message.error(msg)
                }
            })
        },
        // 开户行选择
        bankChange(value) {
            if (value) {
                this.formInfo.openBank = this.bankList.find(item => item.id == value).value.openBank
                this.formInfo.bankAccount = this.bankList.find(item => item.id == value).value.bankAccount
                this.formInfo.receiveName = this.bankList.find(item => item.id == value).value.receiveName
            } else {
                this.formInfo.openBank = ''
                this.formInfo.bankAccount = ''
                this.formInfo.receiveName = ''
            }
        },
        // 获取开户行
        apiGetBankAccount() {
            getBankAccount().then(response => {
                const { code, msg, data } = response;
                if (code === 200) {
                    this.bankList = data.map(item => {
                        return {
                            value: item,
                            label: item.openBank,
                            id: item.id
                        }
                    })
                } else {
                    this.$message.error(msg)
                }
            });
        },
        // 获取提现申请单发票信息
        getList() {
            this.loading = true
            const params = {
                ...this.tableForm,
                orderTimeStart: this.tableForm.orderDate ? this.tableForm.orderDate[0] : '',
                orderTimeEnd: this.tableForm.orderDate ? this.tableForm.orderDate[1] : '',
                pageNum: 1,
                pageSize: ********
            }
            delete params.orderDate
            getWithdrawalHistoryInvoice(params).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.tableData = data.list || []
                    this.loading = false
                    this.tableKey = this.tableKey + 1
                } else {
                    this.$message.error(msg)
                    this.loading = false
                }
            })
        },
        // 查询
        searchList() {
            this.getList()
        },
        // 提交申请
        submitWithdrawal() {
            this.$refs.formInfo.validate((valid) => {
                if (valid) {
                    const selectRows = this.$refs.xTable.getCheckboxRecords()
                    if (selectRows.length === 0) {
                        this.$message.warning('请选择要提现的发票的数据')
                        return
                    }
                    let empty = []
                    for (let i = 0; i < selectRows.length; i++) {
                        if (!selectRows[i].amount) {
                            empty.push(this.tableData.indexOf(selectRows[i]) + 1)
                        }
                    }
                    if (empty.length > 0) {
                        this.$message.warning(`请填写第${empty.join(',')}行的本次核销金额`)
                        return
                    }
                    if (Number(this.writeOffAmount) != Number(this.amountForm.withdrawAmountNow)) {
                        this.$message.warning('本次核销金额需等于本次申请提现金额,请重新填写')
                        return
                    }
                    const Loading = this.$loading({
                        lock: true,
                        text: '正在提交中',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    const detailList = selectRows.map(item => {
                        return {
                            invoiceNo: item.invoiceNo,
                            amount: Number(item.amount)
                        }
                    })
                    const params = {
                        withdrawNo: this.formInfo.withdrawNo,
                        amount: Number(this.amountForm.withdrawAmountNow),  //本次提现总金额
                        tenantBankId: this.formInfo.tenantBankId,   // 开户行id
                        openBank: this.formInfo.openBank,   //开户行
                        receiveName: this.formInfo.receiveName, //收款人
                        bankAccount: this.formInfo.bankAccount, //银行账号
                        detailList: detailList, //提现明细
                    }
                    submitWithdrawalHistory(params).then(res => {
                        const { code, msg } = res
                        if (code === 200) {
                            this.$message.success(msg)
                            Loading.close()
                            this.backToPage()
                        } else {
                            this.$message.error(msg)
                            Loading.close()
                        }
                    }).finally(() => {
                        Loading.close()
                    })
                }
            })
        },
        // 一键恢复 
        recoveryData() {
            this.$refs.xTable.setAllCheckboxRow(false)
            this.tableData.forEach(item => {
                this.$set(item, 'amount', '')
            })
            this.autoAllocation()
        },
        // 收集核销金额
        collectWriteOffAmount() {
            this.writeOffAmount = 0.00
            let sumWriteOffAmount = 0.00
            // 自动核销单上线后删除
            const selectRows = this.$refs.xTable.getCheckboxRecords()
            if (selectRows.length === 0) {
                this.writeOffAmount = '0.00'
            } else {
                selectRows.forEach(item => {
                    // 发票待核销金额小于0时，自动填充（自动核销单上线后删除）
                    // if (Number(item.waitWriteOffAmount) < 0) {
                    //     item.amount = item.waitWriteOffAmount
                    // }
                    if (item.amount) {
                        sumWriteOffAmount += Number.isNaN(Number(item.amount)) ? 0.00 : Number(item.amount)
                    }
                })
                this.writeOffAmount = sumWriteOffAmount.toFixed(2)
            }
        },
        // 自动分摊
        autoAllocation() {
            let allocationAmount = this.amountForm.withdrawAmountNow
            this.$refs.xTable.setAllCheckboxRow(false)
            this.tableData.forEach(item => {
                this.$set(item, 'amount', '')
                if (Number(allocationAmount) > 0) {
                    if (Number(item.waitWriteOffAmount) > 0) {
                        if (Number(allocationAmount) >= Number(item.waitWriteOffAmount)) {
                            this.$set(item, 'amount', item.waitWriteOffAmount.toFixed(2))
                            allocationAmount -= item.waitWriteOffAmount
                            this.$refs.xTable.setCheckboxRow(item, true)
                        } else {
                            this.$set(item, 'amount', Number(allocationAmount).toFixed(2))
                            this.$refs.xTable.setCheckboxRow(item, true)
                            allocationAmount = 0
                        }
                    }
                }
            })
            this.collectWriteOffAmount()
        },
        //神农订单关联勾选状态  （自动核销单上线后删除）
        changeLinkOrder(row, checked) {
            if (row.erpInvoiceNo) {
                this.tableData.forEach(item => {
                    if (item.erpInvoiceNo === row.erpInvoiceNo) {
                        this.$refs.xTable.setCheckboxRow(item, checked)
                        if (!checked) {
                            this.$set(item, 'amount', '')
                        }
                    }
                }
                )
            }
        },
        selectAllEvent({ checked }) {
            this.collectWriteOffAmount()
            this.tableData.forEach(item => {
                if (!checked) {
                    this.$set(item, 'amount', '')
                }
            })
        },
        // 选择行
        selectChangeEvent({ row, checked }) {
            this.collectWriteOffAmount()
            if (!checked) {
                this.$set(row, 'amount', '')
            }
        },
        // 核销金额输入框
        writeOffAmountChange(row) {
            if (Number(row.waitWriteOffAmount) > 0) {
                if (Number(row.amount) <= 0) {
                    this.$set(row, 'amount', '')
                } else {
                    if (Number(row.amount) > Number(row.waitWriteOffAmount)) {
                        this.$set(row, 'amount', '')
                    }
                }
            } else {
                if (Number(row.amount) >= 0) {
                    this.$set(row, 'amount', '')
                } else {
                    if (Number(row.amount) < Number(row.waitWriteOffAmount)) {
                        this.$set(row, 'amount', '')
                    }
                }
            }
            if (Number.isNaN(Number(row.amount))) {
                this.$set(row, 'amount', '')
            }
            this.collectWriteOffAmount()
        },
        // 核销金额输入框
        writeOffAmountInput(value, row) {
            // 规范输入：移除无效字符并限制小数点位数
            let newValue = value.replace(/[^-\d.]/g, ''); // 删除非数字/负号/小数点字符
            const decimalRegex = /^-?\d*(\.\d{0,2})?$/;

            // 如果存在多个小数点或不满足小数位数，进行修正
            if (!decimalRegex.test(newValue)) {
                newValue = newValue
                    .split('.') // 以小数点分割
                    .slice(0, 2) // 保留整数和一个小数部分
                    .join('.'); // 重新组合
                newValue = newValue.replace(/(\.\d{2})\d+/, '$1'); // 限制小数点后2位
            }

            // 更新值
            this.$set(row, 'amount', newValue);
        },

        // 提现金额输入框
        withdrawAmountNowChange(value) {
            if (value > this.WithdrawableAmount) {
                this.$message.warning('提现金额不能大于可提现金额')
                this.$set(this.amountForm, 'withdrawAmountNow', '')
                return
            }
            if (Number(value) == 0) {
                this.$message.warning('提现金额不能为0')
                this.$set(this.amountForm, 'withdrawAmountNow', '')
                return
            }
            this.autoAllocation()
        },
        // 提现金额输入框
        withdrawAmountNowInput(value) {
            // 先将中文句号转换为英文句点
            value = value.replace(/。/g, '.');
            // 过滤掉所有非数字和非英文句点的字符
            const filteredValue = value.replace(/[^0-9.]/g, '');
            const reg = /^\d*(\.?\d{0,2})$/;
            if (reg.test(filteredValue)) {
                this.$set(this.amountForm, 'withdrawAmountNow', filteredValue);
            } else {
                // 去掉最新输入的无效字符
                this.$set(this.amountForm, 'withdrawAmountNow', filteredValue.slice(0, -1));
            }
        },

        // 获取提现申请单汇总信息
        apiGetWithdrawalHistorySummary() {
            getWithdrawalHistorySummary().then(res => {
                if (res.code == 200) {
                    this.balanceAccount = res.data.platformBalance
                    this.verificationAmount = res.data.totalNoMeltAmount
                    this.WithdrawableAmount = res.data.canWithdrawAmount
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 全部提现
        submitAmount() {
            this.$set(this.amountForm, 'withdrawAmountNow', this.WithdrawableAmount)
            this.autoAllocation()
        },
        // 返回
        backToPage() {
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
        },
    },
}
</script>

<style lang="scss" scoped>
#total {
    display: flex;
    flex-direction: row;
    justify-content: start;

    #total_content {
        align-items: center;
        margin: 0 23px 0 23px;
        display: flex;
        flex-direction: column;

        #total_aomunt {
            font-size: 23px;
            font-weight: bolder;
        }
    }
}

#warning_tips {
    background-color: #FFF4E3;
    margin-bottom: 12px;

    #warning_tips_icon {
        color: #EA9D29;
        margin-left: 12px;
        margin-right: 12px;
    }
}

#withdrawal_amount {
    // background-color: #F5F5F5;

    #withdrawal_amount_content {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        #withdrawal_amount_content_left {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            margin: 12px;

            #withdrawal_amount_content_left_taxt {
                font-weight: bolder;
                margin-right: 12px;
            }

            #withdrawal_amount_content_left_aomunt {
                margin-right: 12px;
            }
        }

        #withdrawal_amount_content_right {
            margin: 12px;
            width: 60%;

            #withdrawal_amount_content_right_taxt {
                font-weight: bolder;
                // font-size: 20px;
                margin-bottom: 12px;

                #withdrawal_amount_content_right_button {
                    margin-left: 23px;
                    font-size: 14px;
                    font-weight: normal;
                }
            }

            #withdrawal_amount_content_right_warning_tips {
                font-size: 14px;
                font-weight: normal;
                color: #666666;
            }
        }
    }
}
</style>