export function inventoryCol() {
  return [
    { field: "erpProductCode", title: "商品编码", width: 230,},
    // { field: "csuid", title: "csuid", width: 150,},
    // { field: "productCode", title: "erp商品编码", width: 150,},
    // { field: "standardProductId", title: "标准库id", width: 150,},
    { field: "nameAnd", title: "商品名称/规格/生产厂家", width: 220,
      slots: { default: "nameAnd" },
    },
    { field: "supplyCode", title: "供货单号", width: 160,},
    { field: "stockOrderNo", title: "入库单号", width: 160,},
    { field: "businessOutCode", title: "本公司出库单号", width: 150,},
    { field: "batchNumAnd", title: "批号/有效期至", width: 150,
      slots: { default: "batchNumAnd" },
    },
    { field: "intoPrice", title: "供货价(元)", width: 100,},
    { field: "surplusAmount", title: "在库库存", width: 100,},
    { field: "soldAmount", title: "已售库存", width: 100,},
    { field: "intoTimeAnd", title: "入库日期/在库天数", width: 180,
      slots: { default: "intoTimeAnd" },
    },

  ]
}
