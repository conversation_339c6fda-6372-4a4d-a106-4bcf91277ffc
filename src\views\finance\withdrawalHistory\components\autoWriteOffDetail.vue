<template>
    <div class="app-container">
        <xyy-panel :titleShow="false">
            <div slot="tools">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; ">
                    <div class="info-box">
                        <div class="info-box-content">
                            <div>
                                <span class="info-box-text">提现申请单号：{{ infos.withdrawNo }}</span>
                            </div>
                            <div>
                                <span class="info-box-text">生成时间：{{ infos.applyDateStr }}</span>
                            </div>
                        </div>
                        <div class="info-box-content">
                            <div>
                                <span class="info-box-text">本次正数核销金额：￥{{ infos.withdrawalPositiveAmount }}</span>
                            </div>
                            <div>
                                <span class="info-box-text">本次负数核销金额：￥{{ infos.withdrawalNegativeAmount }}</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <el-button v-hasPermi="['finance:autoWriteOffDetail:back']" @click="backToPage">返回</el-button>
                    </div>
                </div>
            </div>
        </xyy-panel>
        <xyy-panel title="正数发票">
            <div class="table-box">
                <vxe-table ref="xTable" :loading="xloading" highlight-current-row height="auto" :data="positiveData"
                    :key="xtableKey" empty-text="暂无正数发票">
                    <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                        :title="item.title" :width="item.width">
                    </vxe-table-column>
                </vxe-table>
            </div>
        </xyy-panel>
        <xyy-panel title="负数发票">
            <div class="table-box">
                <vxe-table ref="yTable" :loading="yloading" highlight-current-row height="auto" :data="negativeData"
                    :key="ytableKey" empty-text="暂无负数数发票">
                    <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                        :title="item.title" :width="item.width">
                    </vxe-table-column>
                </vxe-table>
            </div>
        </xyy-panel>
    </div>
</template>

<script>
import { getAutoWriteOffDetail, getWithdrawalHistoryDetailByOrderNo } from '@/api/finance/withdrawalHistory'
import { autoWriteOffDetailColumns } from '../config'
export default {
    data() {
        return {
            infos: {
                withdrawNo: '',
                applyDateStr: '',
                withdrawalPositiveAmount: 0,
                withdrawalNegativeAmount: 0,
            },
            positiveData: [],
            negativeData: [],
            tableColumns: autoWriteOffDetailColumns(),
            xloading: false,
            yloading: false,
            xtableKey: 0,
            ytableKey: 0,
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.apiGetWithdrawalHistoryDetailByOrderNo()
            this.getList()
        })
    },
    methods: {
        // 获取详情
        apiGetWithdrawalHistoryDetailByOrderNo() {
            const params = {
                withdrawNo: this.$route.query.withdrawNo,
            }
            getWithdrawalHistoryDetailByOrderNo(params).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.infos = data
                } else {
                    this.$message.error(msg)
                }
            })
        },
        // 获取列表数据
        getList() {
            this.xloading = true
            this.yloading = true
            const params = {
                withdrawNo: this.$route.query.withdrawNo,
                pageNum: 1,
                pageSize: 10000,
            }
            getAutoWriteOffDetail(params).then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.infos.withdrawalPositiveAmount = data.positiveWriteOffAmount || 0
                    this.infos.withdrawalNegativeAmount = data.negativeWriteOffAmount || 0
                    this.positiveData = data.positiveDetail || []
                    this.negativeData = data.negativeDetail || []
                    this.xloading = false
                    this.yloading = false
                    this.xtableKey++
                    this.ytableKey++
                } else {
                    this.$message.error(msg)
                    this.xloading = false
                    this.yloading = false
                }
            })
        },
        //返回
        backToPage() {
            this.$store.dispatch('tagsView/delView', this.$route)
            this.$router.go(-1)
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-table--body-wrapper {
    height: 230px !important;
}

.info-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;

    .info-box-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .info-box-text {
            margin: 0 23px 0 23px;
            font-size: 14px;
            font-weight: 400;
            color: #333;
        }
    }
}
</style>