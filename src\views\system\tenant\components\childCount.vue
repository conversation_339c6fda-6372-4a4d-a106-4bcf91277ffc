<template>
  <div>
    <div>
      {{ title === 0 ? '账户' : '子账户' + title }}
    </div>
      <el-form ref="form" :model="data" :rules="rules" label-width="80px">
        <el-form-item label="联系人" prop="contactName">
          <el-input v-model="data.contactName" placeholder="请输入联系人" style="width: 80%;" :disabled="data.disabled"/>
        </el-form-item>
        <el-form-item label="联系手机" prop="contactMobile">
          <el-input v-model="data.contactMobile"  placeholder="请输入内容" style="width: 80%;" :disabled="data.disabled"/>
          <span class="clickWriting" @click="resetPwd" style="float: right;">重置密码</span>
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="!data.disabled">
          <el-radio-group v-model="data.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>

</template>
<script>
export default {
  name: "ChildCount",
  dicts: ['sys_normal_disable'],
  props: {
    title: {
      type: Number,
      default: ""
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {},
      rules: {
        contactName: [
          { required: true, message: "联系人不能为空", trigger: "blur" }
        ],
        contactMobile: [
          { required: true, message: "联系手机号不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "子账号状态不能为空", trigger: "blur" }
        ],

      }
    };
  },
  methods: {
    resetPwd(){
      this.$emit('resetPwd', this.data.contactMobile, this.data.contactName);
    },
    validate() {
      return new Promise((resolve) => {
        this.$refs.form.validate((valid) => {
          resolve(valid);
        });
      });
    },
  },
};
</script>
<style scoped lang="scss">
</style>
