<template>
    <div class="app-container">
        <xyy-panel title="查询条件">
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnListTop" />
            <el-form ref="form" :model="formData" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="结算单号">
                            <el-input v-model="formData.settleNo" placeholder="请输入结算单号" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="销售单号">
                            <el-input v-model="formData.orderNo" placeholder="请输入销售出库单/销售退货单" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="12" :md="12">
                        <el-form-item label="时间">
                            <el-date-picker v-model="formData.SubmissionTime" value-format="yyyy-MM-dd" type="daterange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                :picker-options="pickerOptions" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel :titleShow="false">
            <div slot="tools" style="position: relative;">
                <div>
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane name="invoicesList">
                            <div slot="label">
                                <span>结算单列表</span>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane name="invoicesDetail">
                            <div slot="label">
                                <span>结算单明细</span>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <!-- 按钮组 start -->
                <btn-group slot="tools" :btn-list="btnListTable" style="position: absolute; right: 0; top: 0; " />
            </div>
            <div class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData"
                    :key="tableKey" empty-text="暂无结算单数据"
                    :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize, }">
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template>
                        <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                            :title="item.title" :width="item.width" :fixed="item.fixed">
                            <template v-slot:default="slotProps">
                                <div v-if="item.field == 'commissionRate'">
                                    <span>{{ Number(slotProps.row[item.field]) * 100 }}%</span>
                                </div>
                                <div v-else-if="item.field == 'operation'">
                                    <span @click="detailhandler(slotProps.row)" class="clickWriting">详情</span>
                                </div>
                                <div v-else>
                                    <span>{{ slotProps.row[item.field] }}</span>
                                </div>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <div :id="{ 'botton_box': activeName === 'invoicesList' }">
                <div v-if="activeName === 'invoicesList'">
                    <div id="total">
                        <div id="total_content">
                            总销售金额（含税）:
                            <span>{{ `￥${totalSaleAmount} ` }}</span>
                        </div>
                    </div>
                </div>
                <div class="pager">
                    <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize"
                        :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                            'PrevPage',
                            'JumpNumber',
                            'NextPage',
                            'FullJump',
                            'Sizes',
                            'Total',
                        ]" @page-change="handlePageChange" />
                </div>
            </div>
        </xyy-panel>
    </div>
</template>

<script>
import { pageConfig } from "@/utils";
import { getInvoicesList, getTotalSaleAmount, getInvoicesDetailTabList } from '@/api/finance/invoices'
import { tableColumns, detailColumns } from './config'
import XEUtils from 'xe-utils'
import { exportFile } from '@/api/system/exportCenter';
const end = new Date();
const start = XEUtils.getWhatDay(end, -30); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd'); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd'); // 将结束日期格式化为字符串
export default {
    name: 'Invoices',
    data() {
        return {
            btnListTop: [
                {
                    label: "查询",
                    type: "primary",
                    clickEvent: this.searchList,
                    plain: 'false',
                    permission: "finance:invoices:query"
                },
                {
                    label: "导出",
                    type: "warning",
                    clickEvent: this.exportTable,
                    plain: 'false',
                    permission: "finance:invoices:export"
                },
            ],
            formData: {
                settleNo: '',
                SubmissionTime: [defaultBeginTime, defaultEndTime],
                orderNo: '',
            },
            loading: false,
            tableKey: 0,
            // 时间限制
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            tableColumns: tableColumns(),
            tableData: [],
            totalSaleAmount: 0,
            tablePage: pageConfig(),
            activeName: 'invoicesList',
        }
    },
    mounted() {

    },
    activated() {
        this.searchList()
    },
    methods: {
        handleClick() {
            if (this.activeName === 'invoicesList') {
                this.tableColumns = tableColumns()
                this.searchList()
            } else {
                this.tableColumns = detailColumns()
                this.searchList()
            }
        },
        //导出
        exportTable() {
            if (this.tableData.length === 0) {
                this.$message.warning('暂无数据可导出')
                return
            }
            const colName = this.tableColumns.filter(item => item.field !== 'operation').map(item => item.field).join(',');
            const colNameDesc = this.tableColumns.filter(item => item.field !== 'operation').map(item => item.title).join(',');
            const formInfo = {
                ...this.formData,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
            }
            formInfo.startDate = this.formData.SubmissionTime ? this.formData.SubmissionTime[0] + ' 00:00:00' : ''
            formInfo.endDate = this.formData.SubmissionTime ? this.formData.SubmissionTime[1] + ' 23:59:59' : ''
            delete formInfo.SubmissionTime
            let params = {}
            if (this.activeName === 'invoicesList') {
                params = {
                    taskBean: 'FinanceSettleService_pageQuery',
                    colNameDesc: colNameDesc,
                    colName: colName,
                    moduleName: 'FINANCE',
                    menuDesc: '结算单',
                    exportParams: JSON.stringify(formInfo)
                }
            } else {
                params = {
                    taskBean: 'FinanceSettleRemote.pageQueryDetail',
                    colNameDesc: colNameDesc,
                    colName: colName,
                    moduleName: 'FINANCE',
                    menuDesc: '结算单明细',
                    exportParams: JSON.stringify(formInfo)
                }
            }
            this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportFile(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        },
        //获取总销售金额（含税）
        apiGetTotalSaleAmount() {
            getTotalSaleAmount().then(res => {
                const { code, msg, data } = res
                if (code === 200) {
                    this.totalSaleAmount = data || 0
                } else {
                    this.$message.error(msg)
                }
            })
        },
        //切页方法
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNum = currentPage;
            this.tablePage.pageSize = pageSize;
            this.getList();
        },
        // 获取列表数据
        getList() {
            this.loading = true;
            const params = {
                ...this.formData,
                pageNum: this.tablePage.pageNum,
                pageSize: this.tablePage.pageSize,
            }
            params.startDate = this.formData.SubmissionTime ? this.formData.SubmissionTime[0] : ''
            params.endDate = this.formData.SubmissionTime ? this.formData.SubmissionTime[1] : ''
            delete params.SubmissionTime
            if (this.activeName === 'invoicesList') {
                getInvoicesList(params).then(res => {
                    const { code, msg, data } = res
                    if (code === 200) {
                        this.tableData = data.list
                        this.tablePage.total = data.total
                        this.loading = false
                        this.tableKey = this.tableKey + 1
                    } else {
                        this.$message.error(msg)
                        this.loading = false
                    }
                })
                this.apiGetTotalSaleAmount()
            } else {
                getInvoicesDetailTabList(params).then(res => {
                    const { code, msg, data } = res
                    if (code === 200) {
                        this.tableData = data.list
                        this.tablePage.total = data.total
                        this.loading = false
                        this.tableKey = this.tableKey + 1
                    } else {
                        this.$message.error(msg)
                        this.loading = false
                    }
                })
            }
        },
        //查询列表
        searchList() {
            this.tablePage.pageNum = 1;
            this.getList()
        },
        detailhandler(row) {
            this.$router.push({
                path: '/finance/InvoicesDetail',
                query: { settleNo: row.settleNo }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
#botton_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#total {
    display: flex;
    flex-direction: row;
    justify-content: start;

    #total_content {
        align-items: center;
        font-weight: bolder;
        font-size: 23px;
        margin: 12px;
    }
}
</style>