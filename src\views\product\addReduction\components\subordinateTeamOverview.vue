<template>
    <div class="my-dialog">
        <xyy-dialog
        title="控销商圈设置"
        ref="subordinateTeamOverview"
        width="90%"
        @on-close="handleDialogClose"
        >
        <xyy-panel :titleShow="false">
            <el-form
              :model="ruleForm"
              ref="ruleForm"
              label-width="130px"
              class="demo-ruleForm"
              >
                  <el-row>
                      <el-col :span="24" >
                          <el-form-item label="业务商圈:">
                          <el-button  plain @click="selectBusinessCircleClick">选择商圈</el-button>
                          <div class="div-info">
                              <span v-if="ruleForm.busAreaConfigName">当前已选：{{ruleForm.busAreaConfigName}}</span>
                              <span style="margin-left:5px;" v-if="ruleForm.busAreaConfigName"  class="clickWriting" @click="checkDetails">查看详情</span>
                          </div>
                          </el-form-item>
                      </el-col>
                  </el-row>
                  <el-row>
                  <el-col>
                      <el-form-item label="供货对象:" prop="controlUserTypes" >
                      <div class="customerType">
                          <el-checkbox
                          :indeterminate="isIndeterminate"
                          v-model="checkAll"
                          :disabled="disabled"
                          @change="handleCheckAllChange"
                          class="checkedall"
                          >全选</el-checkbox>
                          <el-checkbox-group
                          v-model="ruleForm.controlUserTypes"
                          @change="handleCheckedTypesChange"
                          >
                          <el-checkbox
                              v-for="(item,index) in customerTypes"
                              :label="item.id"
                              :disabled="disabled"
                              style="marginBottom:10px;"
                              :key="index"
                          >{{item.name}}</el-checkbox>
                          </el-checkbox-group>
                      </div>
                      <div style='color: red;font-size: 12px;'>
                          注意：被勾选的客户类型对应的客户可购买当前商品。未勾选的客户类型对应的客户不可购买当前商品。
                      </div>
                      </el-form-item>
                  </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="黑白名单：" prop="rosterType">
                        <div class="black-white-list">
                          <div>
                            <el-radio-group v-model="ruleForm.rosterType" class="radio-group">
                              <el-radio :label= 1 @click.native.prevent="rosterTypeClick(1)">黑名单控销组</el-radio>
                              <el-radio :label= 2 @click.native.prevent="rosterTypeClick(2)">白名单控销组</el-radio>
                            </el-radio-group>
                          </div>
                          <div>
                            <el-button size="mini" @click="openBlackWhiteList">选择控销组</el-button>
                            <span style="margin-left: 23px;">{{ controlGroup.groupName }}</span>
                          </div>
                        </div>
                        <div style="color: red; font-size: 12px;">
                          <div>
                            商品维度可见性分为三种场景：
                          </div>
                          <div>
                            场景1：未选择黑白名单控销组。则仅商圈区域内指定客户类型可购买此商品。
                          </div>
                          <div>
                            场景2：选择了黑名单控销组。则仅商圈区域内指定客户类型且非黑名单客户可购买此商品。
                          </div>
                          <div>
                            场景3：选择了白名单控销组。则仅白名单的客户可购买此商品。
                          </div>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
              </el-form>
        </xyy-panel>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancelDialog" size="small">取 消</el-button>
                <el-button type="primary" class="xyy-blue" @click="determineDialog" size="small">确定</el-button>
            </div>
            <!-- 选择商圈 -->
            <select-business-circle ref="selectBusinessCircleDiaglog" 
            :selected.sync="busAreaId" :saleType.sync="saleType" 
            :row="businessRow" 
            @onDialogChange="onDialogChange" 
            @close="close"/>
            <!-- 查看业务商圈 -->
            <business-circle-detail-dialog
            :row="selectViewRow"
            ref="viewBusinessDialog"
            />
            <!-- 选择控销组 -->
            <choose-control-group ref="chooseControlGroup"  @chooseControlGroup="chooseControlGroup" />
        </xyy-dialog>
    </div>
</template>
<script>
import { queryProductControlledStore, updateProductControlledStore } from '@/api/product/controlledStoreManagement';
import { queryList,bindBusArea} from '@/api/product/businessManagement';
import selectBusinessCircle from "./selectBusinessCircle.vue"; //选择商圈弹窗
import {getUserTypeList} from '@/api/product/addReduction';
import BusinessCircleDetailDialog from '../../businessManagement/components/businessCircleDetailDialog.vue';
import chooseControlGroup from './chooseControlGroup.vue';
export default {
  name: 'subordinateTeamOverview',
  components: {
    selectBusinessCircle,
    BusinessCircleDetailDialog,
    chooseControlGroup
  },
  data() {
    return {
        busAreaId:undefined,//带入到选择商圈
        saleType:undefined,//带入选择商圈
        businessRow:undefined,//当前行数据带入选择商圈
        selectViewRow:undefined,//查看详情
        innerSelected:'',
        innerSaleType:'',
        disabled:false,
        checkAll:false,
        ruleForm:{
            busAreaConfigName:'',//已选商圈名称
            controlUserTypes:[],//供货对象
            busAreaId:null,//已选商圈id
            rosterType: '', // 黑白名单控销组
        },
        customerTypes: [],
        isIndeterminate: false,
        controlGroup: {}, // 已选控销组
    }
  },
  props: {
    row: Object,
  },
  watch: {
  },
  mounted() {

  },
  methods: {
    rosterTypeClick(val){
      this.$set(this.ruleForm,'rosterType', this.ruleForm.rosterType === val ? '' : val);
    },
    getControlGroup(){
      const params = new URLSearchParams();
      params.append('csuId', this.row.csuid)
      queryProductControlledStore(params).then(res => {
        if(res.code === 0){
          this.controlGroup = res.result;
          this.$set(this.ruleForm,'rosterType', res.result.rosterType ? res.result.rosterType : '');
        }else {
          this.$message.error(res.msg);
        }
      })
    },
    openBlackWhiteList(){
      this.$refs.chooseControlGroup.open();
    },
    chooseControlGroup(data){
      this.controlGroup.groupName = data.name;
      this.controlGroup.groupId = data.id;
    },
    close(){
        this.busAreaId = undefined;//带入到选择商圈
        this.saleType = undefined;//带入选择商圈
        this.businessRow = undefined;//当前行数据带入选择商圈
    },
    onDialogChange(businessArea,businessAreaName){
        this.ruleForm.busAreaConfigName = businessAreaName;
        this.ruleForm.busAreaId = businessArea;
    },
    /**单选供货对象 */
    handleCheckedTypesChange(value){
        this.checkAll = value.length == this.customerTypes.length;
        this.isIndeterminate = value.length > 0 && value.length < this.customerTypes.length;
    },
    /**全选供货对象 */
    handleCheckAllChange(val){
        const checkAllId = this.customerTypes.map((item => item.id));
        this.ruleForm.controlUserTypes = val ? checkAllId : [];
        this.isIndeterminate = false;
    },
    /**查看详情 */
    checkDetails(){
        this.selectViewRow = {id:this.ruleForm.busAreaId};
        this.$refs.viewBusinessDialog.open();
    },
    /**选择商圈 */
    selectBusinessCircleClick() {
        this.busAreaId = this.row.businessArea;
        this.saleType = this.row.saleType;
        this.businessRow = this.row;
        this.$refs.selectBusinessCircleDiaglog.open();
    },
    /**打开弹窗 */
    open() {
        this.$nextTick(()=>{
            this.getUserTypeListNew();
            this.getControlGroup();
            this.$refs.subordinateTeamOverview.open();
            
        })
    },
    /**查询供货对象 */
    async getUserTypeListNew(){
        try{
            const res = await getUserTypeList()
            if(res.code == 0){
                this.customerTypes = res.result;
                this.ruleForm.busAreaConfigName = this.row.businessAreaName;
                this.ruleForm.busAreaId = this.row.businessArea;
                if(this.row.userTypeList && this.row.userTypeList.length > 0){
                    this.row.userTypeList.forEach(item => {
                        this.customerTypes.forEach(it=>{
                            if(it.id == item){
                                this.ruleForm.controlUserTypes.push(item);
                            }
                        })
                    });
                    this.handleCheckedTypesChange(this.ruleForm.controlUserTypes)
                }
            }else{
                this.$message({
                    message: res.msg,
                    type: 'error',
                });
            }
        }catch(error){

        }
    },
    handleDialogClose() {
        this.busAreaId = undefined;//带入到选择商圈
        this.saleType = undefined;//带入选择商圈
        this.businessRow = undefined;//当前行数据带入选择商圈
        this.selectViewRow = undefined;//查看详情
        this.innerSelected = '';
        this.innerSaleType = '';
        this.disabled = false;
        this.checkAll = false;
        this.ruleForm = {
            busAreaConfigName:'',//已选商圈名称
            controlUserTypes:[],//供货对象
            busAreaId:null//已选商圈id
        },
        this.customerTypes = [];
        this.isIndeterminate = false;
        this.$emit('onDialogChange')
    },
    // 关闭弹窗
    cancelDialog(val) {
      this.$refs.subordinateTeamOverview.close();
    },
    async determineDialog() {
      let params = {
        barcode:this.row.barcode,
        busId:this.ruleForm.busAreaId,
        userType:this.ruleForm.controlUserTypes.join(',')
      }
      let controlParams = {
        groupName: this.controlGroup.groupName,
        groupId: this.controlGroup.groupId,
        rosterType: this.ruleForm.rosterType,
        csuId: this.row.csuid,
      }
      if(!controlParams.rosterType){
        controlParams.groupId = -1
      }else {
        if(!controlParams.groupId){
          this.$message.warning('请选择控销组');
          return;
        }
      }
      try{
        const controlRes = await updateProductControlledStore(controlParams);
        const res = await bindBusArea(params);
        if(controlRes.code != 0){
          this.$message.error(controlRes.msg);
          return;
        }
        if(res.code == 0){
          this.$message({
            message: res.msg,
            type: 'success'
          });
          this.$emit('onDialogChange')
          this.cancelDialog();
        }else{
          this.$message.error(res.msg)
        }
      }catch(error){

      }

    },
    // selectChange($event, row) {
    //   if(row.typeName == '药品和非药' || this.innerSaleType===row.type || this.fromComp === 'groupActivityTheme' || this.fromComp === 'batchEdit'){
    //     this.innerSelected = row.id;
    //     this.selectItem = row;
    //   }else{
    //     setTimeout(() => {
    //       this.$message.error('商圈类型不匹配');
    //     }, 0);
    //   }
    // },
    // 时间格式化
    formatDate(row, column, cellValue) {
      const date = new Date(cellValue)
      const y = date.getFullYear()
      let MM = date.getMonth() + 1
      MM = MM < 10 ? `0${MM}` : MM
      let d = date.getDate()
      d = d < 10 ? `0${d}` : d
      let h = date.getHours()
      h = h < 10 ? `0${h}` : h
      let m = date.getMinutes()
      m = m < 10 ? `0${m}` : m
      let s = date.getSeconds()
      s = s < 10 ? `0${s}` : s
      return `${y}-${MM}-${d} ${h}:${m}:${s}`
    },
  }
}
</script>
<style lang="scss" scoped>
.my-dialog {
  min-width: 400px;
}
::v-deep.el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
::v-deep.el-input__inner {
  border-radius: 0 4px 4px 0;
}
::v-deep.el-form-item__label {
  padding: 0;
}
::v-deep.el-input__inner {
  border-radius: 0;
}
::v-deep.el-dialog__body {
  padding: 0 20px;
}

::v-deep.el-dialog__wrapper .el-dialog__header {
  background: #f9f9f9;
}

::v-deep.el-dialog__wrapper .el-dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

::v-deep.el-divider--horizontal {
  width: 104%;
  margin: 0 0 0 -20px;
}
::v-deep.el-pagination__sizes .el-select {
  top: 0;
  left: 0;
}

.black-white-list{
  display: flex;
  align-items: start;
}

::v-deep .radio-group{
  display: flex;
  flex-direction: column;
  align-items: start;

  .el-radio{
    margin-top: 10px;
  }
}

::v-deep .el-radio__input.is-checked .el-radio__inner {
    background-color: #39B950;
    border-color: #39B950;
}

::v-deep .el-radio__input.is-checked+.el-radio__label {
    color: #39B950;
}
</style>

    