<template>
  <div>
    <xyy-dialog
      ref="refdialog"
      :width="dialogWidth"
      title="查看商品"
      @on-close="onClose"
    >
      <xyy-panel :titleShow='false'>
        <el-form :model="formData" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="商品ERP编码">
                <el-input placeholder="请输入商品ERP编码" v-model="formData.erpProductCode">
                  <!-- <template slot="prepend"></template> -->
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="商品名称">
                <el-input placeholder="请输入商品名称" v-model="formData.productName">
                  <!-- <template slot="prepend">商品名称</template> -->
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" >
              <el-form-item style="float: right">
                <el-button type="primary" @click="searchList">查询</el-button>
                <el-button @click="resetFields">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </xyy-panel>
      <xyy-panel :titleShow="false">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          stripe
          border
          style="width: 100%"
          height="300px"
          highlight-current-row
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
        >
          <el-table-column prop="erpProductCode" label="商品ERP编号" />
          <el-table-column prop="productName" label="商品名称" />
          <el-table-column prop="approvalNumber" label="批准文号" />
          <el-table-column prop="spec" label="规格" />
          <el-table-column prop="statusName" label="状态" />
          <el-table-column prop="status" label="状态" v-if="show === true" />
        </el-table>
          <div class="pager" >
              <vxe-pager
                  border
                  :current-page="tablePage.pageNum"
                  :page-size="tablePage.pageSize"
                  :total="tablePage.total"
                  :page-sizes="[10,20,50,100]"
                  :layouts="[
                  'PrevPage',
                  'JumpNumber',
                  'NextPage',
                  'FullJump',
                  'Sizes',
                  'Total',
                  ]"
                  @page-change="handlePageChange"
              />
          </div>
      </xyy-panel>
        <span slot='footer' class='dialog-footer'>
            <el-button type='primary' size='small' @click='sureBtn' >确 定</el-button>
        </span>
    </xyy-dialog>
  </div>
</template>

<script>
import {selectProductByBusId}   from '@/api/product/businessManagement'

export default {
  name: 'viewProduct',
  model: {
    prop: 'viewProductDialog',
    event: 'onDialogViewProduct'
  },
  props: {
    viewProductDialog: Boolean,
    row: Object
  },
  mounted() {
  },
  data() {
    return {
      show: false,
      dialogWidth: '75%',
      formData: {
        erpProductCode: '', // 商品ERP编码
        productName: '' // 商品名称
      },
      dialogVisibl: false,
      tableData: [], // 弹窗表格
      rowIndex: '',
      pageSizes: [10, 20, 30, 40],
      tablePage: {
        // 分页
        pageNum: 1,
        pageSize: 10,
        total: 1
      },
      busAreaId: '',
      isLoading: false, // 加载
      ruleForm: {
        tempName: '',
        templateType: '',
        tempStatus: '',
        branchCode: null,
        startTime: ''
      }
    }
  },
  methods: {
    open() {
      this.resetFields()
      this.$refs.refdialog.open()
      this.$nextTick(()=>{
        this.busAreaId = this.row.id; // 业务商圈ID
        this.searchList() // 初始化列表
      })
      
      
    },
    /**关闭弹窗 */
    onClose() {
        this.$refs.refdialog.close()
        this.$emit('on-close')
    },
    // 查询
    async searchList() {
      const params = {
        busId: this.busAreaId
      }
      Object.assign(params, this.formData, this.tablePage)
      delete params.total;
      this.isLoading = true
      try{
        const res = await selectProductByBusId(params);
        if(res.code == 0){
            this.isLoading = false
            this.tableData = res.result.list
            this.tablePage.total = res.result.total // 总数据数量
            this.tablePage.pageNum = res.result.pageNum
        }else{
          this.isLoading = false
          this.$message.error(res.msg)
        }
      }catch(error){
        this.isLoading = false
        console.log(error);
      }
    },
    resetFields() {
      this.formData.erpProductCode = ''
      this.formData.productName = ''
      this.searchList()
    },
    // 分页器改变处理
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNum = currentPage;
      this.tablePage.pageSize = pageSize;
      this.searchList();
    },
    sureBtn() {
      this.$refs.refdialog.close()
    }
  }
}
</script>

<style scoped>
::v-deep.el-dialog__header {
  background-color: #f8f8ff;
}
</style>
