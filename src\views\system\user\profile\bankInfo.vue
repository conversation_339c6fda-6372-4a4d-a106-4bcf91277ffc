<template>
    <div>
        <div style="margin-bottom: 23px;">
            账户{{ acountNo }}
        </div>
        <el-form ref="form" :model="bank" :rules="rules" label-width="80px">
            <el-form-item label="收款人" prop="receiveName">
                <el-input v-model="bank.receiveName" placeholder="收款人" :disabled="editFlag"></el-input>
            </el-form-item>
            <el-form-item label="银行账号" prop="bankAccount">
                <el-input v-model="bank.bankAccount" @input="bankAccountInput" placeholder="银行账号"
                    :disabled="editFlag"></el-input>
            </el-form-item>
            <el-form-item label="开户行" prop="openBank">
                <el-input v-model="bank.openBank" placeholder="开户行" :disabled="editFlag"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button @click="editInfo('edit')">修改</el-button>
                <el-button type="primary" @click="submit('add')">保存</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import { addBankAccount } from '@/api/system/user'
export default {
    props: {
        bank: {
            type: Object,
            default: () => {
                return {
                    name: '',
                    bankAccount: '',
                    bankName: ''
                }
            }
        },
        acountNo: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            rules: {
                receiveName: [
                    { required: true, message: '请输入收款人', trigger: 'blur' }
                ],
                bankAccount: [
                    { required: true, message: '请输入银行账号', trigger: 'blur' }
                ],
                openBank: [
                    { required: true, message: '请输入开户行', trigger: 'blur' }
                ]
            },
            editFlag: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (!this.bank.id) {
                this.editFlag = false
            }
        })
    },
    methods: {
        bankAccountInput(value) {
            // 只保留数字，去除非数字字符
            let newValue = value.replace(/[^\d]/g, '');
            // 更新输入值
            this.$set(this.bank, 'bankAccount', newValue)
        },
        editInfo(type) {
            this.editFlag = false
        },
        submit(type) {
            this.$refs.form.validate(valid => {
                if (valid) {
                    const params = { ...this.bank }
                    addBankAccount(params).then(res => {
                        const { code, msg } = res
                        if (code === 200) {
                            this.$message({
                                type: 'success',
                                message: msg
                            })
                            this.$emit('bankSubmit')
                            this.editFlag = true
                        } else {
                            this.$message({
                                type: 'error',
                                message: msg
                            })
                        }
                    })
                }
            })
        }
    }
}
</script>

<style></style>