<template>
    <div>
        <el-dialog title="添加客户" :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
            <xyy-panel :titleShow='false'>
                <btn-group slot="tools" :btn-list="btnListForm" />
                <el-form ref="form" :model="form" label-width="100px">
                    <el-row :gutter="10">
                        <el-col :lg="12" :md="12">
                            <el-form-item label="药店ID">
                                <el-input @input="buyerIdInput" v-model="form.buyerId" />
                            </el-form-item>
                        </el-col>
                        <el-col :lg="12" :md="12">
                            <el-form-item label="药店名称">
                                <el-input v-model="form.buyerName" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div :key="tabKey" class="table-box">
                    <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto"
                        :max-height="tableHeight" :data="tableData" :key="tableKey" :show-overflow="false">
                        <vxe-table-column type="checkbox" width="50" />
                        <vxe-table-column type="seq" title="序号" width="80" />
                        <template>
                            <vxe-table-column v-for="item in tableColumns" :key="item.field" :field="item.field"
                                :title="item.title" :min-width="item.width" :fixed="item.fixed">
                            </vxe-table-column>
                        </template>
                    </vxe-table>
                </div>
            </xyy-panel>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="addCustomer">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { queryCustomer, addMerchant } from '@/api/product/controlledStoreManagement'
import { addCustomerCol } from '../config.js'
export default {
    name: "addCustomers",
    data() {
        return {
            dialogVisible: false,
            btnListForm: [
                {
                    label: "查询",
                    type: 'primary',
                    clickEvent: this.searchList,
                    code: "",
                    plain: 'false',
                    permission: 'product:ControlGroupEdit:searchList'
                },
                {
                    label: "重置",
                    type: 'info',
                    clickEvent: this.resetForm,
                    code: "",
                    plain: 'false',
                    permission: 'product:ControlGroupEdit:resetForm'
                },
            ],
            form: {
                buyerId: null,
                buyerName: "",
            },
            tabKey: 0,
            tableData: [],
            tableKey: 0,
            loading: false,
            tableHeight: 300,
            tableColumns: addCustomerCol(),
            groupId: null,
        }
    },
    methods: {
        buyerIdInput(value) {
            let newVal = value.trim();
            newVal = newVal.replace(/[^\d]/g, '');
            this.form.buyerId = newVal;
        },
        addCustomer() {
            const selectRows = this.$refs.xTable.getCheckboxRecords();
            if (selectRows.length === 0) {
                this.$message.warning("请选择药店");
                return;
            }
            const loading = this.$loading({
                lock: true,
                text: '正在添加客户...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            const params = {
                groupId: this.groupId,
                merchantIds: selectRows.map(item => item.buyerId)
            }
            addMerchant(params).then(res => {
                const { code, msg } = res
                if (code === 0) {
                    this.$message.success(msg);
                    this.handleClose()
                    this.$emit('add-success')
                } else {
                    this.$message.error(msg)
                }
            }).finally(() => {
                loading.close();
            })
        },
        resetForm() {
            this.form = {
                buyerId: "",
                buyerName: "",
            }
            this.tableData = [];
            this.tableKey++
            // this.searchList()
        },
        searchList() {
            this.loading = true;
            const params = {
                ...this.form
            }
            queryCustomer(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.tableData = result;
                } else {
                    this.$message.error(msg)
                }
            }).finally(() => {
                this.loading = false;
            })
        },
        open(data) {
            this.dialogVisible = true;
            this.form = {
                buyerId: null,
                buyerName: "",
            }
            this.tableData = [];
            this.groupId = data;
        },
        handleClose() {
            this.dialogVisible = false;
        },
    },
}
</script>

<style></style>